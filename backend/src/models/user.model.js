import mongoose from 'mongoose';
import crypto from 'crypto';
import validator from 'validator';
import bcrypt from 'bcryptjs';
import jwt from 'jsonwebtoken';
import 'dotenv/config';

const userSchema = new mongoose.Schema({
  userName: {
    type: String,
    trim: true,
    unique: true,
    lowercase: true,
    required: [true, 'User name filed is required']
  },
  fullName: {
    type: String,
    required: [true, 'Full name filed is required']
  },
  email: {
    type: String,
    unique: true,
    required: [true, 'Email filed is required'],
    validate: [validator.isEmail, 'Please enter a valid email address']
  },
  phone: {
    type: String,
    unique: true,
    validate: [validator.isMobilePhone, 'Please enter a valid phone number']
  },
  password: {
    type: String,
    required: [true, 'Password filed is required'],
    minlength: [6, 'Password must be at least 6 characters'],
    select: false
  },
  avatar: {
    type: String
  },
  gender: {
    type: String,
    enum: ['male', 'female']
  },
  dob: {
    type: Date,
    required: [validator.isDate, 'Date of birth filed is required']
  },
  address: {
    type: String,
    required: [true, 'Address field is required']
  },
  role: {
    type: String,
    enum: ['admin', 'user','staff'],
    default: 'user'
  },
  verified: {
    type: Boolean,
    default: false
  },
  status: {
    type: String,
    enum: ['register', 'login', 'logout', 'blocked'],
    default: 'register'
  },
  resetPasswordToken: String,
  resetPasswordExpire: Date,
  emailVerificationToken: String,
  emailVerificationExpire: Date,
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Replace spaces with dashes in userName before saving
userSchema.pre('save', function(next) {
  if (this.userName) {
    this.userName = this.userName.replace(/\s/g, '-');
  }
  next();
});

// Hash password before saving
userSchema.pre('save', async function(next) {
  if (!this.isModified('password')) {
    next();
  }
  this.password = await bcrypt.hash(this.password, 8);
});

// JWT Access Token
userSchema.methods.getJWTToken = function() {
  // Default to 1 day if not set or invalid
  let expiresIn = '1d';
  
  try {
    if (process.env.JWT_ACCESS_TOKEN_EXPIRES) {
      // Validate the format (e.g., "1d", "2h", "30m")
      const isValid = /^\d+[dhms]$/.test(process.env.JWT_ACCESS_TOKEN_EXPIRES);
      if (isValid) {
        expiresIn = process.env.JWT_ACCESS_TOKEN_EXPIRES;
      }
    }
  } catch (error) {
    console.error('Invalid JWT_ACCESS_TOKEN_EXPIRES format, using default:', error);
  }
  
  return jwt.sign({ id: this._id }, process.env.JWT_SECRET_KEY, { expiresIn });
};

// JWT Refresh Token
userSchema.methods.getJWTRefreshToken = function() {
  // Default to 7 days if not set or invalid
  let expiresIn = '7d';
  
  try {
    if (process.env.JWT_REFRESH_TOKEN_EXPIRES) {
      // Validate the format (e.g., "7d", "168h", "10080m")
      const isValid = /^\d+[dhms]$/.test(process.env.JWT_REFRESH_TOKEN_EXPIRES);
      if (isValid) {
        expiresIn = process.env.JWT_REFRESH_TOKEN_EXPIRES;
      }
    }
  } catch (error) {
    console.error('Invalid JWT_REFRESH_TOKEN_EXPIRES format, using default:', error);
  }
  
  return jwt.sign({ id: this._id }, process.env.JWT_REFRESH_TOKEN_SECRET_KEY, { expiresIn });
};

// Compare password
userSchema.methods.comparePassword = async function(password) {
  return bcrypt.compare(password, this.password);
};

// Generate password reset token
userSchema.methods.getResetPasswordToken = function() {
  const resetToken = crypto.randomBytes(20).toString('hex');
  this.resetPasswordToken = crypto
    .createHash('sha256')
    .update(resetToken)
    .digest('hex');
  this.resetPasswordExpire = Date.now() + 15 * 60 * 1000;
  return resetToken;
};

// Generate email verification token
userSchema.methods.getEmailVerificationToken = function() {
  const verificationToken = crypto.randomBytes(20).toString('hex');
  this.emailVerificationToken = crypto
    .createHash('sha256')
    .update(verificationToken)
    .digest('hex');
  this.emailVerificationExpire = Date.now() + 15 * 60 * 1000;
  return verificationToken;
};

// Method to get user's booking history
userSchema.methods.getBookingHistory = async function() {
  return await Booking.find({ user_id: this._id })
    .populate('room_id')
    .populate('room_type')
    .sort({ createdAt: -1 });
};

// Method to get user's favorite room types
userSchema.methods.getFavoriteRoomTypes = async function() {
  const bookings = await Booking.find({ 
    user_id: this._id,
    booking_status: 'completed'
  }).populate('room_id');
  
  // Count bookings by room type
  const roomTypeCounts = {};
  bookings.forEach(booking => {
    if (!booking.room_id || !booking.room_id.room_type) return;
    
    const roomTypeId = booking.room_id.room_type.toString();
    roomTypeCounts[roomTypeId] = (roomTypeCounts[roomTypeId] || 0) + 1;
  });
  
  // Sort room types by booking count
  const sortedRoomTypes = Object.entries(roomTypeCounts)
    .sort((a, b) => b[1] - a[1])
    .map(entry => entry[0]);
  
  return sortedRoomTypes;
};

// Add validation for phone number
userSchema.path('phone').validate(function(value) {
  if (!value) return true; // Allow empty values
  return /^\+?[1-9]\d{1,14}$/.test(value);
}, 'Please enter a valid phone number');

// Add method to check if user is staff
userSchema.methods.isStaff = function() {
  return this.role === 'admin';
};

// Add method to get user's total spending
userSchema.methods.getTotalSpending = async function() {
  const bookings = await Booking.find({
    user_id: this._id,
    booking_status: { $in: ['completed', 'checked-in'] }
  });
  
  return bookings.reduce((total, booking) => total + booking.total_amount, 0);
};

// Add index for faster queries
userSchema.index({ email: 1 });
userSchema.index({ userName: 1 });
userSchema.index({ role: 1 });
userSchema.index({ status: 1 });

const User = mongoose.model('User', userSchema);

export default User;
