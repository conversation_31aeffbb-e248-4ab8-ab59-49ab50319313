/**
 * Checks if any date in an array is before the current date
 * @param {Array<Date>} dates Array of dates to check
 * @returns {Object} Object containing boolean flags for date validation
 */
const bookingDatesBeforeCurrentDate = (dates) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  return {
    isAnyDateInPast: dates.some(date => {
      const bookingDate = new Date(date);
      bookingDate.setHours(0, 0, 0, 0);
      return bookingDate < today;
    }),
    today: today
  };
};

/**
 * Validates if a date string is in a valid format and is a real date
 * @param {string} dateString Date string to validate
 * @returns {boolean} True if date is valid, false otherwise
 */
const isValidDate = (dateString) => {
  const date = new Date(dateString);
  return date instanceof Date && !isNaN(date);
};

/**
 * Formats a date object to YYYY-MM-DD format
 * @param {Date} date Date to format
 * @returns {string} Formatted date string
 */
const formatDate = (date) => {
  return date.toISOString().split('T')[0];
};

/**
 * Gets the date range between two dates
 * @param {Date} startDate Start date
 * @param {Date} endDate End date
 * @returns {Array<Date>} Array of dates between start and end dates
 */
const getDateRange = (startDate, endDate) => {
  const dates = [];
  const currentDate = new Date(startDate);
  currentDate.setHours(0, 0, 0, 0);
  
  const lastDate = new Date(endDate);
  lastDate.setHours(0, 0, 0, 0);

  while (currentDate <= lastDate) {
    dates.push(new Date(currentDate));
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return dates;
};

/**
 * Checks if two date ranges overlap
 * @param {Date} start1 Start date of first range
 * @param {Date} end1 End date of first range
 * @param {Date} start2 Start date of second range
 * @param {Date} end2 End date of second range
 * @returns {boolean} True if ranges overlap, false otherwise
 */
const dateRangesOverlap = (start1, end1, start2, end2) => {
  return start1 <= end2 && start2 <= end1;
};

/**
 * Adds specified number of days to a date
 * @param {Date} date Initial date
 * @param {number} days Number of days to add
 * @returns {Date} New date with added days
 */
const addDays = (date, days) => {
  const result = new Date(date);
  result.setDate(result.getDate() + days);
  return result;
};

/**
 * Gets the difference in days between two dates
 * @param {Date} date1 First date
 * @param {Date} date2 Second date
 * @returns {number} Number of days between dates
 */
const getDaysDifference = (date1, date2) => {
  const diffTime = Math.abs(date2 - date1);
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
};

export { bookingDatesBeforeCurrentDate, isValidDate, formatDate, getDateRange, dateRangesOverlap, addDays, getDaysDifference };
