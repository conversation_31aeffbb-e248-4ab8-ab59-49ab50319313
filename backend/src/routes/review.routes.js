import express from 'express';
import { 
    isAuthenticatedUser, 
    isBlocked 
} from '../middleware/app.authentication.js';
import { 
    roomReviewAdd, 
    getRoomReviewsList, 
    editSelfRoomReview 
} from '../controllers/review.controllers.js';

const router = express.Router();

// route for add user room review
router.route('/room-review-add/:id').post(isAuthenticatedUser, isBlocked, roomReviewAdd);

// route for get a room review list
router.route('/get-room-reviews-list/:room_id').get(getRoomReviewsList);

// route for edit self room review
router.route('/edit-room-review/:review_id').put(isAuthenticatedUser, isBlocked, editSelfRoomReview);

export default router;
