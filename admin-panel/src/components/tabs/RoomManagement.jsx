import React, { useState, useEffect } from 'react';
import { 
  Card, Table, Button, Tag, Space, Drawer, Form, 
  Select, DatePicker, Input, Tabs, Tooltip, Badge,
  Popconfirm, message, Typography, Divider, Timeline,
  Calendar, Dropdown, Menu, Skeleton
} from 'antd';
import { 
  CheckCircleOutlined, CloseCircleOutlined, 
  ToolOutlined, InfoCircleOutlined,
  CalendarOutlined, HistoryOutlined, EditOutlined,
  ExclamationCircleOutlined, SyncOutlined
} from '@ant-design/icons';
import { format } from 'date-fns';
import ApiService from '../../utils/apiService';
import notificationWithIcon from '../../utils/notification';
import RoomStatus from '../rooms/RoomStatus';
import { roomStatusOptions } from '../../utils/responseAsStatus';
import dayjs from 'dayjs';
import useFetchData from '../../hooks/useFetchData';
const { TabPane } = Tabs;
const { TextArea, InputNumber, Checkbox } = Input;
const { Title, Text } = Typography;
import { Empty } from 'antd';

const RoomManagement = () => {
  const [loading, setLoading] = useState(false);
  const [maintenanceModal, setMaintenanceModal] = useState(false);
  const [cleaningModal, setCleaningModal] = useState(false);
  const [statusModal, setStatusModal] = useState(false);
  const [historyModal, setHistoryModal] = useState(false);
  const [calendarModal, setCalendarModal] = useState(false);
  const [selectedRoom, setSelectedRoom] = useState(null);
  const [maintenanceForm] = Form.useForm();
  const [cleaningForm] = Form.useForm();
  const [statusForm] = Form.useForm();
  const [maintenanceHistory, setMaintenanceHistory] = useState([]);
  const [cleaningHistory, setCleaningHistory] = useState([]);
  const [historyType, setHistoryType] = useState('maintenance');
  const [refreshTrigger, setRefreshTrigger] = useState(0);
  const [bookingDates, setBookingDates] = useState([]);
  const [maintenanceDates, setMaintenanceDates] = useState([]);
  const [calendarLoading, setCalendarLoading] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(dayjs());
  const [summary, setSummary] = useState('');
  const [notes, setNotes] = useState('');
  const [comments, setComments] = useState('');
  // Add state for unavailable dates
  const [unavailableDates, setUnavailableDates] = useState([]);

  // Use the useFetchData hook to fetch rooms
  const [fetchLoading, fetchError, response] = useFetchData(
    '/api/v1/all-rooms-list?limit=25',
    refreshTrigger
  );

  // Extract rooms from the response, ensuring it's an array
  const rooms = Array.isArray(response?.data?.rows) 
    ? response?.data?.rows 
    : [];

  const handleRefresh = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  const handleScheduleMaintenance = (room) => {
    setSelectedRoom(room);
    maintenanceForm.resetFields();
    setMaintenanceModal(true);
  };

  const handleMarkCleaning = (room) => {
    setSelectedRoom(room);
    cleaningForm.resetFields();
    setCleaningModal(true);
  };

  const handleUpdateStatus = (room) => {
    setSelectedRoom(room);
    statusForm.setFieldsValue({
      status: room.current_status
    });
    setStatusModal(true);
  };

  const handleViewCalendar = (room) => {
    setSelectedRoom(room);
    setCalendarModal(true);
    // Month is 1-indexed in the API but 0-indexed in dayjs
    fetchRoomAvailability(room._id, dayjs().year(), dayjs().month() + 1);
  };

  const fetchRoomAvailability = async (roomId, year, month) => {
    try {
      setCalendarLoading(true);
      const response = await ApiService.get(`/api/v1/room-booking-calendar/${roomId}?year=${year}&month=${month}`);
      
      if (response?.result_code === 0) {
        const calendar = response.result.data.calendar || [];
        
        // Extract booked and maintenance dates
        const booked = calendar
          .filter(day => day.status === 'booked')
          .map(day => day.date);
          
        const maintenance = calendar
          .filter(day => day.status === 'maintenance')
          .map(day => day.date);
          
        // Also track unavailable dates (inactive, etc.)
        const unavailable = calendar
          .filter(day => !day.available && day.status !== 'booked' && day.status !== 'maintenance')
          .map(day => day.date);
      
        setBookingDates(booked);
        setMaintenanceDates(maintenance);
        
        // You might want to add a state for unavailable dates
        setUnavailableDates(unavailable);
      }
    } catch (error) {
      notificationWithIcon('error', 'Error', 'Failed to fetch room availability');
    } finally {
      setCalendarLoading(false);
    }
  };

  const handlePanelChange = (date) => {
    setCurrentMonth(date);
    if (selectedRoom) {
      // Month is 1-indexed in the API but 0-indexed in dayjs
      fetchRoomAvailability(selectedRoom._id, date.year(), date.month() + 1);
    }
  };

  const dateCellRender = (date) => {
    const dateStr = date.format('YYYY-MM-DD');
    const isBooked = bookingDates.includes(dateStr);
    const isMaintenance = maintenanceDates.includes(dateStr);
    const isUnavailable = unavailableDates.includes(dateStr);
    
    return (
      <div className="date-cell">
        {isBooked && (
          <Badge status="error" text={<span className="text-xs">Booked</span>} />
        )}
        {isMaintenance && (
          <Badge status="warning" text={<span className="text-xs">Maintenance</span>} />
        )}
        {isUnavailable && (
          <Badge status="default" text={<span className="text-xs">Unavailable</span>} />
        )}
        {!isBooked && !isMaintenance && !isUnavailable && date.month() === currentMonth.month() && (
          <Badge status="success" text={<span className="text-xs">Available</span>} />
        )}
      </div>
    );
  };

  const handleViewHistory = async (room, type = 'maintenance') => {
    setSelectedRoom(room);
    setHistoryType(type);
    setHistoryModal(true);
    
    try {
      setLoading(true);
      if (type === 'maintenance') {
        const response = await ApiService.get(`/api/v1/rooms/${room._id}/maintenance-history`);
        if (response?.result_code === 0) {
          setMaintenanceHistory(response.result.data.maintenance_records);
        }
      } else {
        const response = await ApiService.get(`/api/v1/rooms/${room._id}/cleaning-history`);
        if (response?.result_code === 0) {
          setCleaningHistory(response.result.data.cleaning_records);
        }
      }
    } catch (error) {
      notificationWithIcon('error', 'Error', `Failed to fetch ${type} history`);
    } finally {
      setLoading(false);
    }
  };

  const submitMaintenance = async (values) => {
    try {
      const payload = {
        start_date: values.date_range[0].format('YYYY-MM-DD'),
        end_date: values.date_range[1].format('YYYY-MM-DD'),
        reason: values.reason,
        notes: values.notes
      };
      
      const response = await ApiService.post(`/api/v1/rooms/${selectedRoom._id}/maintenance`, payload);
      
      if (response?.result_code === 0) {
        notificationWithIcon('success', 'Success', 'Maintenance scheduled successfully');
        setMaintenanceModal(false);
        handleRefresh();
      }
    } catch (error) {
      notificationWithIcon('error', 'Error', error.message || 'Failed to schedule maintenance');
    }
  };

  const submitCleaning = async (values) => {
    try {
      const payload = {
        notes: values.notes,
        checklist: {
          bathroom_cleaned: values.bathroom_cleaned,
          bed_made: values.bed_made,
          dusting_complete: values.dusting_complete,
          vacuum_complete: values.vacuum_complete
        },
        time_spent: values.time_spent,
        issues_reported: values.issues ? [{ 
          issue: values.issues, 
          severity: values.severity,
          requires_maintenance: values.requires_maintenance
        }] : []
      };
      
      const response = await ApiService.post(`/api/v1/rooms/${selectedRoom._id}/mark-cleaned`, payload);
      
      if (response?.result_code === 0) {
        notificationWithIcon('success', 'Success', 'Room marked as cleaned');
        setCleaningModal(false);
        handleRefresh();
      }
    } catch (error) {
      notificationWithIcon('error', 'Error', error.message || 'Failed to mark room as cleaned');
    }
  };

  const submitStatusUpdate = async (values) => {
    try {
      const payload = {
        status: values.status,
        notes: values.notes
      };
      
      const response = await ApiService.put(`/api/v1/rooms/${selectedRoom._id}/status`, payload);
      
      if (response?.result_code === 0) {
        notificationWithIcon('success', 'Success', 'Room status updated successfully');
        setStatusModal(false);
        handleRefresh();
      }
    } catch (error) {
      notificationWithIcon('error', 'Error', error.message || 'Failed to update room status');
    }
  };

  const updateMaintenanceStatus = async (maintenanceId, status, summary = '', notes = '') => {
    try {
      const payload = {
        status,
        summary,
        notes
      };
      
      const response = await ApiService.put(
        `/api/v1/rooms/${selectedRoom._id}/maintenance/${maintenanceId}`, 
        payload
      );
      
      if (response?.result_code === 0) {
        notificationWithIcon('success', 'Success', `Maintenance ${status} successfully`);
        handleViewHistory(selectedRoom, 'maintenance');
        handleRefresh();
      }
    } catch (error) {
      notificationWithIcon('error', 'Error', error.message || 'Failed to update maintenance status');
    }
  };

  const verifyCleaning = async (cleaningId, passed, comments = '') => {
    try {
      const payload = {
        passed,
        comments
      };
      
      const response = await ApiService.put(
        `/api/v1/rooms/${selectedRoom._id}/cleaning/${cleaningId}/verify`, 
        payload
      );
      
      if (response?.result_code === 0) {
        notificationWithIcon('success', 'Success', `Cleaning verification ${passed ? 'passed' : 'failed'}`);
        handleViewHistory(selectedRoom, 'cleaning');
        handleRefresh();
      }
    } catch (error) {
      notificationWithIcon('error', 'Error', error.message || 'Failed to verify cleaning');
    }
  };

  const runStatusUpdate = async () => {
    try {
      setLoading(true);
      const response = await ApiService.post('/api/v1/rooms/update-completed-bookings');
      
      if (response?.result_code === 0) {
        notificationWithIcon('success', 'Success', 'Room status update completed');
        handleRefresh();
      }
    } catch (error) {
      notificationWithIcon('error', 'Error', error.message || 'Failed to update room statuses');
    } finally {
      setLoading(false);
    }
  };

  const columns = [
    {
      title: 'Room Number',
      dataIndex: 'room_number',
      key: 'room_number',
      sorter: (a, b) => a.room_number.localeCompare(b.room_number)
    },
    {
      title: 'Floor',
      dataIndex: 'floor',
      key: 'floor',
      sorter: (a, b) => a.floor - b.floor
    },
    {
      title: 'Room Type',
      dataIndex: ['room_type', 'name'],
      key: 'room_type',
      sorter: (a, b) => a.room_type.name.localeCompare(b.room_type.name)
    },
    {
      title: 'Status',
      dataIndex: 'current_status',
      key: 'current_status',
      render: (status) => <RoomStatus status={status} />,
      filters: roomStatusOptions.map(option => ({
        text: option.label,
        value: option.value
      })),
      onFilter: (value, record) => record.current_status === value
    },
    {
      title: 'Last Cleaned',
      key: 'last_cleaned',
      render: (_, record) => (
        record.last_cleaned?.date ? 
        <Tooltip title={`By: ${record.last_cleaned.by?.fullName || 'Unknown'}`}>
          {format(new Date(record.last_cleaned.date), 'MMM dd, yyyy')}
        </Tooltip> : 
        <Tag color="red">Not recorded</Tag>
      )
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space size="small">
          <Tooltip title="Update Status">
            <Button 
              type="primary" 
              icon={<EditOutlined />} 
              size="small"
              onClick={() => handleUpdateStatus(record)}
            />
          </Tooltip>
          
          <Tooltip title="Schedule Maintenance">
            <Button 
              type="default" 
              icon={<ToolOutlined />} 
              size="small"
              onClick={() => handleScheduleMaintenance(record)}
            />
          </Tooltip>
          
          <Tooltip title="Mark as Cleaned">
            <Button 
              type="default" 
              icon={<ExclamationCircleOutlined />} 
              size="small"
              onClick={() => handleMarkCleaning(record)}
              disabled={record.current_status !== 'cleaning'}
            />
          </Tooltip>
          
          <Tooltip title="View Booking Calendar">
            <Button 
              type="default" 
              icon={<CalendarOutlined />} 
              size="small"
              onClick={() => handleViewCalendar(record)}
            />
          </Tooltip>
          
          <Dropdown overlay={
            <Menu>
              <Menu.Item key="maintenance" onClick={() => handleViewHistory(record, 'maintenance')}>
                <ToolOutlined /> Maintenance History
              </Menu.Item>
              <Menu.Item key="cleaning" onClick={() => handleViewHistory(record, 'cleaning')}>
                <ExclamationCircleOutlined /> Cleaning History
              </Menu.Item>
            </Menu>
          }>
            <Button 
              type="default" 
              icon={<HistoryOutlined />} 
              size="small"
            />
          </Dropdown>
        </Space>
      )
    }
  ];

  return (
    <div className="room-management">
      <Card 
        title="Room Management" 
        extra={
          <Space>
            <Button 
              type="primary" 
              icon={<SyncOutlined />} 
              onClick={handleRefresh}
              loading={fetchLoading}
            >
              Refresh
            </Button>
            <Tooltip title="Update status for rooms with completed bookings">
              <Button 
                type="default" 
                icon={<CalendarOutlined />} 
                onClick={runStatusUpdate}
                loading={loading}
              >
                Update Room Status
              </Button>
            </Tooltip>
          </Space>
        }
      >
        <Table 
          dataSource={rooms} 
          columns={columns} 
          rowKey="_id"
          loading={fetchLoading}
          pagination={{ pageSize: 10 }}
          locale={{
            emptyText: fetchError ? (
              <Empty 
                description={fetchError}
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            ) : (
              <Empty 
                description="No rooms found"
                image={Empty.PRESENTED_IMAGE_SIMPLE}
              />
            )
          }}
        />
      </Card>

      {/* Calendar Drawer */}
      <Drawer
        title={`Room ${selectedRoom?.room_number} - Booking Calendar`}
        placement="right"
        onClose={() => setCalendarModal(false)}
        open={calendarModal}
        width={800}
        extra={[
          <Button key="close" onClick={() => setCalendarModal(false)}>
            Close
          </Button>
        ]}
      >
        <div className="calendar-legend mb-4 flex gap-4">
          <div className="flex items-center">
            <Badge status="success" />
            <span className="ml-2">Available</span>
          </div>
          <div className="flex items-center">
            <Badge status="error" />
            <span className="ml-2">Booked</span>
          </div>
          <div className="flex items-center">
            <Badge status="warning" />
            <span className="ml-2">Maintenance</span>
          </div>
          <div className="flex items-center">
            <Badge status="default" />
            <span className="ml-2">Unavailable</span>
          </div>
        </div>
        
        {calendarLoading ? (
          <div className="flex justify-center items-center h-96">
            <Skeleton active paragraph={{ rows: 10 }} />
          </div>
        ) : (
          <Calendar 
            dateCellRender={dateCellRender}
            onPanelChange={handlePanelChange}
            value={currentMonth}
          />
        )}
      </Drawer>

      {/* Schedule Maintenance Drawer */}
      <Drawer
        title={`Schedule Maintenance for Room ${selectedRoom?.room_number}`}
        placement="right"
        onClose={() => setMaintenanceModal(false)}
        open={maintenanceModal}
        width={500}
      >
        <Form
          form={maintenanceForm}
          layout="vertical"
          onFinish={submitMaintenance}
        >
          <Form.Item
            name="date_range"
            label="Maintenance Period"
            rules={[{ required: true, message: 'Please select maintenance period' }]}
          >
            <DatePicker.RangePicker style={{ width: '100%' }} />
          </Form.Item>
          
          <Form.Item
            name="reason"
            label="Reason"
            rules={[{ required: true, message: 'Please enter maintenance reason' }]}
          >
            <Select>
              <Select.Option value="routine">Routine Maintenance</Select.Option>
              <Select.Option value="repair">Repair</Select.Option>
              <Select.Option value="renovation">Renovation</Select.Option>
              <Select.Option value="inspection">Inspection</Select.Option>
              <Select.Option value="other">Other</Select.Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="notes"
            label="Notes"
          >
            <TextArea rows={4} />
          </Form.Item>
          
          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              Schedule Maintenance
            </Button>
          </Form.Item>
        </Form>
      </Drawer>

      {/* Mark as Cleaned Drawer */}
      <Drawer
        title={`Mark Room ${selectedRoom?.room_number} as Cleaned`}
        placement="right"
        onClose={() => setCleaningModal(false)}
        open={cleaningModal}
        width={500}
      >
        <Form
          form={cleaningForm}
          layout="vertical"
          onFinish={submitCleaning}
          initialValues={{
            bathroom_cleaned: true,
            bed_made: true,
            dusting_complete: true,
            vacuum_complete: true,
            requires_maintenance: false
          }}
        >
          <Divider>Cleaning Checklist</Divider>
          
          <Form.Item name="bathroom_cleaned" valuePropName="checked">
            <Checkbox>Bathroom Cleaned</Checkbox>
          </Form.Item>
          
          <Form.Item name="bed_made" valuePropName="checked">
            <Checkbox>Bed Made</Checkbox>
          </Form.Item>
          
          <Form.Item name="dusting_complete" valuePropName="checked">
            <Checkbox>Dusting Complete</Checkbox>
          </Form.Item>
          
          <Form.Item name="vacuum_complete" valuePropName="checked">
            <Checkbox>Vacuum Complete</Checkbox>
          </Form.Item>
          
          <Form.Item
            name="time_spent"
            label="Time Spent (minutes)"
            rules={[{ required: true, message: 'Please enter time spent' }]}
          >
            <InputNumber min={1} max={240} style={{ width: '100%' }} />
          </Form.Item>
          
          <Divider>Issues Reported</Divider>
          
          <Form.Item
            name="issues"
            label="Issues Found"
          >
            <TextArea rows={2} placeholder="Describe any issues found during cleaning" />
          </Form.Item>
          
          <Form.Item
            name="severity"
            label="Severity"
          >
            <Select placeholder="Select severity if issues found">
              <Select.Option value="low">Low</Select.Option>
              <Select.Option value="medium">Medium</Select.Option>
              <Select.Option value="high">High</Select.Option>
            </Select>
          </Form.Item>
          
          <Form.Item name="requires_maintenance" valuePropName="checked">
            <Checkbox>Requires Maintenance</Checkbox>
          </Form.Item>
          
          <Form.Item
            name="notes"
            label="Additional Notes"
          >
            <TextArea rows={3} />
          </Form.Item>
          
          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              Mark as Cleaned
            </Button>
          </Form.Item>
        </Form>
      </Drawer>

      {/* Update Status Drawer */}
      <Drawer
        title={`Update Status for Room ${selectedRoom?.room_number}`}
        placement="right"
        onClose={() => setStatusModal(false)}
        open={statusModal}
        width={500}
      >
        <Form
          form={statusForm}
          layout="vertical"
          onFinish={submitStatusUpdate}
        >
          <Form.Item
            name="status"
            label="Room Status"
            rules={[{ required: true, message: 'Please select room status' }]}
          >
            <Select>
              {roomStatusOptions.map(option => (
                <Select.Option key={option.value} value={option.value}>
                  <Tag color={option.color}>{option.label}</Tag>
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          
          <Form.Item
            name="notes"
            label="Notes"
          >
            <TextArea rows={4} placeholder="Reason for status change" />
          </Form.Item>
          
          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              Update Status
            </Button>
          </Form.Item>
        </Form>
      </Drawer>

      {/* History Drawer */}
      <Drawer
        title={`${historyType === 'maintenance' ? 'Maintenance' : 'Cleaning'} History for Room ${selectedRoom?.room_number}`}
        placement="right"
        onClose={() => setHistoryModal(false)}
        open={historyModal}
        width={800}
        extra={[
          <Button key="close" onClick={() => setHistoryModal(false)}>
            Close
          </Button>
        ]}
      >
        <Tabs 
          activeKey={historyType} 
          onChange={setHistoryType}
          onTabClick={(key) => handleViewHistory(selectedRoom, key)}
        >
          <TabPane tab="Maintenance History" key="maintenance">
            {maintenanceHistory.length === 0 ? (
              <Empty description="No maintenance history found" />
            ) : (
              <Timeline mode="left">
                {maintenanceHistory.map(record => (
                  <Timeline.Item 
                    key={record._id}
                    color={
                      record.status === 'completed' ? 'green' :
                      record.status === 'in_progress' ? 'blue' :
                      record.status === 'scheduled' ? 'orange' : 'red'
                    }
                    label={format(new Date(record.start_date), 'MMM dd, yyyy')}
                  >
                    <Card size="small" style={{ marginBottom: 16 }}>
                      <Space direction="vertical" style={{ width: '100%' }}>
                        <Space>
                          <Tag color={
                            record.status === 'completed' ? 'green' :
                            record.status === 'in_progress' ? 'blue' :
                            record.status === 'scheduled' ? 'orange' : 'red'
                          }>{record.status}</Tag>
                          <Text>{record.reason}</Text>
                        </Space>
                        <Text>{record.notes}</Text>
                        {record.summary && <Text strong>Summary:</Text>}
                        <Text>{record.summary}</Text>
                        <Text strong>Duration:</Text>
                        <Text>{format(new Date(record.start_date), 'MMM dd, yyyy')} - {format(new Date(record.end_date), 'MMM dd, yyyy')}</Text>
                        <Space>
                          <Popconfirm
                            title="Update Status"
                            onConfirm={() => updateMaintenanceStatus(record._id, 'in_progress')}
                            okText="In Progress"
                            cancelText="Cancel"
                            disabled={record.status !== 'scheduled'}
                          >
                            <Button 
                              size="small" 
                              type="primary"
                              disabled={record.status !== 'scheduled'}
                            >
                              Start
                            </Button>
                          </Popconfirm>
                          
                          <Popconfirm
                            title="Mark as Completed"
                            onConfirm={() => {
                              Modal.confirm({
                                title: 'Complete Maintenance',
                                icon: <CheckCircleOutlined />,
                                content: (
                                  <Input.TextArea 
                                    placeholder="Enter completion summary" 
                                    onChange={(e) => setSummary(e.target.value)}
                                    rows={4}
                                  />
                                ),
                                onOk: () => updateMaintenanceStatus(record._id, 'completed', summary),
                                okText: 'Complete',
                                cancelText: 'Cancel',
                              });
                            }}
                            okText="Complete"
                            cancelText="Cancel"
                            disabled={record.status !== 'in_progress'}
                          >
                            <Button 
                              size="small" 
                              type="primary"
                              disabled={record.status !== 'in_progress'}
                            >
                              Complete
                            </Button>
                          </Popconfirm>
                          
                          <Popconfirm
                            title="Cancel Maintenance"
                            onConfirm={() => {
                              Modal.confirm({
                                title: 'Cancel Maintenance',
                                icon: <CloseCircleOutlined />,
                                content: (
                                  <Input.TextArea 
                                    placeholder="Enter cancellation reason" 
                                    onChange={(e) => setNotes(e.target.value)}
                                    rows={4}
                                  />
                                ),
                                onOk: () => updateMaintenanceStatus(record._id, 'cancelled', '', notes),
                                okText: 'Cancel Maintenance',
                                cancelText: 'Go Back',
                              });
                            }}
                            okText="Cancel"
                            cancelText="Go Back"
                            disabled={record.status === 'completed' || record.status === 'cancelled'}
                          >
                            <Button 
                              size="small" 
                              danger
                              disabled={record.status === 'completed' || record.status === 'cancelled'}
                            >
                              Cancel
                            </Button>
                          </Popconfirm>
                        </Space>
                      </Space>
                    </Card>
                  </Timeline.Item>
                ))}
              </Timeline>
            )}
          </TabPane>
          
          <TabPane tab="Cleaning History" key="cleaning">
            {cleaningHistory.length === 0 ? (
              <Empty description="No cleaning history found" />
            ) : (
              <Timeline mode="left">
                {cleaningHistory.map(record => (
                  <Timeline.Item 
                    key={record._id}
                    color={
                      record.status === 'verified' && record.inspection?.passed ? 'green' :
                      record.status === 'verified' && !record.inspection?.passed ? 'red' :
                      record.status === 'completed' ? 'blue' :
                      record.status === 'in_progress' ? 'orange' : 'gray'
                    }
                    label={format(new Date(record.created_at), 'MMM dd, yyyy')}
                  >
                    <Card size="small" style={{ marginBottom: 16 }}>
                      <Space direction="vertical" style={{ width: '100%' }}>
                        <Space>
                          <Tag color={
                            record.status === 'verified' && record.inspection?.passed ? 'green' :
                            record.status === 'verified' && !record.inspection?.passed ? 'red' :
                            record.status === 'completed' ? 'blue' :
                            record.status === 'in_progress' ? 'orange' : 'gray'
                          }>{record.status}</Tag>
                          <Text>Cleaned by: {record.cleaned_by?.fullName || 'Unknown'}</Text>
                        </Space>
                        
                        {record.time_spent && (
                          <Text>Time spent: {record.time_spent} minutes</Text>
                        )}
                        
                        {record.notes && (
                          <Text>Notes: {record.notes}</Text>
                        )}
                        
                        {record.checklist && (
                          <div>
                            <Text strong>Checklist:</Text>
                            <ul style={{ margin: '4px 0', paddingLeft: 20 }}>
                              {Object.entries(record.checklist).map(([key, value]) => (
                                <li key={key}>
                                  {key.split('_').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}: 
                                  {value ? ' ✓' : ' ✗'}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                        
                        {record.issues_reported && record.issues_reported.length > 0 && (
                          <div>
                            <Text strong>Issues Reported:</Text>
                            <ul style={{ margin: '4px 0', paddingLeft: 20 }}>
                              {record.issues_reported.map((issue, index) => (
                                <li key={index}>
                                  <Tag color={
                                    issue.severity === 'high' ? 'red' :
                                    issue.severity === 'medium' ? 'orange' : 'green'
                                  }>{issue.severity}</Tag>
                                  {issue.issue}
                                  {issue.requires_maintenance && (
                                    <Tag color="purple" style={{ marginLeft: 8 }}>Needs Maintenance</Tag>
                                  )}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                        
                        {record.inspection && (
                          <div>
                            <Text strong>Inspection:</Text>
                            <div style={{ marginTop: 4 }}>
                              <Tag color={record.inspection.passed ? 'green' : 'red'}>
                                {record.inspection.passed ? 'Passed' : 'Failed'}
                              </Tag>
                              <Text>
                                Inspected by: {record.inspection.inspected_by?.fullName || 'Unknown'}
                              </Text>
                              {record.inspection.comments && (
                                <Text>Comments: {record.inspection.comments}</Text>
                              )}
                            </div>
                          </div>
                        )}
                        
                        {record.status === 'completed' && (
                          <Space style={{ marginTop: 8 }}>
                            <Popconfirm
                              title="Verify Cleaning"
                              onConfirm={() => verifyCleaning(record._id, true)}
                              okText="Pass"
                              cancelText="Cancel"
                            >
                              <Button size="small" type="primary" icon={<CheckCircleOutlined />}>
                                Pass
                              </Button>
                            </Popconfirm>
                            
                            <Popconfirm
                              title="Fail Cleaning"
                              onConfirm={() => {
                                Modal.confirm({
                                  title: 'Fail Cleaning Verification',
                                  icon: <CloseCircleOutlined />,
                                  content: (
                                    <Input.TextArea 
                                      placeholder="Enter reason for failure" 
                                      onChange={(e) => setComments(e.target.value)}
                                      rows={4}
                                    />
                                  ),
                                  onOk: () => verifyCleaning(record._id, false, comments),
                                  okText: 'Fail',
                                  cancelText: 'Cancel',
                                });
                              }}
                              okText="Fail"
                              cancelText="Cancel"
                            >
                              <Button size="small" danger icon={<CloseCircleOutlined />}>
                                Fail
                              </Button>
                            </Popconfirm>
                          </Space>
                        )}
                      </Space>
                    </Card>
                  </Timeline.Item>
                ))}
              </Timeline>
            )}
          </TabPane>
        </Tabs>
      </Drawer>
    </div>
  );
}

export default RoomManagement;
