import jwtDecode from 'jwt-decode';

const APP_USER_STORAGE = 'TTS-NEXUS-USER';
const APP_ACCESS_TOKEN = 'TTS-NEXUS-ACCESS-TOKEN';
const APP_REFRESH_TOKEN = 'TTS-NEXUS-REFRESH-TOKEN';

/**
 * function to get session user details
 * @returns if session user return user object otherwise return null
 */
export const getSessionUser = () => {
  const userStr = localStorage.getItem(APP_USER_STORAGE);

  if (userStr) {
    return JSON.parse(userStr);
  }
  return null;
};

/**
 * function to get session user access-token
 * @returns if session user return access-token otherwise return null
 */
export const getSessionToken = () => {
  const tokenStr = localStorage.getItem(APP_ACCESS_TOKEN);

  if (tokenStr) {
    return tokenStr;
  }
  return null;
};

/**
 * function to get session user refresh-token
 * @returns if session user return refresh-token otherwise return null
 */
export const getRefreshToken = () => {
  const tokenStr = localStorage.getItem(APP_REFRESH_TOKEN);

  if (tokenStr) {
    return tokenStr;
  }
  return null;
};

/**
 * function to set session user and JWT access-token & refresh-token
 * @param {*} user user object
 * @param {*} accessToken user JWT access-token
 * @param {*} refreshToken user JWT refresh-token
 */
export const setSessionUserAndToken = (user, accessToken, refreshToken) => {
  localStorage.setItem(APP_USER_STORAGE, JSON.stringify(user));
  localStorage.setItem(APP_ACCESS_TOKEN, accessToken);
  localStorage.setItem(APP_REFRESH_TOKEN, refreshToken);
  
  // Store token expiration time for better tracking
  try {
    const decoded = jwtDecode(accessToken);
    if (decoded.exp) {
      localStorage.setItem('TTS-NEXUS-TOKEN-EXPIRY', decoded.exp);
      console.log(`Token will expire at: ${new Date(decoded.exp * 1000).toLocaleString()}`);
    }
  } catch (error) {
    console.error('Error storing token expiration:', error);
  }
};

/**
 * function to set session JWT access-token & refresh-token
 * @param {*} accessToken user JWT access-token
 * @param {*} refreshToken user JWT refresh-token
 */
export const setSessionAccessAndRefreshToken = (accessToken, refreshToken) => {
  localStorage.setItem(APP_ACCESS_TOKEN, accessToken);
  localStorage.setItem(APP_REFRESH_TOKEN, refreshToken);
  
  // Store token expiration time for better tracking
  try {
    const decoded = jwtDecode(accessToken);
    if (decoded.exp) {
      localStorage.setItem('TTS-NEXUS-TOKEN-EXPIRY', decoded.exp);
      console.log(`Token will expire at: ${new Date(decoded.exp * 1000).toLocaleString()}`);
    }
  } catch (error) {
    console.error('Error storing token expiration:', error);
  }
};

/**
 * function to set session user
 * @param {*} user user object
 */
export const setSessionUser = (user) => {
  localStorage.setItem(APP_USER_STORAGE, JSON.stringify(user));
};

/**
 * function to set session user object key against value
 * @param {*} key session user object key
 * @param {*} value session user object key's value
 */
export const setSessionUserKeyAgainstValue = (key, value) => {
  const userStr = localStorage.getItem(APP_USER_STORAGE);
  let userStrObj = JSON.parse(userStr);

  userStrObj = {
    ...userStrObj, [key]: value
  };

  localStorage.setItem(APP_USER_STORAGE, JSON.stringify(userStrObj));
};

/**
 * function to removed in session and logout user
 */
export const removeSessionAndLogoutUser = () => {
  localStorage.removeItem(APP_USER_STORAGE);
  localStorage.removeItem(APP_ACCESS_TOKEN);
  localStorage.removeItem(APP_REFRESH_TOKEN);
  window.location.href = '/auth/login';
};

/**
 * Get the expiration time from the JWT token
 * @returns {number|null} Expiration timestamp in seconds or null if invalid
 */
export const getTokenExpiration = () => {
  try {
    // First try to get from localStorage for better reliability
    const storedExpiry = localStorage.getItem('TTS-NEXUS-TOKEN-EXPIRY');
    if (storedExpiry) {
      return parseInt(storedExpiry, 10);
    }
    
    // Fallback to decoding the token
    const token = getSessionToken();
    if (!token) return null;
    
    const decoded = jwtDecode(token);
    return decoded.exp; // JWT exp is in seconds
  } catch (error) {
    console.error('Error decoding token:', error);
    return null;
  }
};

/**
 * Check if the current token is expired
 * @returns {boolean} True if token is expired or invalid
 */
export const isTokenExpired = () => {
  const expiration = getTokenExpiration();
  if (!expiration) return true;
  
  // Current time in seconds
  const now = Math.floor(Date.now() / 1000);
  return now >= expiration;
};
