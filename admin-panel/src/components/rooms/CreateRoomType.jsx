import React, { useState, useEffect } from 'react';
import {
  Form, Input, InputNumber, Select, Switch, Button, Divider, Card, TimePicker, DatePicker
} from 'antd';
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons';
import ApiService from '../../utils/apiService';
import notificationWithIcon from '../../utils/notification';
import dayjs from 'dayjs';

const { TextArea } = Input;

function CreateRoomType({ onSuccess }) {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  const onFinish = async (values) => {
    try {
      setLoading(true);
      
      // Validate required fields
      if (!values.name || !values.description || values.base_price <= 0) {
        notificationWithIcon('error', 'Validation Error', 'Name, description, and base price are required');
        setLoading(false);
        return;
      }
      
      // Format the check-in and check-out times
      const checkInTime = values.policies?.check_in_time 
        ? dayjs(values.policies.check_in_time).format('HH:mm') 
        : "14:00";
      
      const checkOutTime = values.policies?.check_out_time 
        ? dayjs(values.policies.check_out_time).format('HH:mm') 
        : "12:00";
      
      // Format the maintenance dates
      const lastRenovation = values.maintenance?.last_renovation 
        ? dayjs(values.maintenance.last_renovation).format('YYYY-MM-DD') 
        : null;
      
      const nextMaintenance = values.maintenance?.next_maintenance 
        ? dayjs(values.maintenance.next_maintenance).format('YYYY-MM-DD') 
        : null;
      
      // Ensure bed configuration is valid
      const bedConfiguration = values.specifications?.bed_configuration?.filter(bed => 
        bed && bed.bed_type && bed.count
      ) || [];
      
      
      const formData = {
        name: values.name.trim(),
        description: values.description.trim(),
        is_active: values.is_active,
        base_price: values.base_price,
        specifications: {
          room_size: {
            value: values.specifications.room_size.value,
            unit: values.specifications.room_size.unit
          },
          max_occupancy: {
            adults: values.specifications.max_occupancy.adults,
            children: values.specifications.max_occupancy.children
          },
          bed_configuration: bedConfiguration
        },
        amenities: {
          basic: values.amenities.basic || [],
          additional: values.amenities.additional?.map(item => ({
            name: item.name,
            description: item.description,
            icon_url: item.icon_url || ''
          })) || []
        },
        policies: {
          allow_pets: values.policies.allow_pets,
          provide_breakfast: values.policies.provide_breakfast,
          smoking_allowed: values.policies.smoking_allowed,
          cancellation_policy: {
            type: values.policies.cancellation_policy.type,
            hours_before: values.policies.cancellation_policy.hours_before,
            refund_percentage: values.policies.cancellation_policy.refund_percentage
          },
          check_in_time: checkInTime,
          check_out_time: checkOutTime
        },
        pricing: {
          hourly_rate: values.pricing.hourly_rate || 0,
          daily_rate: values.pricing.daily_rate,
          weekly_discount: values.pricing.weekly_discount || 0,
          monthly_discount: values.pricing.monthly_discount || 0,
          cleaning_fee: values.pricing.cleaning_fee || 0,
          security_deposit: values.pricing.security_deposit || 0
        },
        maintenance: {
          last_renovation: lastRenovation,
          next_maintenance: nextMaintenance,
          maintenance_notes: values.maintenance?.maintenance_notes || ''
        }
      };
      
      console.log('Submitting room type data:', formData);
      
      const response = await ApiService.post('/api/v1/room-types', formData);
      notificationWithIcon('success', 'Success', 'Room type created successfully');
      form.resetFields();
      onSuccess?.();
    } catch (error) {
      console.error('Error creating room type:', error);
      notificationWithIcon(
        'error', 
        'Error', 
        error.response?.data?.result?.error || 'Failed to create room type'
      );
    } finally {
      setLoading(false);
    }
  };

  const initialValues = {
    is_active: true,
    base_price: 100, // Set a default value greater than 0
    specifications: {
      room_size: { 
        value: 200,
        unit: 'sqft'
      },
      max_occupancy: { 
        adults: 2, 
        children: 1 
      },
      bed_configuration: [
        { bed_type: 'queen', count: 1 } // Add a default bed configuration
      ]
    },
    amenities: { 
      basic: [],
      additional: []
    },
    policies: {
      allow_pets: false,
      provide_breakfast: false,
      smoking_allowed: false,
      cancellation_policy: {
        type: 'moderate',
        hours_before: 24,
        refund_percentage: 80
      },
      check_in_time: dayjs('14:00', 'HH:mm'),
      check_out_time: dayjs('12:00', 'HH:mm')
    },
    pricing: {
      hourly_rate: 0,
      daily_rate: 100,
      weekly_discount: 0,
      monthly_discount: 0,
      cleaning_fee: 0,
      security_deposit: 0
    },
    maintenance: {
      last_renovation: null,
      next_maintenance: null,
      maintenance_notes: ''
    }
  };

  return (
    <Card className="max-w-3xl mx-auto">
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        initialValues={initialValues}
      >
        <Divider>Basic Information</Divider>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Form.Item
            name="name"
            label="Room Type Name"
            rules={[{ required: true, message: 'Room type name is required' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="base_price"
            label="Base Price"
            rules={[{ required: true, message: 'Base price is required' }]}
          >
            <InputNumber 
              min={0} 
              style={{ width: '100%' }}
              formatter={value => `₹ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => value.replace(/\$\s?|(,*)/g, '')}
            />
          </Form.Item>

          <Form.Item
            name="is_active"
            label="Status"
            valuePropName="checked"
          >
            <Switch checkedChildren="Active" unCheckedChildren="Inactive" />
          </Form.Item>
        </div>

        <Form.Item
          name="description"
          label="Description"
          rules={[{ required: true, message: 'Description is required' }]}
        >
          <TextArea rows={4} />
        </Form.Item>

        <Divider>Room Specifications</Divider>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <Form.Item label="Room Size" required>
              <Input.Group compact>
                <Form.Item
                  name={['specifications', 'room_size', 'value']}
                  noStyle
                  rules={[{ required: true, message: 'Room size is required' }]}
                >
                  <InputNumber style={{ width: '70%' }} min={1} />
                </Form.Item>
                <Form.Item
                  name={['specifications', 'room_size', 'unit']}
                  noStyle
                  initialValue="sqft"
                >
                  <Select style={{ width: '30%' }}>
                    <Select.Option value="sqft">sqft</Select.Option>
                    <Select.Option value="sqm">sqm</Select.Option>
                  </Select>
                </Form.Item>
              </Input.Group>
            </Form.Item>
          </div>

          <div>
            <Form.Item label="Max Occupancy" required>
              <Input.Group compact>
                <Form.Item
                  name={['specifications', 'max_occupancy', 'adults']}
                  noStyle
                  rules={[{ required: true, message: 'Adults count is required' }]}
                >
                  <InputNumber
                    min={1}
                    style={{ width: '50%' }}
                    placeholder="Adults"
                  />
                </Form.Item>
                <Form.Item
                  name={['specifications', 'max_occupancy', 'children']}
                  noStyle
                  rules={[{ required: true, message: 'Children count is required' }]}
                >
                  <InputNumber
                    min={0}
                    style={{ width: '50%' }}
                    placeholder="Children"
                  />
                </Form.Item>
              </Input.Group>
            </Form.Item>
          </div>
        </div>

        <Form.List name={['specifications', 'bed_configuration']}
          rules={[
            {
              validator: async (_, beds) => {
                if (!beds || beds.length < 1) {
                  return Promise.reject(new Error('At least one bed configuration is required'));
                }
              },
            },
          ]}
        >
          {(fields, { add, remove }, { errors }) => (
            <>
              {fields.map(({ key, name, ...restField }) => (
                <div key={key} className="flex items-center gap-4 mb-2">
                  <Form.Item
                    {...restField}
                    name={[name, 'bed_type']}
                    rules={[{ required: true, message: 'Bed type is required' }]}
                    style={{ marginBottom: 0, flex: 1 }}
                  >
                    <Select placeholder="Bed Type">
                      <Select.Option value="single">Single</Select.Option>
                      <Select.Option value="double">Double</Select.Option>
                      <Select.Option value="queen">Queen</Select.Option>
                      <Select.Option value="king">King</Select.Option>
                      <Select.Option value="sofa_bed">Sofa Bed</Select.Option>
                    </Select>
                  </Form.Item>
                  <Form.Item
                    {...restField}
                    name={[name, 'count']}
                    rules={[{ required: true, message: 'Count is required' }]}
                    style={{ marginBottom: 0, width: '100px' }}
                  >
                    <InputNumber min={1} placeholder="Count" style={{ width: '100%' }} />
                  </Form.Item>
                  <Button type="text" danger onClick={() => remove(name)}>
                    <MinusCircleOutlined />
                  </Button>
                </div>
              ))}
              <Form.Item>
                <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                  Add Bed Configuration
                </Button>
                <Form.ErrorList errors={errors} />
              </Form.Item>
            </>
          )}
        </Form.List>

        <Divider>Amenities</Divider>
        <Form.Item
          name={['amenities', 'basic']}
          label="Basic Amenities"
        >
          <Select mode="multiple" placeholder="Select basic amenities">
            <Select.Option value="wifi">WiFi</Select.Option>
            <Select.Option value="tv">TV</Select.Option>
            <Select.Option value="ac">AC</Select.Option>
            <Select.Option value="phone">Phone</Select.Option>
            <Select.Option value="heater">Heater</Select.Option>
            <Select.Option value="safe">Safe</Select.Option>
            <Select.Option value="fridge">Fridge</Select.Option>
          </Select>
        </Form.Item>

        <Form.List name={['amenities', 'additional']}>
          {(fields, { add, remove }) => (
            <>
              {fields.map(({ key, name, ...restField }) => (
                <div key={key} className="space-y-2">
                  <Form.Item
                    {...restField}
                    name={[name, 'name']}
                    rules={[{ required: true, message: 'Required' }]}
                  >
                    <Input placeholder="Amenity Name" />
                  </Form.Item>
                  <Form.Item
                    {...restField}
                    name={[name, 'description']}
                    rules={[{ required: true, message: 'Required' }]}
                  >
                    <Input placeholder="Description" />
                  </Form.Item>
                  <Form.Item
                    {...restField}
                    name={[name, 'icon_url']}
                  >
                    <Input placeholder="Icon URL" />
                  </Form.Item>
                  <Button type="text" danger onClick={() => remove(name)}>
                    <MinusCircleOutlined /> Remove
                  </Button>
                </div>
              ))}
              <Form.Item>
                <Button
                  type="dashed"
                  onClick={() => add()}
                  block
                  icon={<PlusOutlined />}
                >
                  Add Additional Amenity
                </Button>
              </Form.Item>
            </>
          )}
        </Form.List>

        <Divider>Policies</Divider>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <Form.Item
            name={['policies', 'allow_pets']}
            label="Allow Pets"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name={['policies', 'provide_breakfast']}
            label="Provide Breakfast"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>

          <Form.Item
            name={['policies', 'smoking_allowed']}
            label="Smoking Allowed"
            valuePropName="checked"
          >
            <Switch />
          </Form.Item>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Form.Item
            name={['policies', 'check_in_time']}
            label="Check-in Time"
          >
            <TimePicker 
              format="HH:mm" 
              style={{ width: '100%' }}
            />
          </Form.Item>

          <Form.Item
            name={['policies', 'check_out_time']}
            label="Check-out Time"
          >
            <TimePicker 
              format="HH:mm" 
              style={{ width: '100%' }}
            />
          </Form.Item>
        </div>

        <Form.Item label="Cancellation Policy">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Form.Item
              name={['policies', 'cancellation_policy', 'type']}
              noStyle
              rules={[{ required: true, message: 'Required' }]}
            >
              <Select>
                <Select.Option value="flexible">Flexible</Select.Option>
                <Select.Option value="moderate">Moderate</Select.Option>
                <Select.Option value="strict">Strict</Select.Option>
              </Select>
            </Form.Item>

            <Form.Item
              name={['policies', 'cancellation_policy', 'hours_before']}
              noStyle
              rules={[{ required: true, message: 'Required' }]}
            >
              <InputNumber
                min={0}
                placeholder="Hours Before"
                style={{ width: '100%' }}
              />
            </Form.Item>

            <Form.Item
              name={['policies', 'cancellation_policy', 'refund_percentage']}
              noStyle
              rules={[{ required: true, message: 'Required' }]}
            >
              <InputNumber
                min={0}
                max={100}
                placeholder="Refund %"
                style={{ width: '100%' }}
              />
            </Form.Item>
          </div>
        </Form.Item>

        <Divider>Pricing</Divider>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Form.Item
            name={['pricing', 'daily_rate']}
            label="Daily Rate"
            rules={[{ required: true, message: 'Daily rate is required' }]}
          >
            <InputNumber
              min={0}
              style={{ width: '100%' }}
              formatter={value => `₹ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => value.replace(/\$\s?|(,*)/g, '')}
            />
          </Form.Item>

          <Form.Item
            name={['pricing', 'hourly_rate']}
            label="Hourly Rate"
          >
            <InputNumber
              min={0}
              style={{ width: '100%' }}
              formatter={value => `₹ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => value.replace(/\$\s?|(,*)/g, '')}
            />
          </Form.Item>

          <Form.Item
            name={['pricing', 'weekly_discount']}
            label="Weekly Discount %"
          >
            <InputNumber min={0} max={100} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name={['pricing', 'monthly_discount']}
            label="Monthly Discount %"
          >
            <InputNumber min={0} max={100} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name={['pricing', 'cleaning_fee']}
            label="Cleaning Fee"
          >
            <InputNumber
              min={0}
              style={{ width: '100%' }}
              formatter={value => `₹ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => value.replace(/\$\s?|(,*)/g, '')}
            />
          </Form.Item>

          <Form.Item
            name={['pricing', 'security_deposit']}
            label="Security Deposit"
          >
            <InputNumber
              min={0}
              style={{ width: '100%' }}
              formatter={value => `₹ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => value.replace(/\$\s?|(,*)/g, '')}
            />
          </Form.Item>
        </div>

        <Divider>Maintenance</Divider>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Form.Item
            name={['maintenance', 'last_renovation']}
            label="Last Renovation Date"
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name={['maintenance', 'next_maintenance']}
            label="Next Maintenance Date"
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
        </div>

        <Form.Item
          name={['maintenance', 'maintenance_notes']}
          label="Maintenance Notes"
        >
          <TextArea rows={4} />
        </Form.Item>

        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading} block>
            Create Room Type
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
}

export default CreateRoomType;
