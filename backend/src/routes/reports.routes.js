import express from 'express';
import { isAuthenticatedUser, isAdmin } from '../middleware/app.authentication.js';
import {
  getMonthlyBookingsReport,
  getMonthlyCleaningsReport,
  getMonthlyMaintenanceReport,
  getMonthlyRevenueReport,
  exportReportAsCSV
} from '../controllers/reports.controllers.js';

const router = express.Router();

// All reports routes require admin authentication
router.use(isAuthenticatedUser, isAdmin);

// Monthly reports routes
router.get('/reports/bookings/monthly', getMonthlyBookingsReport);
router.get('/reports/cleanings/monthly', getMonthlyCleaningsReport);
router.get('/reports/maintenance/monthly', getMonthlyMaintenanceReport);
router.get('/reports/revenue/monthly', getMonthlyRevenueReport);

// Export routes
router.get('/reports/export', exportReportAsCSV);

export default router;
