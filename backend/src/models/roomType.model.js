import mongoose from 'mongoose';

const roomTypeSchema = new mongoose.Schema({
  name: {
    type: String,
    required: [true, 'Room type name is required'],
    unique: true,
    trim: true
  },
  slug: {
    type: String,
    unique: true,
    lowercase: true,
    index: true
  },
  description: {
    type: String,
    required: [true, 'Description is required']
  },
  is_active: {
    type: Boolean,
    default: true
  },
  base_price: {
    type: Number,
    required: [true, 'Base price is required']
  },
  specifications: {
    room_size: {
      value: {
        type: Number,
        required: [true, 'Room size value is required']
      },
      unit: {
        type: String,
        enum: ['sqft', 'sqm'],
        default: 'sqft'
      }
    },
    max_occupancy: {
      adults: {
        type: Number,
        required: [true, 'Maximum number of adults is required']
      },
      children: {
        type: Number,
        required: [true, 'Maximum number of children is required']
      }
    },
    bed_configuration: [{
      bed_type: {
        type: String,
        enum: ['single', 'double', 'queen', 'king', 'sofa_bed'],
        required: true
      },
      count: {
        type: Number,
        required: true,
        min: 1
      }
    }]
  },
  amenities: {
    basic: [{
      type: String,
      enum: ['wifi', 'tv', 'ac', 'phone', 'heater', 'safe', 'fridge']
    }],
    additional: [{
      name: {
        type: String,
        required: true
      },
      description: {
        type: String,
        required: true
      },
      icon_url: String
    }]
  },
  policies: {
    allow_pets: {
      type: Boolean,
      default: false
    },
    provide_breakfast: {
      type: Boolean,
      default: false
    },
    smoking_allowed: {
      type: Boolean,
      default: false
    },
    cancellation_policy: {
      type: {
        type: String,
        enum: ['flexible', 'moderate', 'strict'],
        default: 'moderate'
      },
      hours_before: {
        type: Number,
        default: 24
      },
      refund_percentage: {
        type: Number,
        min: 0,
        max: 100,
        default: 80
      }
    },
    check_in_time: {
      type: String,
      default: "14:00"
    },
    check_out_time: {
      type: String,
      default: "12:00"
    }
  },
  pricing: {
    hourly_rate: {
      type: Number,
      min: 0
    },
    daily_rate: {
      type: Number,
      required: [true, 'Daily rate is required'],
      min: 0
    },
    weekly_discount: {
      type: Number,
      min: 0,
      max: 100,
      default: 0
    },
    monthly_discount: {
      type: Number,
      min: 0,
      max: 100,
      default: 0
    },
    cleaning_fee: {
      type: Number,
      min: 0,
      default: 0
    },
    security_deposit: {
      type: Number,
      min: 0,
      default: 0
    }
  },
  maintenance: {
    last_renovation: {
      type: Date
    },
    next_maintenance: {
      type: Date
    },
    maintenance_notes: {
      type: String
    }
  },
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Create indexes
roomTypeSchema.index({ name: 1 });
roomTypeSchema.index({ 'pricing.daily_rate': 1 });
roomTypeSchema.index({ is_active: 1 });
roomTypeSchema.index({ slug: 1 });

// Pre-save middleware to generate slug
roomTypeSchema.pre('save', function(next) {
  if (this.isModified('name')) {
    this.slug = this.name
      .toLowerCase()
      .replace(/[^a-zA-Z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
  }
  next();
});

// Virtual for total capacity
roomTypeSchema.virtual('total_capacity').get(function() {
  return this.specifications.max_occupancy.adults + this.specifications.max_occupancy.children;
});

// Virtual for total beds
roomTypeSchema.virtual('total_beds').get(function() {
  // Add null checks to prevent "Cannot read properties of undefined (reading 'reduce')" error
  if (!this.specifications || !this.specifications.bed_configuration || !Array.isArray(this.specifications.bed_configuration)) {
    return 0;
  }
  return this.specifications.bed_configuration.reduce((total, bed) => total + bed.count, 0);
});

// Method to get average rating for this room type
roomTypeSchema.methods.getAverageRating = async function() {
  const rooms = await Room.find({ room_type: this._id });
  const roomIds = rooms.map(room => room._id);
  
  const reviews = await Review.find({ room_id: { $in: roomIds } });
  
  if (reviews.length === 0) {
    return 0;
  }
  
  const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0);
  return totalRating / reviews.length;
};

// Method to get occupancy rate for this room type
roomTypeSchema.methods.getOccupancyRate = async function(startDate, endDate) {
  const rooms = await Room.find({ room_type: this._id });
  const roomIds = rooms.map(room => room._id);
  
  const bookings = await Booking.find({
    room_id: { $in: roomIds },
    booking_status: { $in: ['approved', 'checked-in', 'completed'] },
    booking_dates: {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    }
  });
  
  // Calculate total possible room-nights
  const start = new Date(startDate);
  const end = new Date(endDate);
  const daysDiff = Math.ceil((end - start) / (1000 * 60 * 60 * 24));
  const totalPossibleNights = rooms.length * daysDiff;
  
  // Calculate booked room-nights
  let bookedNights = 0;
  bookings.forEach(booking => {
    booking.booking_dates.forEach(date => {
      if (date >= start && date <= end) {
        bookedNights++;
      }
    });
  });
  
  return totalPossibleNights > 0 ? (bookedNights / totalPossibleNights) * 100 : 0;
};

// Method to get revenue for this room type
roomTypeSchema.methods.getRevenue = async function(startDate, endDate) {
  const rooms = await Room.find({ room_type: this._id });
  const roomIds = rooms.map(room => room._id);
  
  const bookings = await Booking.find({
    room_id: { $in: roomIds },
    booking_status: { $in: ['completed', 'checked-in'] },
    createdAt: {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    }
  });
  
  return bookings.reduce((total, booking) => total + booking.total_amount, 0);
};

// Add validation for pricing
roomTypeSchema.path('pricing.daily_rate').validate(function(value) {
  return value > 0;
}, 'Daily rate must be greater than 0');

const RoomType = mongoose.model('RoomType', roomTypeSchema);

export default RoomType;
