import React, { useState } from 'react';
import { Card, Statistic, Select } from 'antd';
import { User, UserPlus, UserCheck, UserX } from 'react-feather';
import { 
  <PERSON><PERSON><PERSON>, <PERSON>, 
  <PERSON>atter<PERSON><PERSON>, <PERSON>atter,
  Treemap,
  XAxis, YAxis, CartesianGrid, 
  Tooltip, ResponsiveContainer,
  Legend,
  ZAxis
} from 'recharts';
import { motion, AnimatePresence } from 'framer-motion';

function UsersCard({ loading, data }) {
  const [selectedChart, setSelectedChart] = useState('scatter');
  const [hoveredStat, setHoveredStat] = useState(null);

  const formatter = (value) => <span>{value}</span>;

  const stats = [
    {
      title: 'Total Users',
      value: data?.users?.total || 0,
      icon: <User size={20} />,
      color: 'blue',
      key: 'total'
    },
    {
      title: 'Active Users',
      value: data?.users?.active || 0,
      icon: <UserCheck size={20} />,
      color: 'green',
      key: 'active'
    },
    {
      title: 'Inactive Users',
      value: data?.users?.inactive || 0,
      icon: <UserX size={20} />,
      color: 'amber',
      key: 'inactive'
    },
    {
      title: 'Blocked Users',
      value: data?.users?.blocked || 0,
      icon: <UserX size={20} />,
      color: 'red',
      key: 'blocked'
    }
  ];

  const renderChart = () => {
    switch (selectedChart) {
      case 'scatter':
        return (
          <ScatterChart
            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
            <XAxis dataKey="time" name="Time" />
            <YAxis dataKey="users" name="Users" />
            <ZAxis dataKey="amt" range={[64, 144]} name="Amount" />
            <Tooltip
              cursor={{ strokeDasharray: '3 3' }}
              contentStyle={{
                background: 'rgba(255, 255, 255, 0.95)',
                borderRadius: '8px',
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
              }}
            />
            <Legend />
            <Scatter
              name="User Trends"
              data={data?.users?.trends || []}
              fill="#2563eb"
              shape="circle"
            />
          </ScatterChart>
        );

      case 'treemap':
        return (
          <Treemap
            data={stats}
            dataKey="value"
            aspectRatio={4 / 3}
            stroke="#fff"
            fill="#2563eb"
          >
            <Tooltip
              contentStyle={{
                background: 'rgba(255, 255, 255, 0.95)',
                borderRadius: '8px',
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
              }}
            />
          </Treemap>
        );

      case 'stackedBar':
        return (
          <BarChart
            data={data?.user_trends || []}
            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip
              contentStyle={{
                background: 'rgba(255, 255, 255, 0.95)',
                borderRadius: '8px',
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
              }}
            />
            <Legend />
            <Bar dataKey="new" stackId="a" fill="#2563eb" />
            <Bar dataKey="returning" stackId="a" fill="#7c3aed" />
          </BarChart>
        );

      default:
        return null;
    }
  };

  return (
    <Card
      title={
        <div className='flex items-center justify-between px-6 py-4'>
          <div className='flex items-center space-x-3'>
            <User className='text-blue-600' />
            <span className='text-lg font-semibold'>Users Overview</span>
          </div>
          <Select
            defaultValue="scatter"
            onChange={setSelectedChart}
            className='w-36'
            options={[
              { value: 'scatter', label: 'Scatter Plot' },
              { value: 'treemap', label: 'Treemap' },
              { value: 'stackedBar', label: 'Stacked Bar' },
            ]}
          />
        </div>
      }
      loading={loading}
      className='shadow-lg hover:shadow-xl transition-all duration-300'
      bodyStyle={{ padding: 0 }}
    >
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6 p-6'>
        {/* Stats Section */}
        <div className='space-y-4'>
          <div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
            {stats.map((stat) => (
              <motion.div
                key={stat.key}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onHoverStart={() => setHoveredStat(stat.key)}
                onHoverEnd={() => setHoveredStat(null)}
              >
                <Card 
                  className={`bg-gradient-to-r from-${stat.color}-50 to-${stat.color}-100 
                    shadow-md hover:shadow-lg transition-all duration-300 border-none
                    ${hoveredStat === stat.key ? 'ring-2 ring-' + stat.color + '-400' : ''}`}
                >
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className='px-4 py-3'
                  >
                    <Statistic
                      title={
                        <div className='flex items-center space-x-2 text-gray-600 font-medium mb-2'>
                          <span className={`text-${stat.color}-600`}>{stat.icon}</span>
                          <span>{stat.title}</span>
                        </div>
                      }
                      value={stat.value}
                      formatter={formatter}
                      valueStyle={{ 
                        color: `var(--${stat.color}-600)`, 
                        fontSize: '24px', 
                        fontWeight: '600' 
                      }}
                    />
                  </motion.div>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Chart Section */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
          className='bg-white rounded-lg shadow-md p-4'
        >
          <h3 className='text-lg font-semibold mb-4 text-gray-700'>User Trends</h3>
          <ResponsiveContainer width="100%" height={300}>
            {renderChart()}
          </ResponsiveContainer>
        </motion.div>
      </div>
    </Card>
  );
}

export default UsersCard;
