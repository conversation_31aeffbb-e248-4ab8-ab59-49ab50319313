
import currentDateTime from '../lib/current.date.time.js';
import getDateAfterDuration from '../lib/get.date.after.duration.js';

/**
 * function to success response login user with JWT access and refresh token
 * @param {*} res node/express app res objects
 * @param {*} user API response in login user information
 * @param {*} maintenance API provide any kind of maintenance information
 */
const loginResponse = (res, user, maintenance) => {
  const accessToken = user.getJWTToken();
  const refreshToken = user.getJWTRefreshToken();

  // Calculate expiration date properly
  const cookieExpires = new Date();
  cookieExpires.setDate(cookieExpires.getDate() + (parseInt(process.env.JWT_TOKEN_COOKIE_EXPIRES) || 1));

  // options for cookie with proper expiration date
  const options = {
    expires: cookieExpires,
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production', // Use secure cookies in production
    sameSite: 'strict' // Prevent CSRF attacks
  };

  res
    .status(200)
    .cookie('AccessToken', accessToken, options)
    .json({
      result_code: 0,
      time: currentDateTime(),
      maintenance_info: maintenance || null,
      access_token: accessToken,
      refresh_token: refreshToken,
      access_token_expires: getDateAfterDuration(process.env.JWT_ACCESS_TOKEN_EXPIRES || '1d'),
      refresh_token_expires: getDateAfterDuration(process.env.JWT_REFRESH_TOKEN_EXPIRES || '7d'),
      result: {
        title: 'SUCCESS',
        message: 'User login successful',
        data: {
          id: user._id,
          userName: user.userName,
          fullName: user.fullName,
          email: user.email,
          phone: user.phone,
          avatar: process.env.APP_BASE_URL + user.avatar,
          gender: user.gender,
          dob: user.dob,
          address: user.address,
          role: user.role,
          verified: user.verified,
          status: user.status,
          createdAt: user.createdAt,
          updatedAt: user.updatedAt
        }
      }
    });
};

export { loginResponse };
