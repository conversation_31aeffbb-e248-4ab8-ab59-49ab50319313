import cron from 'node-cron';
import { updateRoomStatusForCompletedBookings } from '../utils/room.utils.js';
import logger from '../middleware/winston.logger.js';


// Initialize schedulers
export const initSchedulers = () => {
  // Run every day at midnight (00:00)
  cron.schedule('0 0 * * *', async () => {
    logger.info('Running scheduled task: Update room status for completed bookings');
    await updateRoomStatusForCompletedBookings();
  });
};