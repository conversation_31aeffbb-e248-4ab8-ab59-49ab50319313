
import fs from 'fs';
import appRoot from 'app-root-path';
import Room from '../models/room.model.js';
import User from '../models/user.model.js';
import logger from '../middleware/winston.logger.js';
import { errorResponse, successResponse } from '../config/app.response.js';
import MyQueryHelper from '../config/api.feature.js';
import Booking from '../models/booking.model.js';
import Maintenance from '../models/maintenance.model.js';


// Controller for create new room
const createRoom = async (req, res) => {
  try {
    const {
      room_number,
      floor,
      room_type,
      current_status,
      special_notes,
      is_active,
      maintenance_history
    } = req.body;

    // Validate required fields
    if (!room_number || !floor || !room_type) {
      if (req.files) {
        for (const element of req.files) {
          fs.unlink(`${appRoot}/public/uploads/rooms/${element.filename}`, (err) => {
            if (err) { logger.error(err); }
          });
        }
      }
      return res.status(400).json(errorResponse(
        1,
        'FAILED',
        'Room number, floor and room type are required'
      ));
    }

    // Check if room number already exists
    const existingRoom = await Room.findOne({ room_number });
    if (existingRoom) {
      if (req.files) {
        for (const element of req.files) {
          fs.unlink(`${appRoot}/public/uploads/rooms/${element.filename}`, (err) => {
            if (err) { logger.error(err); }
          });
        }
      }
      return res.status(409).json(errorResponse(
        9,
        'ALREADY EXISTS',
        'Room number already exists'
      ));
    }

    // Prepare room data
    const roomData = {
      room_number,
      floor: Number(floor),
      room_type,
      current_status: current_status || 'operational',
      special_notes: special_notes || '',
      is_active: is_active === 'true',
      created_by: req.user._id,
      maintenance_history: maintenance_history || []
    };

    // Handle image uploads
    if (req.files && req.files.length > 0) {
      roomData.room_images = req.files.map((file, index) => ({
        url: `/uploads/rooms/${file.filename}`,
        is_primary: index === 0,
        caption: `Room ${room_number} - Image ${index + 1}`
      }));
    }

    // Create room
    const room = await Room.create(roomData);

    // Populate the created room with all necessary details
    const populatedRoom = await Room.findById(room._id)
      .populate({
        path: 'created_by',
        select: 'userName fullName email role'
      })
      .populate({
        path: 'room_type',
        select: 'name base_price specifications amenities policies pricing'
      })
      .populate({
        path: 'maintenance_history.performed_by',
        select: 'userName fullName email role'
      });

    // Transform image URLs
    const transformedRoom = {
      ...populatedRoom.toObject(),
      room_images: populatedRoom.room_images?.map(image => ({
        ...image,
        url: process.env.APP_BASE_URL + image.url
      })) || []
    };

    return res.status(201).json(successResponse(
      0,
      'SUCCESS',
      'Room created successfully',
      transformedRoom
    ));

  } catch (error) {
    // Clean up uploaded files on error
    if (req.files) {
      for (const element of req.files) {
        fs.unlink(`${appRoot}/public/uploads/rooms/${element.filename}`, (err) => {
          if (err) { logger.error(err); }
        });
      }
    }

    logger.error('Room creation error:', error);
    return res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

// Get all rooms list controller
const getAllRoomsList = async (req, res) => {
  try {
    const totalCount = await Room.countDocuments();

    // Create base query with complete population
    const baseQuery = Room.find()
      .populate({
        path: 'room_type',
        select: 'name base_price specifications amenities policies pricing'
      })
      .populate({
        path: 'created_by',
        select: 'userName fullName email role'
      })
      .populate({
        path: 'maintenance_history.performed_by',
        select: 'userName fullName email role'
      })
      .select('-__v')
      .lean();

    // Apply filtering, searching, sorting and pagination
    const roomQuery = new MyQueryHelper(baseQuery, req.query)
      .search(['room_number', 'special_notes'])
      .filter()
      .sort()
      .paginate();

    const rooms = await roomQuery.query;

    if (!rooms?.length) {
      return res.status(404).json(errorResponse(
        4,
        'NOT_FOUND',
        'No rooms found'
      ));
    }

    // Get today's date at midnight for booking comparison
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    // Get tomorrow's date at midnight
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    // Get all bookings for today
    const todaysBookings = await Booking.find({
      booking_status: { $in: ['approved', 'checked-in'] },
      booking_dates: {
        $elemMatch: {
          $gte: today,
          $lt: tomorrow
        }
      }
    }).select('room_id booking_status check_in_time check_out_time').lean();
    
    // Create a map of room IDs to booking info for quick lookup
    const roomBookingsMap = {};
    todaysBookings.forEach(booking => {
      if (booking.room_id) {
        roomBookingsMap[booking.room_id.toString()] = {
          booking_status: booking.booking_status,
          check_in_time: booking.check_in_time,
          check_out_time: booking.check_out_time
        };
      }
    });
    
    // Get all maintenance for today
    const todaysMaintenance = await Maintenance.find({
      status: { $in: ['scheduled', 'in_progress'] },
      $or: [
        { start_date: { $lte: tomorrow, $gte: today } },
        { end_date: { $lte: tomorrow, $gte: today } },
        { $and: [{ start_date: { $lte: today } }, { end_date: { $gte: tomorrow } }] }
      ]
    }).select('room_id status').lean();
    
    // Create a map of room IDs to maintenance info
    const roomMaintenanceMap = {};
    todaysMaintenance.forEach(maintenance => {
      if (maintenance.room_id) {
        roomMaintenanceMap[maintenance.room_id.toString()] = {
          maintenance_status: maintenance.status
        };
      }
    });

    // Transform room images URLs, format dates, and add today's occupancy info
    const transformedRooms = rooms.map(room => {
      const roomId = room._id.toString();
      const bookingInfo = roomBookingsMap[roomId];
      const maintenanceInfo = roomMaintenanceMap[roomId];
      
      // Determine today's occupancy status
      let todayOccupancyStatus = 'available';
      let occupancyDetails = null;
      
      if (room.current_status === 'maintenance' || room.current_status === 'out_of_order') {
        todayOccupancyStatus = room.current_status;
      } else if (maintenanceInfo) {
        todayOccupancyStatus = 'maintenance';
        occupancyDetails = {
          maintenance_status: maintenanceInfo.maintenance_status
        };
      } else if (bookingInfo) {
        todayOccupancyStatus = bookingInfo.booking_status === 'checked-in' ? 'occupied' : 'reserved';
        occupancyDetails = {
          check_in_time: bookingInfo.check_in_time,
          check_out_time: bookingInfo.check_out_time
        };
      } else if (room.current_status !== 'available') {
        todayOccupancyStatus = room.current_status;
      }
      
      return {
        ...room,
        room_images: room.room_images?.map(image => ({
          ...image,
          url: process.env.APP_BASE_URL + image.url
        })) || [],
        maintenance_history: room.maintenance_history?.map(history => ({
          ...history,
          start_date: history.start_date ? new Date(history.start_date).toISOString() : null,
          end_date: history.end_date ? new Date(history.end_date).toISOString() : null
        })) || [],
        today_occupancy: {
          status: todayOccupancyStatus,
          details: occupancyDetails
        }
      };
    });

    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const totalPages = Math.ceil(totalCount / limit);

    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Rooms list retrieved successfully',
      {
        rows: transformedRooms,
        total_rows: totalCount,
        current_page: page,
        total_pages: totalPages,
        has_previous: page > 1,
        has_next: page < totalPages,
        filters: {
          featured: req.query.featured_room || 'all',
          status: req.query.current_status || 'all',
          floor: req.query.floor || 'all',
          room_type: req.query.room_type || 'all'
        },
        today_date: today.toISOString().split('T')[0]
      }
    ));
  } catch (error) {
    logger.error('Get rooms list error:', error);
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

// Get room by ID or room number controller
const getRoomByIdOrSlugName = async (req, res) => {
  try {
    const query = /^[0-9a-fA-F]{24}$/.test(req.params.id) 
      ? { _id: req.params.id }
      : { room_number: req.params.id };

    const room = await Room.findOne(query)
      .populate({
        path: 'room_type',
        select: 'name base_price specifications amenities policies pricing maintenance created_by',
        populate: {
          path: 'created_by',
          select: 'userName fullName email role'
        }
      })
      .populate({
        path: 'created_by',
        select: 'userName fullName email role'
      })
      .populate({
        path: 'maintenance_history.performed_by',
        select: 'userName fullName email role'
      })
      .lean();

    if (!room) {
      return res.status(404).json(errorResponse(
        4,
        'UNKNOWN ACCESS',
        'Room does not exist'
      ));
    }

    // Transform room data
    const transformedRoom = {
      ...room,
      room_images: room.room_images?.map(image => ({
        ...image,
        url: process.env.APP_BASE_URL + image.url
      })) || [],
      maintenance_history: room.maintenance_history?.map(history => ({
        ...history,
        start_date: history.start_date ? new Date(history.start_date).toISOString() : null,
        end_date: history.end_date ? new Date(history.end_date).toISOString() : null
      })) || [],
      room_type: room.room_type ? {
        ...room.room_type,
        specifications: {
          ...room.room_type.specifications,
          room_size: {
            value: room.room_type.specifications?.room_size?.value || 0,
            unit: room.room_type.specifications?.room_size?.unit || 'sqft'
          },
          max_occupancy: {
            adults: room.room_type.specifications?.max_occupancy?.adults || 0,
            children: room.room_type.specifications?.max_occupancy?.children || 0
          },
          bed_configuration: room.room_type.specifications?.bed_configuration || []
        },
        amenities: {
          basic: room.room_type.amenities?.basic || [],
          additional: room.room_type.amenities?.additional || []
        },
        policies: {
          allow_pets: room.room_type.policies?.allow_pets || false,
          provide_breakfast: room.room_type.policies?.provide_breakfast || false,
          smoking_allowed: room.room_type.policies?.smoking_allowed || false,
          cancellation_policy: room.room_type.policies?.cancellation_policy || {}
        },
        pricing: room.room_type.pricing || {},
        maintenance: {
          last_renovation: room.room_type.maintenance?.last_renovation,
          next_maintenance: room.room_type.maintenance?.next_maintenance,
          maintenance_notes: room.room_type.maintenance?.maintenance_notes
        },
        created_by: room.room_type.created_by || null
      } : null
    };

    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Room details retrieved successfully',
      transformedRoom
    ));
  } catch (error) {
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

// Edit room by admin controller
const editRoomByAdmin = async (req, res) => {
  try {
    const room = await Room.findById(req.params.id);
    if (!room) {
      if (req.files) {
        for (const element of req.files) {
          fs.unlink(`${appRoot}/public/uploads/rooms/${element.filename}`, (err) => {
            if (err) { logger.error(err); }
          });
        }
      }
      return res.status(404).json(errorResponse(
        4,
        'UNKNOWN ACCESS',
        'Room does not exist'
      ));
    }

    const updateData = {
      ...req.body,
      is_active: req.body.is_active === 'true',
      floor: Number(req.body.floor)
    };

    // Handle maintenance history updates
    if (req.body.maintenance_history) {
      try {
        updateData.maintenance_history = JSON.parse(req.body.maintenance_history).map(history => ({
          ...history,
          start_date: history.start_date ? new Date(history.start_date) : null,
          end_date: history.end_date ? new Date(history.end_date) : null
        }));
      } catch (error) {
        return res.status(400).json(errorResponse(
          1,
          'FAILED',
          'Invalid maintenance history format'
        ));
      }
    }

    // Handle image updates
    if (req.files?.length > 0) {
      // Delete old images
      if (room.room_images?.length) {
        for (const image of room.room_images) {
          fs.unlink(`${appRoot}/public${image.url}`, (err) => {
            if (err) { logger.error(err); }
          });
        }
      }
      
      // Add new images
      updateData.room_images = req.files.map((file, index) => ({
        url: `/uploads/rooms/${file.filename}`,
        is_primary: index === 0,
        caption: `Room ${room.room_number} - Image ${index + 1}`
      }));
    }

    // Update room with validation
    const updatedRoom = await Room.findByIdAndUpdate(
      req.params.id,
      updateData,
      { 
        new: true,
        runValidators: true
      }
    )
    .populate({
      path: 'room_type',
      select: 'name base_price specifications amenities policies pricing'
    })
    .populate({
      path: 'created_by',
      select: 'userName fullName email role'
    })
    .populate({
      path: 'maintenance_history.performed_by',
      select: 'userName fullName email role'
    })
    .lean();

    // Transform updated room data
    const transformedRoom = {
      ...updatedRoom,
      room_images: updatedRoom.room_images?.map(image => ({
        ...image,
        url: process.env.APP_BASE_URL + image.url
      })) || [],
      maintenance_history: updatedRoom.maintenance_history?.map(history => ({
        ...history,
        start_date: history.start_date ? new Date(history.start_date).toISOString() : null,
        end_date: history.end_date ? new Date(history.end_date).toISOString() : null
      })) || []
    };

    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Room updated successfully',
      transformedRoom
    ));
  } catch (error) {
    if (req.files) {
      for (const element of req.files) {
        fs.unlink(`${appRoot}/public/uploads/rooms/${element.filename}`, (err) => {
          if (err) { logger.error(err); }
        });
      }
    }

    logger.error('Room update error:', error);
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

// Delete room by ID controller
const deleteRoomById = async (req, res) => {
  try {
    const room = await Room.findById(req.params.id);

    if (!room) {
      return res.status(404).json(errorResponse(
        4,
        'UNKNOWN ACCESS',
        'Room does not exist'
      ));
    }

    // Check for active bookings
    const activeBookings = await Booking.find({
      room_id: room._id,
      booking_status: { $in: ['pending', 'confirmed', 'checked_in'] }
    });

    if (activeBookings.length > 0) {
      return res.status(400).json(errorResponse(
        1,
        'FAILED',
        'Cannot delete room with active bookings'
      ));
    }

    // Delete room images
    if (room.room_images?.length) {
      for (const image of room.room_images) {
        const imagePath = `${appRoot}/public${image.url}`;
        try {
          if (fs.existsSync(imagePath)) {
            fs.unlinkSync(imagePath);
          }
        } catch (err) {
          logger.error(`Error deleting image ${imagePath}:`, err);
        }
      }
    }

    await Room.findByIdAndDelete(room._id);

    return res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Room deleted successfully'
    ));

  } catch (error) {
    logger.error('Delete room error:', error);
    return res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message || 'Failed to delete room'
    ));
  }
};

// Get featured rooms list controller
const getFeaturedRoomsList = async (req, res) => {
  try {
    // Get total count of featured rooms
    const totalCount = await Room.countDocuments({ featured_room: true });

    // Create base query for featured rooms with population
    const baseQuery = Room.find({ featured_room: true })
      .populate({
        path: 'room_type',
        select: 'name base_price'
      })
      .populate({
        path: 'created_by',
        select: 'userName fullName email role'
      })
      .lean(); // Convert Mongoose documents to plain JavaScript objects

    // Apply filtering, searching, sorting and pagination
    const roomQuery = new MyQueryHelper(baseQuery, req.query)
      .search('room_number')
      .filter()
      .sort()
      .paginate();

    const rooms = await roomQuery.query;

    if (!rooms) {
      return res.status(404).json(errorResponse(
        4,
        'NOT_FOUND',
        'No featured rooms found'
      ));
    }

    // Calculate pagination values
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const totalPages = Math.ceil(totalCount / limit);

    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Featured rooms list retrieved successfully',
      {
        rows: rooms,
        total_rows: totalCount,
        current_page: page,
        total_pages: totalPages,
        has_previous: page > 1,
        has_next: page < totalPages
      }
    ));
  } catch (error) {
    logger.error('Get featured rooms list error:', error);
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

// Get room booking calendar dates
const getRoomBookingCalendar = async (req, res) => {
  try {
    const { roomId } = req.params;
    const { year, month } = req.query;
    
    // Validate room ID
    if (!roomId) {
      return res.status(400).json(errorResponse(
        1,
        'FAILED',
        'Room ID is required'
      ));
    }
    
    // Find the room
    const room = await Room.findById(roomId);
    if (!room) {
      return res.status(404).json(errorResponse(
        4,
        'UNKNOWN ACCESS',
        'Room does not exist'
      ));
    }
    
    // Set date range for calendar
    let startDate, endDate;
    
    if (year && month) {
      // If year and month provided, get calendar for that month
      const numYear = parseInt(year);
      const numMonth = parseInt(month) - 1; // JS months are 0-indexed
      
      if (isNaN(numYear) || isNaN(numMonth) || numMonth < 0 || numMonth > 11) {
        return res.status(400).json(errorResponse(
          1,
          'FAILED',
          'Invalid year or month'
        ));
      }
      
      startDate = new Date(numYear, numMonth, 1);
      endDate = new Date(numYear, numMonth + 1, 0); // Last day of month
    } else {
      // Default to current month and next 2 months
      startDate = new Date();
      startDate.setDate(1); // First day of current month
      startDate.setHours(0, 0, 0, 0);
      
      endDate = new Date(startDate);
      endDate.setMonth(endDate.getMonth() + 2); // 2 months from start
      endDate.setDate(0); // Last day of the last month
    }
    
    // Ensure dates are set to midnight for proper comparison
    startDate.setHours(0, 0, 0, 0);
    endDate.setHours(23, 59, 59, 999);
    
    logger.info(`Fetching calendar for room ${roomId} from ${startDate.toISOString()} to ${endDate.toISOString()}`);
    
    // Get all bookings for this room in the date range
    const bookings = await Booking.find({
      room_id: roomId,
      booking_status: { $in: ['pending', 'approved', 'checked-in'] },
      booking_dates: { $elemMatch: { $gte: startDate, $lte: endDate } }
    }).select('booking_dates booking_status _id guest_name').lean();
    
    logger.info(`Found ${bookings.length} bookings for room ${roomId}`);
    
    // Get all maintenance for this room in the date range
    const maintenances = await Maintenance.find({
      room_id: roomId,
      status: { $in: ['scheduled', 'in_progress'] },
      $or: [
        { start_date: { $lte: endDate, $gte: startDate } },
        { end_date: { $lte: endDate, $gte: startDate } },
        { $and: [{ start_date: { $lte: startDate } }, { end_date: { $gte: endDate } }] }
      ]
    }).select('start_date end_date status _id description').lean();
    
    logger.info(`Found ${maintenances.length} maintenance records for room ${roomId}`);
    
    // Generate calendar data
    const calendarData = [];
    const currentDate = new Date(startDate);
    
    while (currentDate <= endDate) {
      const dateStr = currentDate.toISOString().split('T')[0];
      const dateObj = new Date(currentDate);
      
      // Check if date is booked
      const booking = bookings.find(booking => 
        booking.booking_dates && booking.booking_dates.some(bookingDate => {
          const bookingDateStr = new Date(bookingDate).toISOString().split('T')[0];
          return bookingDateStr === dateStr;
        })
      );
      
      // Check if date is under maintenance
      const maintenance = maintenances.find(maintenance => {
        if (!maintenance.start_date || !maintenance.end_date) return false;
        const maintenanceStart = new Date(maintenance.start_date);
        const maintenanceEnd = new Date(maintenance.end_date);
        maintenanceStart.setHours(0, 0, 0, 0);
        maintenanceEnd.setHours(23, 59, 59, 999);
        return dateObj >= maintenanceStart && dateObj <= maintenanceEnd;
      });
      
      calendarData.push({
        date: dateStr,
        day: currentDate.getDate(),
        month: currentDate.getMonth() + 1,
        year: currentDate.getFullYear(),
        available: !booking && !maintenance && room.is_active,
        status: booking ? 'booked' : (maintenance ? 'maintenance' : (room.is_active ? 'available' : 'inactive')),
        booking_id: booking ? booking._id : null,
        booking_status: booking ? booking.booking_status : null,
        guest_name: booking ? booking.guest_name : null,
        maintenance_id: maintenance ? maintenance._id : null,
        maintenance_description: maintenance ? maintenance.description : null
      });
      
      // Move to next day
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    // Send response with detailed information
    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Room booking calendar retrieved successfully',
      {
        room: {
          id: room._id,
          room_number: room.room_number,
          is_active: room.is_active,
          current_status: room.current_status,
          floor: room.floor,
          room_type: room.room_type
        },
        year: parseInt(year) || startDate.getFullYear(),
        month: parseInt(month) || startDate.getMonth() + 1,
        calendar: calendarData,
        date_range: {
          start: startDate.toISOString().split('T')[0],
          end: endDate.toISOString().split('T')[0]
        }
      }
    ));
  } catch (error) {
    // Detailed error logging
    logger.error('Error getting room booking calendar:', error);
    logger.error('Error details:', JSON.stringify({
      message: error.message,
      stack: error.stack,
      name: error.name
    }));
    
    // Check for specific validation errors
    if (error.name === 'ValidationError') {
      const missingFields = Object.keys(error.errors).map(field => ({
        field,
        message: error.errors[field].message
      }));
      
      return res.status(400).json(errorResponse(
        1,
        'VALIDATION ERROR',
        'Missing required fields',
        { missing_fields: missingFields }
      ));
    }
    
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

export {
  createRoom,
  getAllRoomsList,
  getRoomByIdOrSlugName,
  editRoomByAdmin,
  deleteRoomById,
  getFeaturedRoomsList,
  getRoomBookingCalendar
};
