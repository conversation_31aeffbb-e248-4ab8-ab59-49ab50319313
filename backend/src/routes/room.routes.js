import express from 'express';
import roomImageUpload from '../middleware/room.image.upload.js';
import { isAuthenticatedUser, isAdmin } from '../middleware/app.authentication.js';
import {
  createRoom,
  getAllRoomsList,
  getRoomByIdOrSlugName,
  editRoomByAdmin,
  deleteRoomById,
  getFeaturedRoomsList,
  getRoomBookingCalendar
} from '../controllers/room.controllers.js';

const router = express.Router();

// route for create new room
router.route('/create-room').post(
  isAuthenticatedUser, 
  isAdmin, 
  roomImageUpload.array('room_images', 5), 
  createRoom
);

// routes for get all, single and featured rooms list
router.route('/all-rooms-list').get(getAllRoomsList);
router.route('/get-room-by-id-or-slug-name/:id').get(getRoomByIdOrSlugName);
router.route('/featured-rooms-list').get(getFeaturedRoomsList);

// routes for edit and delete room by admin
router.route('/edit-room/:id').put(
  isAuthenticatedUser, 
  isAdmin, 
  roomImageUpload.array('room_images', 5), 
  editRoomByAdmin
);
router.route('/delete-room/:id').delete(isAuthenticatedUser, isAdmin, deleteRoomById);

// route for room booking calendar
router.route('/room-booking-calendar/:roomId').get(getRoomBookingCalendar);

export default router;
