
const getDateAfterDuration = (duration) => {
  try {
    const currentDate = new Date();
    
    // Default to 1 day if duration is invalid
    if (!duration || typeof duration !== 'string') {
      const tomorrow = new Date(currentDate);
      tomorrow.setDate(tomorrow.getDate() + 1);
      return tomorrow;
    }
    
    const parts = duration.match(/(\d+)([dhms])/);
    if (!parts) {
      const tomorrow = new Date(currentDate);
      tomorrow.setDate(tomorrow.getDate() + 1);
      return tomorrow;
    }
  
    const amount = parseInt(parts[1], 10);
    const unit = parts[2];
    
    // Handle invalid amount
    if (isNaN(amount) || amount <= 0) {
      const tomorrow = new Date(currentDate);
      tomorrow.setDate(tomorrow.getDate() + 1);
      return tomorrow;
    }
    
    // Calculate future date based on unit
    const futureDate = new Date(currentDate);
    
    switch (unit) {
      case 'd':
        futureDate.setDate(futureDate.getDate() + amount);
        break;
      case 'h':
        futureDate.setHours(futureDate.getHours() + amount);
        break;
      case 'm':
        futureDate.setMinutes(futureDate.getMinutes() + amount);
        break;
      case 's':
        futureDate.setSeconds(futureDate.getSeconds() + amount);
        break;
      default:
        futureDate.setDate(futureDate.getDate() + 1); // Default to 1 day
    }
    
    return futureDate;
  } catch (error) {
    console.error('Error in getDateAfterDuration:', error);
    // Return tomorrow as fallback
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return tomorrow;
  }
};

export default getDateAfterDuration;
