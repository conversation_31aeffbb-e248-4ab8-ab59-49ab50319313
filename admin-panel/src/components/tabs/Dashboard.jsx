import { Result, Card, Skeleton, Tooltip, Badge, Select, Tabs, Row, Col, Statistic } from 'antd';
import React, { useState, useEffect } from 'react';
import useFetchData from '../../hooks/useFetchData';
import { 
  Activity, TrendingUp, AlertCircle, Check, Tool, 
  DollarSign, Users, Calendar, Clock, Percent,
  Home, User, UserCheck, UserX, Layers, 
  <PERSON>hart as PieChartIcon, BarChart2, ArrowUp, ArrowDown
} from 'react-feather';
import { 
  ResponsiveContainer, AreaChart, Area, BarChart, Bar, 
  XAxis, YAxis, CartesianGrid, Tooltip as RechartsTooltip, 
  Legend, PieChart, Pie, Cell, LineChart, Line,
  RadialBarChart, RadialBar, Treemap
} from 'recharts';
import { motion } from 'framer-motion';

function Dashboard() {
  const [loading, error, response] = useFetchData('/api/v1/dashboard');
  const [timeRange, setTimeRange] = useState('week'); // 'day', 'week', 'month'
  const [activeTab, setActiveTab] = useState('1');

  // Colors for the charts
  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d', '#ffc658', '#ff7300'];
  const ROOM_COLORS = {
    operational: '#00C49F',
    maintenance: '#FFBB28',
    out_of_service: '#FF8042',
    reserved: '#0088FE',
    occupied: '#8884d8'
  };
  const USER_COLORS = {
    active: '#00C49F',
    inactive: '#FFBB28',
    blocked: '#FF8042'
  };
  const BOOKING_COLORS = {
    pending: '#FFBB28',
    approved: '#00C49F',
    checked_in: '#0088FE',
    completed: '#82ca9d',
    cancelled: '#FF8042'
  };

  // Format booking trends data for charts
  const formatBookingTrends = () => {
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    
    return (response?.data?.bookings?.trends || []).map(item => {
      // Check if item has the expected structure
      if (!item || !item._id || typeof item._id.month !== 'number' || !item._id.year) {
        console.warn('Invalid booking trend item format:', item);
        return {
          name: 'Unknown',
          month: 'Unknown',
          value: item?.count || 0,
          count: item?.count || 0,
          revenue: item?.revenue || 0
        };
      }
      
      return {
        name: `${monthNames[item._id.month - 1]} ${item._id.year}`,
        month: `${monthNames[item._id.month - 1]} ${item._id.year}`,
        value: item.count,
        count: item.count,
        revenue: item.revenue || 0
      };
    });
  };

  // Format user trends data for charts
  const formatUserTrends = () => {
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    
    return (response?.data?.users?.trends || []).map(item => {
      // Check if item has the expected structure
      if (!item || !item._id || typeof item._id.month !== 'number' || !item._id.year) {
        console.warn('Invalid user trend item format:', item);
        return {
          name: 'Unknown',
          month: 'Unknown',
          value: item?.count || 0,
          count: item?.count || 0
        };
      }
      
      return {
        name: `${monthNames[item._id.month - 1]} ${item._id.year}`,
        month: `${monthNames[item._id.month - 1]} ${item._id.year}`,
        value: item.count,
        count: item.count
      };
    });
  };

  // Format revenue trends data for charts
  const formatRevenueTrends = () => {
    const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    
    return (response?.data?.revenue?.trends || []).map(item => {
      // Check if item has the expected structure
      if (!item || !item._id || typeof item._id.month !== 'number' || !item._id.year) {
        console.warn('Invalid revenue trend item format:', item);
        return {
          name: 'Unknown',
          month: 'Unknown',
          value: item?.amount || 0,
          revenue: item?.amount || 0
        };
      }
      
      return {
        name: `${monthNames[item._id.month - 1]} ${item._id.year}`,
        month: `${monthNames[item._id.month - 1]} ${item._id.year}`,
        value: item.amount,
        revenue: item.amount || 0
      };
    });
  };

  // Function to render the revenue trend chart
  const renderRevenueChart = () => {
    const data = formatRevenueTrends();
    
    return (
      <ResponsiveContainer width="100%" height={250}>
        <AreaChart data={data} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
          <defs>
            <linearGradient id="colorRevenue" x1="0" y1="0" x2="0" y2="1">
              <stop offset="5%" stopColor="#8884d8" stopOpacity={0.8}/>
              <stop offset="95%" stopColor="#8884d8" stopOpacity={0}/>
            </linearGradient>
          </defs>
          <XAxis dataKey="name" />
          <YAxis />
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <RechartsTooltip 
            formatter={(value) => [`$${value}`, 'Revenue']}
            labelFormatter={(label) => `Date: ${label}`}
          />
          <Area 
            type="monotone" 
            dataKey="value" 
            stroke="#8884d8" 
            fillOpacity={1} 
            fill="url(#colorRevenue)" 
          />
        </AreaChart>
      </ResponsiveContainer>
    );
  };

  // Function to render the booking trend chart
  const renderBookingTrendChart = () => {
    const data = formatBookingTrends();
    
    return (
      <ResponsiveContainer width="100%" height={250}>
        <LineChart data={data} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis dataKey="name" />
          <YAxis />
          <RechartsTooltip 
            formatter={(value) => [`${value}`, 'Bookings']}
          />
          <Line 
            type="monotone" 
            dataKey="value" 
            stroke="#4f46e5" 
            strokeWidth={2}
            dot={{ r: 4 }}
            activeDot={{ r: 8 }}
          />
        </LineChart>
      </ResponsiveContainer>
    );
  };

  // Function to render the user trend chart
  const renderUserTrendChart = () => {
    const data = formatUserTrends();
    
    return (
      <ResponsiveContainer width="100%" height={250}>
        <BarChart data={data} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis dataKey="name" />
          <YAxis />
          <RechartsTooltip 
            formatter={(value) => [`${value}`, 'New Users']}
          />
          <Bar dataKey="value" fill="#00C49F" radius={[4, 4, 0, 0]} />
        </BarChart>
      </ResponsiveContainer>
    );
  };

  // Function to render the room status chart
  const renderRoomStatusChart = () => {
    const roomData = [
      { name: 'Operational', value: response?.data?.rooms?.operational || 0, color: ROOM_COLORS.operational },
      { name: 'Maintenance', value: response?.data?.rooms?.maintenance || 0, color: ROOM_COLORS.maintenance },
      { name: 'Out of Service', value: response?.data?.rooms?.out_of_service || 0, color: ROOM_COLORS.out_of_service },
      { name: 'Reserved', value: response?.data?.rooms?.reserved || 0, color: ROOM_COLORS.reserved },
      { name: 'Occupied', value: response?.data?.rooms?.occupied || 0, color: ROOM_COLORS.occupied }
    ];
    
    return (
      <ResponsiveContainer width="100%" height={250}>
        <PieChart>
          <Pie
            data={roomData}
            cx="50%"
            cy="50%"
            innerRadius={60}
            outerRadius={80}
            fill="#8884d8"
            paddingAngle={5}
            dataKey="value"
            label={({name, percent}) => `${name}: ${(percent * 100).toFixed(0)}%`}
          >
            {roomData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color || COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <RechartsTooltip 
            formatter={(value, name) => [`${value} rooms`, name]}
          />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    );
  };

  // Function to render the user status chart
  const renderUserStatusChart = () => {
    const userData = [
      { name: 'Active', value: response?.data?.users?.active || 0, color: USER_COLORS.active },
      { name: 'Inactive', value: response?.data?.users?.inactive || 0, color: USER_COLORS.inactive },
      { name: 'Blocked', value: response?.data?.users?.blocked || 0, color: USER_COLORS.blocked }
    ];
    
    return (
      <ResponsiveContainer width="100%" height={250}>
        <PieChart>
          <Pie
            data={userData}
            cx="50%"
            cy="50%"
            innerRadius={60}
            outerRadius={80}
            fill="#8884d8"
            paddingAngle={5}
            dataKey="value"
            label={({name, percent}) => `${name}: ${(percent * 100).toFixed(0)}%`}
          >
            {userData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color || COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <RechartsTooltip 
            formatter={(value, name) => [`${value} users`, name]}
          />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    );
  };

  // Function to render the booking status chart
  const renderBookingStatusChart = () => {
    const bookingData = [
      { name: 'Pending', value: response?.data?.bookings?.pending || 0, color: BOOKING_COLORS.pending },
      { name: 'Approved', value: response?.data?.bookings?.approved || 0, color: BOOKING_COLORS.approved },
      { name: 'Checked In', value: response?.data?.bookings?.checked_in || 0, color: BOOKING_COLORS.checked_in },
      { name: 'Completed', value: response?.data?.bookings?.completed || 0, color: BOOKING_COLORS.completed },
      { name: 'Cancelled', value: response?.data?.bookings?.cancelled || 0, color: BOOKING_COLORS.cancelled }
    ];
    
    return (
      <ResponsiveContainer width="100%" height={250}>
        <PieChart>
          <Pie
            data={bookingData}
            cx="50%"
            cy="50%"
            innerRadius={60}
            outerRadius={80}
            fill="#8884d8"
            paddingAngle={5}
            dataKey="value"
            label={({name, percent}) => `${name}: ${(percent * 100).toFixed(0)}%`}
          >
            {bookingData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color || COLORS[index % COLORS.length]} />
            ))}
          </Pie>
          <RechartsTooltip 
            formatter={(value, name) => [`${value} bookings`, name]}
          />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    );
  };

  // Function to render rooms by floor chart
  const renderRoomsByFloorChart = () => {
    const floorData = response?.data?.rooms?.by_floor || [];
    
    return (
      <ResponsiveContainer width="100%" height={250}>
        <BarChart data={floorData} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
          <CartesianGrid strokeDasharray="3 3" vertical={false} />
          <XAxis dataKey="_id" label={{ value: 'Floor', position: 'insideBottom', offset: -5 }} />
          <YAxis label={{ value: 'Rooms', angle: -90, position: 'insideLeft', offset: -5 }} />
          <RechartsTooltip 
            formatter={(value) => [`${value} rooms`, 'Count']}
          />
          <Bar dataKey="count" fill="#8884d8" radius={[4, 4, 0, 0]}>
            {floorData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    );
  };

  // Function to render rooms by type chart
  const renderRoomsByTypeChart = () => {
    const typeData = response?.data?.rooms?.by_type || [];
    
    return (
      <ResponsiveContainer width="100%" height={250}>
        <BarChart data={typeData} layout="vertical" margin={{ top: 10, right: 30, left: 50, bottom: 0 }}>
          <CartesianGrid strokeDasharray="3 3" horizontal={false} />
          <XAxis type="number" />
          <YAxis dataKey="_id" type="category" />
          <RechartsTooltip 
            formatter={(value) => [`${value} rooms`, 'Count']}
          />
          <Bar dataKey="count" fill="#82ca9d" radius={[0, 4, 4, 0]}>
            {typeData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </Bar>
        </BarChart>
      </ResponsiveContainer>
    );
  };

  // Function to render users by role chart
  const renderUsersByRoleChart = () => {
    const roleData = response?.data?.users?.by_role || [];
    
    return (
      <ResponsiveContainer width="100%" height={250}>
        <RadialBarChart 
          cx="50%" 
          cy="50%" 
          innerRadius="20%" 
          outerRadius="80%" 
          barSize={20} 
          data={roleData}
        >
          <RadialBar
            minAngle={15}
            label={{ position: 'insideStart', fill: '#fff' }}
            background
            clockWise
            dataKey="count"
          >
            {roleData.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
            ))}
          </RadialBar>
          <Legend 
            iconSize={10} 
            layout="vertical" 
            verticalAlign="middle" 
            align="right"
            formatter={(value, entry) => entry.payload._id}
          />
          <RechartsTooltip
            formatter={(value, name, props) => [`${value} users`, props.payload._id]}
          />
        </RadialBarChart>
      </ResponsiveContainer>
    );
  };

  return (
    <div className="min-h-full bg-gray-50">
      {/* Header Section */}
      <div className="bg-white shadow-sm">
        <div className="px-4 py-6 sm:px-6 lg:px-8">
          <div className="flex items-center space-x-3">
            <Activity className="h-8 w-8 text-indigo-600" />
            <h1 className="text-2xl font-semibold text-gray-900">
              Hotel Dashboard
            </h1>
          </div>
          <p className="mt-2 text-sm text-gray-600">
            Welcome to TTS Nexus — Monitor your hotel's performance and manage resources efficiently
          </p>
        </div>
      </div>

      {error ? (
        <div className="mt-8">
          <Result
            title="Failed to fetch dashboard data"
            subTitle={error}
            status="error"
          />
        </div>
      ) : (
        <div className="px-4 py-8 sm:px-6 lg:px-8">
          {/* Key Metrics Section */}
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
            {/* Revenue Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Card className="shadow-md hover:shadow-lg transition-all duration-300 border-none">
                <Skeleton loading={loading} active paragraph={{ rows: 1 }}>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-500 text-sm font-medium">Total Revenue</p>
                      <h3 className="text-2xl font-bold mt-1 text-indigo-600">
                        ${response?.data?.total_revenue || '0'}
                      </h3>
                    </div>
                    <div className="bg-indigo-100 p-3 rounded-full">
                      <DollarSign className="h-6 w-6 text-indigo-600" />
                    </div>
                  </div>
                </Skeleton>
              </Card>
            </motion.div>

            {/* Occupancy Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 }}
            >
              <Card className="shadow-md hover:shadow-lg transition-all duration-300 border-none">
                <Skeleton loading={loading} active paragraph={{ rows: 1 }}>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-500 text-sm font-medium">Occupancy Rate</p>
                      <h3 className="text-2xl font-bold mt-1 text-emerald-600">
                        {response?.data?.occupancy_rate || '0'}%
                      </h3>
                    </div>
                    <div className="bg-emerald-100 p-3 rounded-full">
                      <Percent className="h-6 w-6 text-emerald-600" />
                    </div>
                  </div>
                </Skeleton>
              </Card>
            </motion.div>

            {/* Total Rooms Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.2 }}
            >
              <Card className="shadow-md hover:shadow-lg transition-all duration-300 border-none">
                <Skeleton loading={loading} active paragraph={{ rows: 1 }}>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-500 text-sm font-medium">Total Rooms</p>
                      <h3 className="text-2xl font-bold mt-1 text-blue-600">
                        {response?.data?.rooms?.total || '0'}
                      </h3>
                      <p className="text-xs text-gray-500 mt-1">
                        {response?.data?.rooms?.operational || '0'} operational
                      </p>
                    </div>
                    <div className="bg-blue-100 p-3 rounded-full">
                      <Home className="h-6 w-6 text-blue-600" />
                    </div>
                  </div>
                </Skeleton>
              </Card>
            </motion.div>

            {/* Active Bookings Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.3 }}
            >
              <Card className="shadow-md hover:shadow-lg transition-all duration-300 border-none">
                <Skeleton loading={loading} active paragraph={{ rows: 1 }}>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-500 text-sm font-medium">Active Bookings</p>
                      <h3 className="text-2xl font-bold mt-1 text-amber-600">
                        {response?.data?.active_bookings || '0'}
                      </h3>
                      <p className="text-xs text-gray-500 mt-1">
                        <span className={response?.data?.booking_growth > 0 ? "text-green-500" : "text-red-500"}>
                          {response?.data?.booking_growth > 0 ? "↑" : "↓"} {Math.abs(response?.data?.booking_growth || 0)}%
                        </span> booking rate
                      </p>
                    </div>
                    <div className="bg-amber-100 p-3 rounded-full">
                      <Calendar className="h-6 w-6 text-amber-600" />
                    </div>
                  </div>
                </Skeleton>
              </Card>
            </motion.div>
          </div>

          {/* Secondary Metrics Section */}
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
            {/* Today's Bookings Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.4 }}
            >
              <Card className="shadow-md hover:shadow-lg transition-all duration-300 border-none">
                <Skeleton loading={loading} active paragraph={{ rows: 1 }}>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-500 text-sm font-medium">Today's Bookings</p>
                      <h3 className="text-2xl font-bold mt-1 text-purple-600">
                        {response?.data?.today_bookings || '0'}
                      </h3>
                    </div>
                    <div className="bg-purple-100 p-3 rounded-full">
                      <Clock className="h-6 w-6 text-purple-600" />
                    </div>
                  </div>
                </Skeleton>
              </Card>
            </motion.div>

            {/* Total Users Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.5 }}
            >
              <Card className="shadow-md hover:shadow-lg transition-all duration-300 border-none">
                <Skeleton loading={loading} active paragraph={{ rows: 1 }}>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-500 text-sm font-medium">Total Users</p>
                      <h3 className="text-2xl font-bold mt-1 text-cyan-600">
                        {response?.data?.users?.total || '0'}
                      </h3>
                      <p className="text-xs text-gray-500 mt-1">
                        {response?.data?.users?.active || '0'} active users
                      </p>
                    </div>
                    <div className="bg-cyan-100 p-3 rounded-full">
                      <Users className="h-6 w-6 text-cyan-600" />
                    </div>
                  </div>
                </Skeleton>
              </Card>
            </motion.div>

            {/* Reserved Rooms Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.6 }}
            >
              <Card className="shadow-md hover:shadow-lg transition-all duration-300 border-none">
                <Skeleton loading={loading} active paragraph={{ rows: 1 }}>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-500 text-sm font-medium">Reserved Rooms</p>
                      <h3 className="text-2xl font-bold mt-1 text-pink-600">
                        {response?.data?.rooms?.reserved || '0'}
                      </h3>
                    </div>
                    <div className="bg-pink-100 p-3 rounded-full">
                      <Layers className="h-6 w-6 text-pink-600" />
                    </div>
                  </div>
                </Skeleton>
              </Card>
            </motion.div>

            {/* Occupied Rooms Card */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.7 }}
            >
              <Card className="shadow-md hover:shadow-lg transition-all duration-300 border-none">
                <Skeleton loading={loading} active paragraph={{ rows: 1 }}>
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-500 text-sm font-medium">Occupied Rooms</p>
                      <h3 className="text-2xl font-bold mt-1 text-orange-600">
                        {response?.data?.rooms?.occupied || '0'}
                      </h3>
                    </div>
                    <div className="bg-orange-100 p-3 rounded-full">
                      <Home className="h-6 w-6 text-orange-600" />
                    </div>
                  </div>
                </Skeleton>
              </Card>
            </motion.div>
          </div>

          {/* Tabs for different dashboard views */}
          <Tabs 
            defaultActiveKey="1" 
            onChange={setActiveTab}
            className="dashboard-tabs"
            tabBarStyle={{ marginBottom: '24px' }}
            size="large"
            type="card"
            items={[
              {
                key: '1',
                label: (
                  <span className="flex items-center">
                    <BarChart2 className="mr-2 h-4 w-4" />
                    Overview
                  </span>
                ),
                children: (
                  <>
                    {/* Charts Section - First Row */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                      {/* Revenue Trend Chart */}
                      <Card 
                        title={
                          <div className="flex items-center space-x-2">
                            <TrendingUp className="h-5 w-5 text-indigo-600" />
                            <span className="font-semibold">Revenue Trend</span>
                          </div>
                        }
                        className="shadow-md hover:shadow-lg transition-all duration-300"
                      >
                        <Skeleton loading={loading} active paragraph={{ rows: 6 }}>
                          {renderRevenueChart()}
                        </Skeleton>
                      </Card>

                      {/* Booking Trend Chart */}
                      <Card 
                        title={
                          <div className="flex items-center space-x-2">
                            <Calendar className="h-5 w-5 text-indigo-600" />
                            <span className="font-semibold">Booking Trend</span>
                          </div>
                        }
                        className="shadow-md hover:shadow-lg transition-all duration-300"
                      >
                        <Skeleton loading={loading} active paragraph={{ rows: 6 }}>
                          {renderBookingTrendChart()}
                        </Skeleton>
                      </Card>
                    </div>

                    {/* Charts Section - Second Row */}
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
                      {/* Room Status Chart */}
                      <Card 
                        title={
                          <div className="flex items-center space-x-2">
                            <Home className="h-5 w-5 text-blue-600" />
                            <span className="font-semibold">Room Status</span>
                          </div>
                        }
                        className="shadow-md hover:shadow-lg transition-all duration-300"
                      >
                        <Skeleton loading={loading} active paragraph={{ rows: 6 }}>
                          {renderRoomStatusChart()}
                        </Skeleton>
                      </Card>

                      {/* User Status Chart */}
                      <Card 
                        title={
                          <div className="flex items-center space-x-2">
                            <User className="h-5 w-5 text-green-600" />
                            <span className="font-semibold">User Status</span>
                          </div>
                        }
                        className="shadow-md hover:shadow-lg transition-all duration-300"
                      >
                        <Skeleton loading={loading} active paragraph={{ rows: 6 }}>
                          {renderUserStatusChart()}
                        </Skeleton>
                      </Card>

                      {/* Booking Status Chart */}
                      <Card 
                        title={
                          <div className="flex items-center space-x-2">
                            <Calendar className="h-5 w-5 text-amber-600" />
                            <span className="font-semibold">Booking Status</span>
                          </div>
                        }
                        className="shadow-md hover:shadow-lg transition-all duration-300"
                      >
                        <Skeleton loading={loading} active paragraph={{ rows: 6 }}>
                          {renderBookingStatusChart()}
                        </Skeleton>
                      </Card>
                    </div>

                    {/* User Trend Chart */}
                    <div className="mb-8">
                      <Card 
                        title={
                          <div className="flex items-center space-x-2">
                            <Users className="h-5 w-5 text-purple-600" />
                            <span className="font-semibold">User Registration Trend</span>
                          </div>
                        }
                        className="shadow-md hover:shadow-lg transition-all duration-300"
                      >
                        <Skeleton loading={loading} active paragraph={{ rows: 6 }}>
                          {renderUserTrendChart()}
                        </Skeleton>
                      </Card>
                    </div>
                  </>
                )
              },
              {
                key: '2',
                label: (
                  <span className="flex items-center">
                    <Home className="mr-2 h-4 w-4" />
                    Rooms
                  </span>
                ),
                children: (
                  <>
                    {/* Room Distribution Section */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                      {/* Rooms by Floor Chart */}
                      <Card 
                        title={
                          <div className="flex items-center space-x-2">
                            <Layers className="h-5 w-5 text-blue-600" />
                            <span className="font-semibold">Rooms by Floor</span>
                          </div>
                        }
                        className="shadow-md hover:shadow-lg transition-all duration-300"
                      >
                        <Skeleton loading={loading} active paragraph={{ rows: 6 }}>
                          {renderRoomsByFloorChart()}
                        </Skeleton>
                      </Card>

                      {/* Rooms by Type Chart */}
                      <Card 
                        title={
                          <div className="flex items-center space-x-2">
                            <PieChartIcon className="h-5 w-5 text-green-600" />
                            <span className="font-semibold">Rooms by Type</span>
                          </div>
                        }
                        className="shadow-md hover:shadow-lg transition-all duration-300"
                      >
                        <Skeleton loading={loading} active paragraph={{ rows: 6 }}>
                          {renderRoomsByTypeChart()}
                        </Skeleton>
                      </Card>
                    </div>

                    {/* Room Status Details */}
                    <Card 
                      title={
                        <div className="flex items-center space-x-2">
                          <Home className="h-5 w-5 text-indigo-600" />
                          <span className="font-semibold">Room Status Details</span>
                        </div>
                      }
                      className="shadow-md hover:shadow-lg transition-all duration-300 mb-8"
                    >
                      <Skeleton loading={loading} active paragraph={{ rows: 2 }}>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                          <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm font-medium text-green-700">Operational</span>
                              <Check className="h-5 w-5 text-green-500" />
                            </div>
                            <div className="text-2xl font-bold text-green-700">
                              {response?.data?.rooms?.operational || 0}
                            </div>
                            <div className="text-xs text-green-600 mt-1">
                              {response?.data?.rooms?.total ? 
                                `${((response?.data?.rooms?.operational / response?.data?.rooms?.total) * 100).toFixed(1)}%` : 
                                '0%'} of total
                            </div>
                          </div>

                          <div className="bg-amber-50 p-4 rounded-lg border border-amber-100">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm font-medium text-amber-700">Maintenance</span>
                              <Tool className="h-5 w-5 text-amber-500" />
                            </div>
                            <div className="text-2xl font-bold text-amber-700">
                              {response?.data?.rooms?.maintenance || 0}
                            </div>
                            <div className="text-xs text-amber-600 mt-1">
                              {response?.data?.rooms?.total ? 
                                `${((response?.data?.rooms?.maintenance / response?.data?.rooms?.total) * 100).toFixed(1)}%` : 
                                '0%'} of total
                            </div>
                          </div>

                          <div className="bg-red-50 p-4 rounded-lg border border-red-100">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm font-medium text-red-700">Out of Service</span>
                              <AlertCircle className="h-5 w-5 text-red-500" />
                            </div>
                            <div className="text-2xl font-bold text-red-700">
                              {response?.data?.rooms?.out_of_service || 0}
                            </div>
                            <div className="text-xs text-red-600 mt-1">
                              {response?.data?.rooms?.total ? 
                                `${((response?.data?.rooms?.out_of_service / response?.data?.rooms?.total) * 100).toFixed(1)}%` : 
                                '0%'} of total
                            </div>
                          </div>

                          <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm font-medium text-blue-700">Reserved</span>
                              <Calendar className="h-5 w-5 text-blue-500" />
                            </div>
                            <div className="text-2xl font-bold text-blue-700">
                              {response?.data?.rooms?.reserved || 0}
                            </div>
                            <div className="text-xs text-blue-600 mt-1">
                              {response?.data?.rooms?.total ? 
                                `${((response?.data?.rooms?.reserved / response?.data?.rooms?.total) * 100).toFixed(1)}%` : 
                                '0%'} of total
                            </div>
                          </div>

                          <div className="bg-purple-50 p-4 rounded-lg border border-purple-100">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm font-medium text-purple-700">Occupied</span>
                              <Users className="h-5 w-5 text-purple-500" />
                            </div>
                            <div className="text-2xl font-bold text-purple-700">
                              {response?.data?.rooms?.occupied || 0}
                            </div>
                            <div className="text-xs text-purple-600 mt-1">
                              {response?.data?.rooms?.total ? 
                                `${((response?.data?.rooms?.occupied / response?.data?.rooms?.total) * 100).toFixed(1)}%` : 
                                '0%'} of total
                            </div>
                          </div>
                        </div>
                      </Skeleton>
                    </Card>
                  </>
                )
              },
              {
                key: '3',
                label: (
                  <span className="flex items-center">
                    <Users className="mr-2 h-4 w-4" />
                    Users
                  </span>
                ),
                children: (
                  <>
                    {/* User Distribution Section */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                      {/* User Status Details */}
                      <Card 
                        title={
                          <div className="flex items-center space-x-2">
                            <User className="h-5 w-5 text-indigo-600" />
                            <span className="font-semibold">User Status Details</span>
                          </div>
                        }
                        className="shadow-md hover:shadow-lg transition-all duration-300"
                      >
                        <Skeleton loading={loading} active paragraph={{ rows: 2 }}>
                          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                              <div className="flex items-center justify-between mb-2">
                                <span className="text-sm font-medium text-green-700">Active</span>
                                <UserCheck className="h-5 w-5 text-green-500" />
                              </div>
                              <div className="text-2xl font-bold text-green-700">
                                {response?.data?.users?.active || 0}
                              </div>
                              <div className="text-xs text-green-600 mt-1">
                                {response?.data?.users?.total ? 
                                  `${((response?.data?.users?.active / response?.data?.users?.total) * 100).toFixed(1)}%` : 
                                  '0%'} of total
                              </div>
                            </div>

                            <div className="bg-amber-50 p-4 rounded-lg border border-amber-100">
                              <div className="flex items-center justify-between mb-2">
                                <span className="text-sm font-medium text-amber-700">Inactive</span>
                                <User className="h-5 w-5 text-amber-500" />
                              </div>
                              <div className="text-2xl font-bold text-amber-700">
                                {response?.data?.users?.inactive || 0}
                              </div>
                              <div className="text-xs text-amber-600 mt-1">
                                {response?.data?.users?.total ? 
                                  `${((response?.data?.users?.inactive / response?.data?.users?.total) * 100).toFixed(1)}%` : 
                                  '0%'} of total
                              </div>
                            </div>

                            <div className="bg-red-50 p-4 rounded-lg border border-red-100">
                              <div className="flex items-center justify-between mb-2">
                                <span className="text-sm font-medium text-red-700">Blocked</span>
                                <UserX className="h-5 w-5 text-red-500" />
                              </div>
                              <div className="text-2xl font-bold text-red-700">
                                {response?.data?.users?.blocked || 0}
                              </div>
                              <div className="text-xs text-red-600 mt-1">
                                {response?.data?.users?.total ? 
                                  `${((response?.data?.users?.blocked / response?.data?.users?.total) * 100).toFixed(1)}%` : 
                                  '0%'} of total
                              </div>
                            </div>
                          </div>
                        </Skeleton>
                      </Card>

                      {/* Users by Role Chart */}
                      <Card 
                        title={
                          <div className="flex items-center space-x-2">
                            <PieChartIcon className="h-5 w-5 text-blue-600" />
                            <span className="font-semibold">Users by Role</span>
                          </div>
                        }
                        className="shadow-md hover:shadow-lg transition-all duration-300"
                      >
                        <Skeleton loading={loading} active paragraph={{ rows: 6 }}>
                          {renderUsersByRoleChart()}
                        </Skeleton>
                      </Card>
                    </div>

                    {/* User Trend Chart */}
                    <Card 
                      title={
                        <div className="flex items-center space-x-2">
                          <TrendingUp className="h-5 w-5 text-green-600" />
                          <span className="font-semibold">User Registration Trend</span>
                        </div>
                      }
                      className="shadow-md hover:shadow-lg transition-all duration-300 mb-8"
                    >
                      <Skeleton loading={loading} active paragraph={{ rows: 6 }}>
                        {renderUserTrendChart()}
                      </Skeleton>
                    </Card>
                  </>
                )
              },
              {
                key: '4',
                label: (
                  <span className="flex items-center">
                    <Calendar className="mr-2 h-4 w-4" />
                    Bookings
                  </span>
                ),
                children: (
                  <>
                    {/* Booking Status Details */}
                    <Card 
                      title={
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-5 w-5 text-indigo-600" />
                          <span className="font-semibold">Booking Status Details</span>
                        </div>
                      }
                      className="shadow-md hover:shadow-lg transition-all duration-300 mb-8"
                    >
                      <Skeleton loading={loading} active paragraph={{ rows: 2 }}>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
                          <div className="bg-amber-50 p-4 rounded-lg border border-amber-100">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm font-medium text-amber-700">Pending</span>
                              <Clock className="h-5 w-5 text-amber-500" />
                            </div>
                            <div className="text-2xl font-bold text-amber-700">
                              {response?.data?.bookings?.pending || 0}
                            </div>
                            <div className="text-xs text-amber-600 mt-1">
                              {response?.data?.bookings?.total ? 
                                `${((response?.data?.bookings?.pending / response?.data?.bookings?.total) * 100).toFixed(1)}%` : 
                                '0%'} of total
                            </div>
                          </div>

                          <div className="bg-green-50 p-4 rounded-lg border border-green-100">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm font-medium text-green-700">Approved</span>
                              <Check className="h-5 w-5 text-green-500" />
                            </div>
                            <div className="text-2xl font-bold text-green-700">
                              {response?.data?.bookings?.approved || 0}
                            </div>
                            <div className="text-xs text-green-600 mt-1">
                              {response?.data?.bookings?.total ? 
                                `${((response?.data?.bookings?.approved / response?.data?.bookings?.total) * 100).toFixed(1)}%` : 
                                '0%'} of total
                            </div>
                          </div>

                          <div className="bg-blue-50 p-4 rounded-lg border border-blue-100">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm font-medium text-blue-700">Checked In</span>
                              <Home className="h-5 w-5 text-blue-500" />
                            </div>
                            <div className="text-2xl font-bold text-blue-700">
                              {response?.data?.bookings?.checked_in || 0}
                            </div>
                            <div className="text-xs text-blue-600 mt-1">
                              {response?.data?.bookings?.total ? 
                                `${((response?.data?.bookings?.checked_in / response?.data?.bookings?.total) * 100).toFixed(1)}%` : 
                                '0%'} of total
                            </div>
                          </div>

                          <div className="bg-purple-50 p-4 rounded-lg border border-purple-100">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm font-medium text-purple-700">Completed</span>
                              <Check className="h-5 w-5 text-purple-500" />
                            </div>
                            <div className="text-2xl font-bold text-purple-700">
                              {response?.data?.bookings?.completed || 0}
                            </div>
                            <div className="text-xs text-purple-600 mt-1">
                              {response?.data?.bookings?.total ? 
                                `${((response?.data?.bookings?.completed / response?.data?.bookings?.total) * 100).toFixed(1)}%` : 
                                '0%'} of total
                            </div>
                          </div>

                          <div className="bg-red-50 p-4 rounded-lg border border-red-100">
                            <div className="flex items-center justify-between mb-2">
                              <span className="text-sm font-medium text-red-700">Cancelled</span>
                              <AlertCircle className="h-5 w-5 text-red-500" />
                            </div>
                            <div className="text-2xl font-bold text-red-700">
                              {response?.data?.bookings?.cancelled || 0}
                            </div>
                            <div className="text-xs text-red-600 mt-1">
                              {response?.data?.bookings?.total ? 
                                `${((response?.data?.bookings?.cancelled / response?.data?.bookings?.total) * 100).toFixed(1)}%` : 
                                '0%'} of total
                            </div>
                          </div>
                        </div>
                      </Skeleton>
                    </Card>

                    {/* Booking Trends */}
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                      {/* Booking Count Trend */}
                      <Card 
                        title={
                          <div className="flex items-center space-x-2">
                            <TrendingUp className="h-5 w-5 text-blue-600" />
                            <span className="font-semibold">Booking Count Trend</span>
                          </div>
                        }
                        className="shadow-md hover:shadow-lg transition-all duration-300"
                      >
                        <Skeleton loading={loading} active paragraph={{ rows: 6 }}>
                          {renderBookingTrendChart()}
                        </Skeleton>
                      </Card>

                      {/* Revenue Trend */}
                      <Card 
                        title={
                          <div className="flex items-center space-x-2">
                            <DollarSign className="h-5 w-5 text-green-600" />
                            <span className="font-semibold">Revenue Trend</span>
                          </div>
                        }
                        className="shadow-md hover:shadow-lg transition-all duration-300"
                      >
                        <Skeleton loading={loading} active paragraph={{ rows: 6 }}>
                          {renderRevenueChart()}
                        </Skeleton>
                      </Card>
                    </div>

                    {/* Booking Status Chart */}
                    <Card 
                      title={
                        <div className="flex items-center space-x-2">
                          <PieChartIcon className="h-5 w-5 text-amber-600" />
                          <span className="font-semibold">Booking Status Distribution</span>
                        </div>
                      }
                      className="shadow-md hover:shadow-lg transition-all duration-300 mb-8"
                    >
                      <Skeleton loading={loading} active paragraph={{ rows: 6 }}>
                        {renderBookingStatusChart()}
                      </Skeleton>
                    </Card>
                  </>
                )
              }
            ]}
          />
        </div>
      )}
    </div>
  );
}

export default Dashboard;
