
import mongoose from 'mongoose';

const reviewSchema = new mongoose.Schema({
  user_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'User id is required field']
  },
  room_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Room',
    required: [true, 'Room id is required field']
  },
  booking_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Booking',
    required: [true, 'Booking id is required field']
  },
  rating: {
    type: Number,
    required: [true, 'Review `rating` filed is required'],
    min: 1,
    max: 5
  },
  message: {
    type: String,
    required: [true, 'Review `message` filed is required'],
    trim: true,
    minlength: [10, 'Review message must be at least 10 characters']
  },
  categories: {
    cleanliness: {
      type: Number,
      min: 1,
      max: 5
    },
    comfort: {
      type: Number,
      min: 1,
      max: 5
    },
    location: {
      type: Number,
      min: 1,
      max: 5
    },
    services: {
      type: Number,
      min: 1,
      max: 5
    },
    staff: {
      type: Number,
      min: 1,
      max: 5
    },
    value_for_money: {
      type: Number,
      min: 1,
      max: 5
    }
  },
  photos: [{
    url: String,
    caption: String
  }],
  is_verified: {
    type: Boolean,
    default: false
  },
  admin_response: {
    message: String,
    responded_by: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    responded_at: Date
  },
  is_featured: {
    type: Boolean,
    default: false
  },
  helpful_votes: {
    type: Number,
    default: 0
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending'
  }
}, {
  timestamps: true
});

// Add validation to ensure user can only review after checkout
reviewSchema.pre('save', async function(next) {
  if (this.isNew) {
    const Booking = mongoose.model('Booking');
    const booking = await Booking.findById(this.booking_id);
    
    if (!booking) {
      return next(new Error('Booking not found'));
    }
    
    if (booking.booking_status !== 'completed') {
      return next(new Error('Cannot review a booking that is not completed'));
    }
    
    if (booking.user_id.toString() !== this.user_id.toString()) {
      return next(new Error('User can only review their own bookings'));
    }
  }
  
  next();
});

// Add virtual for average category rating
reviewSchema.virtual('average_category_rating').get(function() {
  const categories = this.categories || {};
  const ratings = Object.values(categories).filter(r => r);
  
  if (ratings.length === 0) {
    return this.rating;
  }
  
  return ratings.reduce((sum, r) => sum + r, 0) / ratings.length;
});

// Add method to mark review as helpful
reviewSchema.methods.markAsHelpful = function() {
  this.helpful_votes += 1;
  return this.save();
};

// Add method to respond to review (admin only)
reviewSchema.methods.respondToReview = function(message, adminId) {
  this.admin_response = {
    message,
    responded_by: adminId,
    responded_at: new Date()
  };
  return this.save();
};

// Add indexes for faster queries
reviewSchema.index({ user_id: 1 });
reviewSchema.index({ room_id: 1 });
reviewSchema.index({ booking_id: 1 });
reviewSchema.index({ rating: 1 });
reviewSchema.index({ is_featured: 1 });
reviewSchema.index({ status: 1 });
reviewSchema.index({ createdAt: -1 });

const Review = mongoose.model('Review', reviewSchema);

export default Review;
