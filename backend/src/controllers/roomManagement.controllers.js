import Room from '../models/room.model.js';
import Booking from '../models/booking.model.js';
import Maintenance from '../models/maintenance.model.js';
import Cleaning from '../models/cleaning.model.js';
import { errorResponse, successResponse } from '../config/app.response.js';
import logger from '../middleware/winston.logger.js';

/**
 * Check room availability for specific dates
 */
const checkRoomAvailability = async (req, res) => {
  try {
    const { roomId, startDate, endDate } = req.body;

    if (!roomId || !startDate || !endDate) {
      return res.status(400).json(errorResponse(
        1,
        'FAILED',
        'Room ID, start date, and end date are required'
      ));
    }

    // Convert dates to Date objects
    const start = new Date(startDate);
    const end = new Date(endDate);

    // Validate dates
    if (isNaN(start.getTime()) || isNaN(end.getTime())) {
      return res.status(400).json(errorResponse(
        1,
        'FAILED',
        'Invalid date format'
      ));
    }

    if (start >= end) {
      return res.status(400).json(errorResponse(
        1,
        'FAILED',
        'End date must be after start date'
      ));
    }

    // Find the room
    const room = await Room.findById(roomId).populate('room_type');
    if (!room) {
      return res.status(404).json(errorResponse(
        4,
        'UNKNOWN ACCESS',
        'Room not found'
      ));
    }

    // Check if room is active
    if (!room.is_active) {
      return res.status(400).json(errorResponse(
        1,
        'FAILED',
        'Room is not active'
      ));
    }

    // Generate array of dates to check
    const dates = [];
    const currentDate = new Date(start);
    while (currentDate < end) {
      dates.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }

    // Check for existing bookings
    const existingBookings = await Booking.find({
      room_id: roomId,
      booking_status: { $in: ['pending', 'approved', 'checked-in'] },
      booking_dates: { $elemMatch: { $gte: start, $lt: end } }
    });

    // Check for scheduled maintenance
    const scheduledMaintenance = await Maintenance.find({
      room_id: roomId,
      status: { $in: ['scheduled', 'in_progress'] },
      $or: [
        { start_date: { $lte: end, $gte: start } },
        { end_date: { $lte: end, $gte: start } },
        { $and: [{ start_date: { $lte: start } }, { end_date: { $gte: end } }] }
      ]
    });

    // Determine availability for each date
    const availability = dates.map(date => {
      const dateStr = date.toISOString().split('T')[0];
      
      // Check if date is in any existing booking
      const isBooked = existingBookings.some(booking => 
        booking.booking_dates.some(bookingDate => 
          bookingDate.toISOString().split('T')[0] === dateStr
        )
      );
      
      // Check if date is during scheduled maintenance
      const isUnderMaintenance = scheduledMaintenance.some(maintenance => {
        const maintenanceStart = new Date(maintenance.start_date);
        const maintenanceEnd = new Date(maintenance.end_date);
        return date >= maintenanceStart && date <= maintenanceEnd;
      });
      
      return {
        date: dateStr,
        available: !isBooked && !isUnderMaintenance,
        reason: isBooked ? 'booked' : (isUnderMaintenance ? 'maintenance' : null)
      };
    });

    // Calculate pricing if available
    let pricing = null;
    if (availability.every(day => day.available)) {
      const numberOfNights = dates.length;
      const basePrice = room.room_type.base_price;
      
      // Apply dynamic pricing if available
      let totalPrice = basePrice * numberOfNights;
      
      // Apply weekend pricing if applicable
      const weekendDays = dates.filter(date => {
        const day = date.getDay();
        return day === 0 || day === 6; // Sunday or Saturday
      }).length;
      
      if (room.room_type.pricing && room.room_type.pricing.weekend_multiplier) {
        const weekdayDays = numberOfNights - weekendDays;
        totalPrice = (basePrice * weekdayDays) + 
                     (basePrice * weekendDays * room.room_type.pricing.weekend_multiplier);
      }
      
      // Apply seasonal pricing if applicable
      if (room.room_type.pricing && room.room_type.pricing.seasonal_rates) {
        const today = new Date();
        const applicableSeasonalRate = room.room_type.pricing.seasonal_rates.find(rate => {
          const seasonStart = new Date(rate.start_date);
          const seasonEnd = new Date(rate.end_date);
          return start >= seasonStart && end <= seasonEnd;
        });
        
        if (applicableSeasonalRate) {
          totalPrice = totalPrice * applicableSeasonalRate.multiplier;
        }
      }
      
      pricing = {
        base_price: basePrice,
        number_of_nights: numberOfNights,
        total_price: totalPrice,
        currency: 'USD' // Assuming USD as default
      };
    }

    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Room availability checked successfully',
      {
        room: {
          id: room._id,
          room_number: room.room_number,
          room_type: room.room_type.name,
          capacity: room.room_type.specifications.capacity
        },
        availability,
        is_fully_available: availability.every(day => day.available),
        pricing
      }
    ));
  } catch (error) {
    logger.error('Error checking room availability:', error);
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

/**
 * Get room availability calendar for a month
 */
const getRoomAvailabilityCalendar = async (req, res) => {
  try {
    const { roomId, year, month } = req.params;
    
    // Validate input
    if (!roomId || !year || !month) {
      return res.status(400).json(errorResponse(
        1,
        'FAILED',
        'Room ID, year, and month are required'
      ));
    }
    
    const numYear = parseInt(year);
    const numMonth = parseInt(month) - 1; // JS months are 0-indexed
    
    if (isNaN(numYear) || isNaN(numMonth) || numMonth < 0 || numMonth > 11) {
      return res.status(400).json(errorResponse(
        1,
        'FAILED',
        'Invalid year or month'
      ));
    }
    
    // Find the room
    const room = await Room.findById(roomId);
    if (!room) {
      return res.status(404).json(errorResponse(
        4,
        'UNKNOWN ACCESS',
        'Room not found'
      ));
    }
    
    // Calculate start and end dates for the month
    const startDate = new Date(numYear, numMonth, 1);
    const endDate = new Date(numYear, numMonth + 1, 0); // Last day of month
    
    // Get all bookings for this room in the specified month
    const bookings = await Booking.find({
      room_id: roomId,
      booking_status: { $in: ['pending', 'approved', 'checked-in'] },
      booking_dates: { $elemMatch: { $gte: startDate, $lte: endDate } }
    });
    
    // Get all maintenance for this room in the specified month
    const maintenances = await Maintenance.find({
      room_id: roomId,
      status: { $in: ['scheduled', 'in_progress'] },
      $or: [
        { start_date: { $lte: endDate, $gte: startDate } },
        { end_date: { $lte: endDate, $gte: startDate } },
        { $and: [{ start_date: { $lte: startDate } }, { end_date: { $gte: endDate } }] }
      ]
    });
    
    // Create calendar data
    const daysInMonth = endDate.getDate();
    const calendarData = [];
    
    for (let day = 1; day <= daysInMonth; day++) {
      const date = new Date(numYear, numMonth, day);
      const dateStr = date.toISOString().split('T')[0];
      
      // Check if date is booked
      const booking = bookings.find(booking => 
        booking.booking_dates.some(bookingDate => 
          bookingDate.toISOString().split('T')[0] === dateStr
        )
      );
      
      // Check if date is under maintenance
      const maintenance = maintenances.find(maintenance => {
        const maintenanceStart = new Date(maintenance.start_date);
        const maintenanceEnd = new Date(maintenance.end_date);
        return date >= maintenanceStart && date <= maintenanceEnd;
      });
      
      calendarData.push({
        date: dateStr,
        day,
        available: !booking && !maintenance,
        status: booking ? 'booked' : (maintenance ? 'maintenance' : 'available'),
        booking_id: booking ? booking._id : null,
        maintenance_id: maintenance ? maintenance._id : null
      });
    }
    
    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Room availability calendar retrieved successfully',
      {
        room: {
          id: room._id,
          room_number: room.room_number
        },
        year: numYear,
        month: numMonth + 1,
        calendar: calendarData
      }
    ));
  } catch (error) {
    logger.error('Error getting room availability calendar:', error);
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

/**
 * Schedule room maintenance
 */
const scheduleRoomMaintenance = async (req, res) => {
  try {
    const { roomId } = req.params;
    const { 
      start_date, 
      end_date, 
      reason, 
      assigned_to, 
      notes,
      cost_amount,
      cost_currency
    } = req.body;
    
    // Validate input
    if (!roomId || !start_date || !end_date || !reason) {
      return res.status(400).json(errorResponse(
        1,
        'FAILED',
        'Room ID, start date, end date, and reason are required'
      ));
    }
    
    // Find the room
    const room = await Room.findById(roomId);
    if (!room) {
      return res.status(404).json(errorResponse(
        4,
        'UNKNOWN ACCESS',
        'Room not found'
      ));
    }
    
    // Convert dates
    const startDate = new Date(start_date);
    const endDate = new Date(end_date);
    
    // Validate dates
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return res.status(400).json(errorResponse(
        1,
        'FAILED',
        'Invalid date format'
      ));
    }
    
    if (startDate >= endDate) {
      return res.status(400).json(errorResponse(
        1,
        'FAILED',
        'End date must be after start date'
      ));
    }
    
    // Check for conflicting bookings
    const conflictingBookings = await Booking.find({
      room_id: roomId,
      booking_status: { $in: ['pending', 'approved', 'checked-in'] },
      booking_dates: { $elemMatch: { $gte: startDate, $lte: endDate } }
    });
    
    if (conflictingBookings.length > 0) {
      return res.status(400).json(errorResponse(
        1,
        'FAILED',
        'Maintenance period conflicts with existing bookings'
      ));
    }
    
    // Create maintenance record
    const maintenance = new Maintenance({
      room_id: roomId,
      start_date: startDate,
      end_date: endDate,
      reason,
      performed_by: req.user.id,
      assigned_to: assigned_to || [],
      notes,
      cost: {
        amount: cost_amount,
        currency: cost_currency || 'USD'
      }
    });
    
    await maintenance.save();
    
    // If maintenance starts today, update room status
    const today = new Date();
    if (startDate <= today && today <= endDate) {
      room.current_status = 'maintenance';
      await room.save();
    }
    
    res.status(201).json(successResponse(
      0,
      'SUCCESS',
      'Room maintenance scheduled successfully',
      await Maintenance.findById(maintenance._id)
        .populate('performed_by', 'userName fullName email')
        .populate('assigned_to', 'userName fullName email')
    ));
  } catch (error) {
    logger.error('Error scheduling room maintenance:', error);
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

/**
 * Update maintenance status
 */
const updateMaintenanceStatus = async (req, res) => {
  try {
    const { roomId, maintenanceId } = req.params;
    const { status, notes, summary, parts_replaced } = req.body;
    
    // Validate input
    if (!roomId || !maintenanceId || !status) {
      return res.status(400).json(errorResponse(
        1,
        'FAILED',
        'Room ID, maintenance ID, and status are required'
      ));
    }
    
    // Find the maintenance record
    const maintenance = await Maintenance.findOne({
      _id: maintenanceId,
      room_id: roomId
    });
    
    if (!maintenance) {
      return res.status(404).json(errorResponse(
        4,
        'UNKNOWN ACCESS',
        'Maintenance record not found'
      ));
    }
    
    // Update based on status
    switch (status) {
      case 'in_progress':
        await maintenance.startMaintenance(req.user.id);
        break;
      case 'completed':
        await maintenance.completeMaintenance(summary, req.user.id);
        break;
      case 'cancelled':
        await maintenance.cancelMaintenance(notes || 'Cancelled by admin');
        break;
      default:
        return res.status(400).json(errorResponse(
          1,
          'FAILED',
          'Invalid status. Must be in_progress, completed, or cancelled'
        ));
    }
    
    // Update additional fields
    if (notes) {
      maintenance.notes = notes;
    }
    
    if (parts_replaced) {
      maintenance.parts_replaced = parts_replaced;
    }
    
    await maintenance.save();
    
    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Maintenance status updated successfully',
      await Maintenance.findById(maintenance._id)
        .populate('performed_by', 'userName fullName email')
        .populate('assigned_to', 'userName fullName email')
    ));
  } catch (error) {
    logger.error('Error updating maintenance status:', error);
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

/**
 * Mark room as cleaned
 */
const markRoomAsCleaned = async (req, res) => {
  try {
    const { roomId } = req.params;
    const { 
      notes, 
      checklist, 
      time_spent, 
      issues_reported 
    } = req.body;
    
    // Find the room
    const room = await Room.findById(roomId);
    if (!room) {
      return res.status(404).json(errorResponse(
        4,
        'UNKNOWN ACCESS',
        'Room not found'
      ));
    }
    
    // Check if room is in cleaning status
    if (room.current_status !== 'cleaning') {
      return res.status(400).json(errorResponse(
        1,
        'FAILED',
        'Room is not in cleaning status'
      ));
    }
    
    // Create or update cleaning record
    let cleaning = await Cleaning.findOne({
      room_id: roomId,
      status: { $in: ['pending', 'in_progress'] }
    });
    
    if (!cleaning) {
      cleaning = new Cleaning({
        room_id: roomId,
        cleaned_by: req.user.id,
        status: 'in_progress'
      });
    }
    
    // Complete the cleaning
    await cleaning.completeCleaning(checklist, time_spent, notes);
    
    // Add reported issues if any
    if (issues_reported && issues_reported.length > 0) {
      for (const issue of issues_reported) {
        await cleaning.reportIssue(
          issue.issue,
          issue.severity,
          issue.requires_maintenance
        );
      }
    }
    
    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Room marked as cleaned successfully',
      await Cleaning.findById(cleaning._id)
        .populate('cleaned_by', 'userName fullName email')
    ));
  } catch (error) {
    logger.error('Error marking room as cleaned:', error);
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

/**
 * Find available rooms
 */
const findAvailableRooms = async (req, res) => {
  try {
    const { 
      start_date, 
      end_date, 
      guests, 
      room_type, 
      features 
    } = req.body;
    
    // Validate input
    if (!start_date || !end_date) {
      return res.status(400).json(errorResponse(
        1,
        'FAILED',
        'Start date and end date are required'
      ));
    }
    
    // Convert dates
    const startDate = new Date(start_date);
    const endDate = new Date(end_date);
    
    // Validate dates
    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
      return res.status(400).json(errorResponse(
        1,
        'FAILED',
        'Invalid date format'
      ));
    }
    
    if (startDate >= endDate) {
      return res.status(400).json(errorResponse(
        1,
        'FAILED',
        'End date must be after start date'
      ));
    }
    
    // Build query for rooms
    let roomQuery = {
      is_active: true
    };
    
    // Add room type filter if provided
    if (room_type) {
      roomQuery.room_type = room_type;
    }
    
    // Add features filter if provided
    if (features && features.length > 0) {
      const featureQueries = [];
      
      for (const feature of features) {
        const featureQuery = {};
        featureQuery[`features.${feature}`] = true;
        featureQueries.push(featureQuery);
      }
      
      roomQuery.$and = featureQueries;
    }
    
    // Find all rooms matching the criteria
    const rooms = await Room.find(roomQuery).populate('room_type');
    
    // Generate array of dates to check
    const dates = [];
    const currentDate = new Date(startDate);
    while (currentDate < endDate) {
      dates.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    // Filter out rooms that have bookings or maintenance during the requested period
    const availableRooms = [];
    
    for (const room of rooms) {
      // Skip rooms that don't meet capacity requirements
      if (guests && room.room_type.specifications.capacity < guests) {
        continue;
      }
      
      // Check for existing bookings
      const existingBookings = await Booking.find({
        room_id: room._id,
        booking_status: { $in: ['pending', 'approved', 'checked-in'] },
        booking_dates: { $elemMatch: { $gte: startDate, $lt: endDate } }
      });
      
      if (existingBookings.length > 0) {
        continue;
      }
      
      // Check for scheduled maintenance
      const scheduledMaintenance = await Maintenance.find({
        room_id: room._id,
        status: { $in: ['scheduled', 'in_progress'] },
        $or: [
          { start_date: { $lte: endDate, $gte: startDate } },
          { end_date: { $lte: endDate, $gte: startDate } },
          { $and: [{ start_date: { $lte: startDate } }, { end_date: { $gte: endDate } }] }
        ]
      });
      
      if (scheduledMaintenance.length > 0) {
        continue;
      }
      
      // Calculate pricing
      const numberOfNights = dates.length;
      const basePrice = room.room_type.base_price;
      
      // Apply dynamic pricing if available
      let totalPrice = basePrice * numberOfNights;
      
      // Apply weekend pricing if applicable
      const weekendDays = dates.filter(date => {
        const day = date.getDay();
        return day === 0 || day === 6; // Sunday or Saturday
      }).length;
      
      if (room.room_type.pricing && room.room_type.pricing.weekend_multiplier) {
        const weekdayDays = numberOfNights - weekendDays;
        totalPrice = (basePrice * weekdayDays) + 
                     (basePrice * weekendDays * room.room_type.pricing.weekend_multiplier);
      }
      
      // Apply seasonal pricing if applicable
      if (room.room_type.pricing && room.room_type.pricing.seasonal_rates) {
        const today = new Date();
        const applicableSeasonalRate = room.room_type.pricing.seasonal_rates.find(rate => {
          const seasonStart = new Date(rate.start_date);
          const seasonEnd = new Date(rate.end_date);
          return startDate >= seasonStart && endDate <= seasonEnd;
        });
        
        if (applicableSeasonalRate) {
          totalPrice = totalPrice * applicableSeasonalRate.multiplier;
        }
      }
      
      // Add room to available rooms
      availableRooms.push({
        id: room._id,
        room_number: room.room_number,
        floor: room.floor,
        room_type: {
          id: room.room_type._id,
          name: room.room_type.name,
          description: room.room_type.description,
          capacity: room.room_type.specifications.capacity,
          size: room.room_type.specifications.size
        },
        features: room.features,
        images: room.images,
        pricing: {
          base_price: basePrice,
          number_of_nights: numberOfNights,
          total_price: totalPrice,
          currency: 'USD' // Assuming USD as default
        }
      });
    }
    
    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Available rooms found successfully',
      {
        search_criteria: {
          start_date,
          end_date,
          guests,
          room_type,
          features
        },
        total_available: availableRooms.length,
        rooms: availableRooms
      }
    ));
  } catch (error) {
    logger.error('Error finding available rooms:', error);
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

/**
 * Get room booked dates
 */
const getRoomBookedDates = async (req, res) => {
  try {
    const { roomId } = req.params;
    
    // Find the room
    const room = await Room.findById(roomId);
    if (!room) {
      return res.status(404).json(errorResponse(
        4,
        'UNKNOWN ACCESS',
        'Room not found'
      ));
    }
    
    // Get start and end dates for the next 6 months
    const startDate = new Date();
    const endDate = new Date();
    endDate.setMonth(endDate.getMonth() + 6);
    
    // Get all bookings for this room in the next 6 months
    const bookings = await Booking.find({
      room_id: roomId,
      booking_status: { $in: ['pending', 'approved', 'checked-in'] },
      booking_dates: { $elemMatch: { $gte: startDate, $lte: endDate } }
    });
    
    // Get all maintenance for this room in the next 6 months
    const maintenances = await Maintenance.find({
      room_id: roomId,
      status: { $in: ['scheduled', 'in_progress'] },
      $or: [
        { start_date: { $lte: endDate, $gte: startDate } },
        { end_date: { $lte: endDate, $gte: startDate } },
        { $and: [{ start_date: { $lte: startDate } }, { end_date: { $gte: endDate } }] }
      ]
    });
    
    // Collect all booked dates
    const bookedDates = [];
    
    // Add dates from bookings
    bookings.forEach(booking => {
      booking.booking_dates.forEach(date => {
        if (date >= startDate && date <= endDate) {
          bookedDates.push({
            date: date.toISOString().split('T')[0],
            type: 'booking',
            booking_id: booking._id
          });
        }
      });
    });
    
    // Add dates from maintenance
    maintenances.forEach(maintenance => {
      const maintenanceStart = new Date(maintenance.start_date);
      const maintenanceEnd = new Date(maintenance.end_date);
      
      const currentDate = new Date(Math.max(maintenanceStart, startDate));
      while (currentDate <= Math.min(maintenanceEnd, endDate)) {
        bookedDates.push({
          date: currentDate.toISOString().split('T')[0],
          type: 'maintenance',
          maintenance_id: maintenance._id
        });
        currentDate.setDate(currentDate.getDate() + 1);
      }
    });
    
    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Room booked dates retrieved successfully',
      {
        room: {
          id: room._id,
          room_number: room.room_number
        },
        booked_dates: bookedDates
      }
    ));
  } catch (error) {
    logger.error('Error getting room booked dates:', error);
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

/**
 * Update room status
 */
const updateRoomStatus = async (req, res) => {
  try {
    const { roomId } = req.params;
    const { status, notes } = req.body;
    
    // Validate input
    if (!roomId || !status) {
      return res.status(400).json(errorResponse(
        1,
        'FAILED',
        'Room ID and status are required'
      ));
    }
    
    // Find the room
    const room = await Room.findById(roomId);
    if (!room) {
      return res.status(404).json(errorResponse(
        4,
        'UNKNOWN ACCESS',
        'Room not found'
      ));
    }
    
    // Validate status
    const validStatuses = [
      'available', 'occupied', 'reserved', 'cleaning', 
      'maintenance', 'out_of_order', 'blocked', 'inspection'
    ];
    
    if (!validStatuses.includes(status)) {
      return res.status(400).json(errorResponse(
        1,
        'FAILED',
        `Invalid status. Must be one of: ${validStatuses.join(', ')}`
      ));
    }
    
    // Check for active bookings if setting to maintenance or out_of_order
    if (['maintenance', 'out_of_order', 'blocked'].includes(status)) {
      const activeBookings = await Booking.find({
        room_id: roomId,
        booking_status: { $in: ['pending', 'approved', 'checked-in'] },
        booking_dates: { $gte: new Date() }
      });
      
      if (activeBookings.length > 0) {
        return res.status(400).json(errorResponse(
          1,
          'FAILED',
          'Cannot change status because room has active bookings'
        ));
      }
    }
    
    // Update room status
    room.current_status = status;
    
    // Create appropriate records based on status
    if (status === 'cleaning') {
      // Create cleaning record if not exists
      const existingCleaning = await Cleaning.findOne({
        room_id: roomId,
        status: { $in: ['pending', 'in_progress'] }
      });
      
      if (!existingCleaning) {
        const cleaning = new Cleaning({
          room_id: roomId,
          cleaned_by: req.user.id,
          status: 'pending',
          notes: notes || 'Initiated by admin'
        });
        
        await cleaning.startCleaning();
      }
    } else if (status === 'maintenance') {
      // Create maintenance record if not exists
      const existingMaintenance = await Maintenance.findOne({
        room_id: roomId,
        status: { $in: ['scheduled', 'in_progress'] }
      });
      
      if (!existingMaintenance) {
        const maintenance = new Maintenance({
          room_id: roomId,
          start_date: new Date(),
          end_date: new Date(new Date().setDate(new Date().getDate() + 1)), // Default to 1 day
          reason: notes || 'Initiated by admin',
          performed_by: req.user.id,
          status: 'scheduled'
        });
        
        await maintenance.save();
        await maintenance.startMaintenance(req.user.id);
      }
    }
    
    await room.save();
    
    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Room status updated successfully',
      await Room.findById(room._id).populate('room_type')
    ));
  } catch (error) {
    logger.error('Error updating room status:', error);
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

/**
 * Manually run the update for completed bookings
 */
const runStatusUpdateForCompletedBookings = async (req, res) => {
  try {
    const result = await updateRoomStatusForCompletedBookings();

    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Room status update for completed bookings executed successfully',
      result
    ));
  } catch (error) {
    logger.error('Error running status update for completed bookings:', error);
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

/**
 * Verify room cleaning
 */
const verifyRoomCleaning = async (req, res) => {
  try {
    const { roomId, cleaningId } = req.params;
    const { passed, comments } = req.body;
    
    // Validate input
    if (!roomId || !cleaningId || passed === undefined) {
      return res.status(400).json(errorResponse(
        1,
        'FAILED',
        'Room ID, cleaning ID, and passed status are required'
      ));
    }
    
    // Find the cleaning record
    const cleaning = await Cleaning.findOne({
      _id: cleaningId,
      room_id: roomId
    });
    
    if (!cleaning) {
      return res.status(404).json(errorResponse(
        4,
        'UNKNOWN ACCESS',
        'Cleaning record not found'
      ));
    }
    
    // Verify the cleaning
    await cleaning.verifyCleaning(req.user.id, passed, comments);
    
    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Room cleaning verified successfully',
      await Cleaning.findById(cleaning._id)
        .populate('cleaned_by', 'userName fullName email')
        .populate('inspection.inspected_by', 'userName fullName email')
    ));
  } catch (error) {
    logger.error('Error verifying room cleaning:', error);
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

/**
 * Get room maintenance history
 */
const getRoomMaintenanceHistory = async (req, res) => {
  try {
    const { roomId } = req.params;
    
    // Find the room
    const room = await Room.findById(roomId);
    if (!room) {
      return res.status(404).json(errorResponse(
        4,
        'UNKNOWN ACCESS',
        'Room not found'
      ));
    }
    
    // Get all maintenance records for this room
    const maintenanceRecords = await Maintenance.find({ room_id: roomId })
      .sort({ start_date: -1 })
      .populate('performed_by', 'userName fullName email')
      .populate('assigned_to', 'userName fullName email')
      .populate('completion_report.verified_by', 'userName fullName email');
    
    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Room maintenance history retrieved successfully',
      {
        room: {
          id: room._id,
          room_number: room.room_number
        },
        maintenance_records: maintenanceRecords
      }
    ));
  } catch (error) {
    logger.error('Error getting room maintenance history:', error);
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

/**
 * Get room cleaning history
 */
const getRoomCleaningHistory = async (req, res) => {
  try {
    const { roomId } = req.params;
    
    // Find the room
    const room = await Room.findById(roomId);
    if (!room) {
      return res.status(404).json(errorResponse(
        4,
        'UNKNOWN ACCESS',
        'Room not found'
      ));
    }
    
    // Get all cleaning records for this room
    const cleaningRecords = await Cleaning.find({ room_id: roomId })
      .sort({ cleaned_at: -1 })
      .populate('cleaned_by', 'userName fullName email')
      .populate('inspection.inspected_by', 'userName fullName email');
    
    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Room cleaning history retrieved successfully',
      {
        room: {
          id: room._id,
          room_number: room.room_number
        },
        cleaning_records: cleaningRecords
      }
    ));
  } catch (error) {
    logger.error('Error getting room cleaning history:', error);
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

export {
  checkRoomAvailability,
  getRoomAvailabilityCalendar,
  scheduleRoomMaintenance,
  updateMaintenanceStatus,
  markRoomAsCleaned,
  findAvailableRooms,
  getRoomBookedDates,
  updateRoomStatus,
  runStatusUpdateForCompletedBookings,
  verifyRoomCleaning,
  getRoomMaintenanceHistory,
  getRoomCleaningHistory
};

