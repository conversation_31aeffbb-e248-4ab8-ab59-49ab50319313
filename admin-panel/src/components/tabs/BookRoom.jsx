import { 
  ExclamationCircleFilled, 
  InfoCircleOutlined, 
  CalendarOutlined,
  UserOutlined,
  HomeOutlined,
  MailOutlined,
  PhoneOutlined,
  TeamOutlined,
  CreditCardOutlined,
  DollarOutlined,
  FileTextOutlined,
  FilterOutlined
} from '@ant-design/icons';
import {
  Button, DatePicker, Form, Input, InputNumber, Space, Select, Spin, Switch, Divider,
  Card, Collapse, Row, Col, Typography, Tooltip, TimePicker, Checkbox, Alert
} from 'antd';
import React, { useState, useEffect } from 'react';
import { getSessionUser } from '../../utils/authentication';
import ApiService from '../../utils/apiService';
import notificationWithIcon from '../../utils/notification';
import dayjs from 'dayjs';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';

// Add the isSameOrBefore plugin to dayjs
dayjs.extend(isSameOrBefore);

const { RangePicker } = DatePicker;
const { Option } = Select;
const { TextArea } = Input;
const { Panel } = Collapse;
const { Title } = Typography;

// Helper component for required field labels
const RequiredFieldLabel = ({ text }) => (
  <span>
    {text} <Tooltip title="Required field"><InfoCircleOutlined style={{ color: '#ff4d4f' }} /></Tooltip>
  </span>
);

function BookRoom({ roomId: propRoomId }) {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [rooms, setRooms] = useState([]);
  const [allRooms, setAllRooms] = useState([]); // Store all fetched rooms
  const [fetchingRooms, setFetchingRooms] = useState(false);
  const [users, setUsers] = useState([]);
  const [fetchingUsers, setFetchingUsers] = useState(false);
  const [isGuestBooking, setIsGuestBooking] = useState(false);
  const [roomTypes, setRoomTypes] = useState([]);
  const [fetchingRoomTypes, setFetchingRoomTypes] = useState(false);
  const [selectedRoom, setSelectedRoom] = useState(null);
  const [selectedRoomTypeFilter, setSelectedRoomTypeFilter] = useState(null); // For filtering rooms by type
  const [isAdvancePayment, setIsAdvancePayment] = useState(false);
  const [totalAmount, setTotalAmount] = useState(0);
  const [nightlyRate, setNightlyRate] = useState(0);
  const [numberOfNights, setNumberOfNights] = useState(0);

  useEffect(() => {
    if (!propRoomId) {
      fetchAvailableRooms();
    } else {
      fetchRoomDetails(propRoomId);
    }
    fetchUsers();
    fetchRoomTypes();
  }, [propRoomId]);

  // Filter rooms when room type filter changes
  useEffect(() => {
    if (selectedRoomTypeFilter) {
      const filteredRooms = allRooms.filter(room => 
        room.room_type._id === selectedRoomTypeFilter
      );
      setRooms(filteredRooms);
    } else {
      setRooms(allRooms);
    }
  }, [selectedRoomTypeFilter, allRooms]);

  const fetchRoomDetails = async (roomId) => {
    try {
      const response = await ApiService.get(`/api/v1/rooms/${roomId}`);
      if (response?.result_code === 0) {
        setSelectedRoom(response.result.data);
        form.setFieldsValue({
          room_type: response.result.data.room_type._id
        });
      }
    } catch (error) {
      notificationWithIcon('error', 'ERROR', 'Failed to fetch room details');
    }
  };

  const fetchAvailableRooms = async () => {
    try {
      setFetchingRooms(true);
      const response = await ApiService.get('/api/v1/all-rooms-list?limit=1000');
      if (response?.result?.data?.rows) {
        // Store all rooms and set the filtered rooms
        const fetchedRooms = response.result.data.rows;
        setAllRooms(fetchedRooms);
        
        // Apply room type filter if it exists
        if (selectedRoomTypeFilter) {
          const filteredRooms = fetchedRooms.filter(room => 
            room.room_type._id === selectedRoomTypeFilter
          );
          setRooms(filteredRooms);
        } else {
          setRooms(fetchedRooms);
        }
      }
    } catch (error) {
      notificationWithIcon('error', 'ERROR', 'Failed to fetch available rooms');
    } finally {
      setFetchingRooms(false);
    }
  };

  const fetchUsers = async () => {
    try {
      setFetchingUsers(true);
      const response = await ApiService.get('/api/v1/all-users-list');
      if (response?.result?.data?.rows) {
        setUsers(response.result.data.rows);
      }
    } catch (error) {
      notificationWithIcon('error', 'ERROR', 'Failed to fetch users');
    } finally {
      setFetchingUsers(false);
    }
  };

  const fetchRoomTypes = async () => {
    try {
      setFetchingRoomTypes(true);
      const response = await ApiService.get('/api/v1/room-types');
      if (response?.result?.data) {
        setRoomTypes(response.result.data);
      }
    } catch (error) {
      notificationWithIcon('error', 'ERROR', 'Failed to fetch room types');
    } finally {
      setFetchingRoomTypes(false);
    }
  };

  const handleGuestSwitchChange = (checked) => {
    setIsGuestBooking(checked);
    form.setFieldsValue({
      user_id: undefined,
      guest_email: undefined,
      guest_phone: undefined
    });
  };

  const handleRoomChange = (roomId) => {
    const room = rooms.find(r => r._id === roomId);
    if (room) {
      setSelectedRoom(room);
      form.setFieldsValue({
        room_type: room.room_type._id
      });
      
      // Calculate total amount when room changes
      calculateTotalAmount(room, form.getFieldValue('dates'));
      
      // Check availability if dates are selected
      const dates = form.getFieldValue('dates');
      if (dates && dates.length === 2) {
        checkRoomAvailability(roomId, dates);
      }
    }
  };

  const handleRoomTypeFilterChange = (roomTypeId) => {
    setSelectedRoomTypeFilter(roomTypeId);
    // Clear the selected room when changing the filter
    if (roomTypeId) {
      form.setFieldsValue({ room_id: undefined });
      setSelectedRoom(null);
    }
  };

  const handleSubmit = async (values) => {
    try {
      setLoading(true);

      // Extract dates from range picker
      const startDate = values.dates[0];
      const endDate = values.dates[1];
      
      // Generate array of dates between start and end
      const bookingDates = [];
      let currentDate = startDate.clone();
      
      while (currentDate.isSameOrBefore(endDate, 'day')) {
        bookingDates.push(currentDate.format('YYYY-MM-DD'));
        currentDate = currentDate.add(1, 'day');
      }

      // Ensure we're working with numbers for all payment values
      // Use the state value for totalAmount if the form value is missing
      const totalAmount = Number(values.payment_amount || totalAmount || 0);
      const advanceAmount = isAdvancePayment ? Number(values.advance_amount || 0) : totalAmount;
      const balanceAmount = Math.max(0, totalAmount - advanceAmount);

      // Determine payment status based on advance payment
      const paymentStatus = advanceAmount > 0 ? 
        (advanceAmount < totalAmount ? 'partially_paid' : 'completed') : 
        'pending';

      // Create payment history entry for the initial payment
      const initialPaymentHistory = [];
      if (advanceAmount > 0) {
        initialPaymentHistory.push({
          type: advanceAmount < totalAmount ? 'advance' : 'full',
          amount: advanceAmount,
          method: values.payment_method || 'credit_card',
          transaction_id: values.transaction_id || '',
          paid_at: new Date().toISOString(),
          notes: `Initial ${advanceAmount < totalAmount ? 'advance' : 'full'} payment`
        });
      }

      // Create the booking data object with payment history
      const bookingData = {
        room_id: propRoomId || values.room_id,
        room_type_id: values.room_type,
        booking_dates: bookingDates,
        booking_status: values.booking_status || 'approved',
        guests: {
          adults: values.adults,
          children: values.children || 0
        },
        special_requests: values.special_requests || '',
        payment: {
          method: values.payment_method || 'credit_card',
          amount: totalAmount,
          advance_amount: advanceAmount,
          balance_amount: balanceAmount,
          currency: values.currency || 'INR',
          status: paymentStatus,
          transaction_id: values.transaction_id || '',
          paid_at: advanceAmount > 0 ? new Date().toISOString() : null,
          notes: values.payment_notes || '',
          payment_history: initialPaymentHistory
        }
      };
      
      // Log the data being sent to help with debugging
      console.log('Booking data being sent:', bookingData);
      
      // Important: Set the correct endpoint based on booking type
      let endpoint = '/api/v1/admin/bookings';
      
      if (isGuestBooking) {
        // For guest bookings, set is_guest flag and include guest details
        bookingData.is_guest = true;
        bookingData.guest_name = values.guest_name;
        bookingData.guest_email = values.guest_email;
        bookingData.guest_phone = values.guest_phone;
        bookingData.guest_address = `${values.guest_address?.street || ''}, ${values.guest_address?.city || ''}, ${values.guest_address?.state || ''}, ${values.guest_address?.postalCode || ''}, ${values.guest_address?.country || ''}`;
        
        // Remove any user_id that might be present
        delete bookingData.user_id;
      } else {
        // For registered user bookings, include the user_id
        bookingData.user_id = values.user_id;
        
        // Remove any guest fields that might be present
        delete bookingData.is_guest;
        delete bookingData.guest_name;
        delete bookingData.guest_email;
        delete bookingData.guest_phone;
        delete bookingData.guest_address;
      }
      
      const response = await ApiService.post(endpoint, bookingData);
      
      if (response.result_code === 0) {
        notificationWithIcon('success', 'SUCCESS', 'Room booked successfully');
        form.resetFields();
        setIsGuestBooking(false);
      } else {
        throw new Error(response.result?.error || 'Failed to create booking');
      }
    } catch (error) {
      notificationWithIcon('error', 'ERROR', error.message || 'Failed to book room');
    } finally {
      setLoading(false);
    }
  };

  const validateDates = (_, value) => {
    if (!value || value.length !== 2) {
      return Promise.reject('Please select booking dates');
    }
    
    const today = dayjs().startOf('day');
    const [startDate, endDate] = value;
    
    if (!dayjs.isDayjs(startDate) || !dayjs.isDayjs(endDate)) {
      return Promise.reject('Invalid date selection');
    }
    
    if (startDate.isBefore(today) || endDate.isBefore(today)) {
      return Promise.reject('Cannot book dates in the past');
    }
    
    if (endDate.isBefore(startDate)) {
      return Promise.reject('End date cannot be before start date');
    }
    
    return Promise.resolve();
  };

  // Add guest address fields to the form
  const renderGuestFields = () => (
    <>
      <Form.Item
        name="guest_name"
        label="Guest Name"
        rules={[{ required: isGuestBooking, message: 'Please enter guest name' }]}
      >
        <Input placeholder="Enter guest name" />
      </Form.Item>
      
      <Form.Item
        name="guest_email"
        label="Guest Email"
        rules={[
          { type: 'email', message: 'Please enter a valid email' }
        ]}
      >
        <Input placeholder="Enter guest email" />
      </Form.Item>
      
      <Form.Item
        name="guest_phone"
        label="Guest Phone"
        rules={[{ required: isGuestBooking, message: 'Please enter guest phone' }]}
      >
        <Input placeholder="Enter guest phone" />
      </Form.Item>
      
      <Divider>Guest Address</Divider>
      
      <Form.Item
        name={['guest_address', 'street']}
        label="Street Address"
        rules={[{ required: isGuestBooking, message: 'Please enter street address' }]}
      >
        <Input placeholder="Enter street address" />
      </Form.Item>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Form.Item
          name={['guest_address', 'city']}
          label="City"
          rules={[{ required: isGuestBooking, message: 'Please enter city' }]}
        >
          <Input placeholder="Enter city" />
        </Form.Item>
        
        <Form.Item
          name={['guest_address', 'state']}
          label="State/Province"
          rules={[{ required: isGuestBooking, message: 'Please enter state/province' }]}
        >
          <Input placeholder="Enter state/province" />
        </Form.Item>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Form.Item
          name={['guest_address', 'postalCode']}
          label="Postal Code"
          rules={[{ required: isGuestBooking, message: 'Please enter postal code' }]}
        >
          <Input placeholder="Enter postal code" />
        </Form.Item>
        
        <Form.Item
          name={['guest_address', 'country']}
          label="Country"
          rules={[{ required: isGuestBooking, message: 'Please enter country' }]}
        >
          <Input placeholder="Enter country" />
        </Form.Item>
      </div>
    </>
  );

  const calculateTotalAmount = (roomData, dates) => {
    if (!roomData || !dates || !dates[0] || !dates[1]) {
      setTotalAmount(0);
      setNightlyRate(0);
      setNumberOfNights(0);
      
      // Update the form field
      form.setFieldsValue({
        payment_amount: 0
      });
      return;
    }
    
    const startDate = dates[0];
    const endDate = dates[1];
    const nights = endDate.diff(startDate, 'days');
    
    // Get the base price from the selected room
    const basePrice = Number(roomData.room_type?.base_price || 0);
    
    // Calculate total amount
    const calculatedTotal = basePrice * nights;
    
    setNightlyRate(basePrice);
    setNumberOfNights(nights);
    setTotalAmount(calculatedTotal);
    
    // Update the form field with the calculated amount
    form.setFieldsValue({
      payment_amount: calculatedTotal
    });
    
    console.log('Updated payment_amount in form to:', calculatedTotal);
  };

  // Remove this incorrect useEffect
  useEffect(() => {
    // Watch for changes in the dates field
    const subscription = form.getFieldValue('dates') && 
      form.watch('dates', (value) => {
        calculateTotalAmount(selectedRoom, value);
      });
    
    return () => subscription?.unsubscribe();
  }, [selectedRoom, form]);

  // We don't need this effect since we're using onValuesChange on the Form component

  // Add this function to check room availability
  const checkRoomAvailability = async (roomId, dates) => {
    if (!roomId || !dates || !dates[0] || !dates[1]) return;
    
    try {
      const startDate = dates[0].format('YYYY-MM-DD');
      const endDate = dates[1].format('YYYY-MM-DD');
      
      const response = await ApiService.get(
        `/api/v1/rooms/${roomId}/availability?start_date=${startDate}&end_date=${endDate}`
      );
      
      if (response?.result_code === 0) {
        const isAvailable = response.result.data.availability.every(day => day.available);
        
        if (!isAvailable) {
          notificationWithIcon(
            'warning', 
            'Room Not Available', 
            'The selected room is not available for all the selected dates.'
          );
        }
      }
    } catch (error) {
      console.error('Error checking room availability:', error);
    }
  };

  return (
    <Card title={<span><HomeOutlined /> Book a Room</span>}>
      <Form
        form={form}
        layout="vertical"
        onFinish={handleSubmit}
        initialValues={{
          room_id: propRoomId,
          adults: 1,
          children: 0,
          currency: 'INR',
          payment_status: 'pending',
          payment_method: 'credit_card',
          payment_amount: 0
        }}
        onValuesChange={(changedValues) => {
          if (changedValues.dates) {
            calculateTotalAmount(selectedRoom, changedValues.dates);
          }
        }}
      >
        <Collapse defaultActiveKey={['1', '2', '3']} ghost>
          <Panel header={<span><HomeOutlined /> Room & Guest Information</span>} key="1">
            {!propRoomId && (
              <Row gutter={16} style={{ marginBottom: 16 }}>
                <Col span={24}>
                  <Form.Item
                    label={<span><FilterOutlined /> Filter Rooms by Type</span>}
                  >
                    <Select
                      placeholder="Select room type to filter"
                      allowClear
                      onChange={handleRoomTypeFilterChange}
                      loading={fetchingRoomTypes}
                      value={selectedRoomTypeFilter}
                      style={{ width: '100%' }}
                    >
                      {roomTypes.map(type => (
                        <Option key={type._id} value={type._id}>
                          {type.name}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
            )}
            
            <Row gutter={16}>
              {!propRoomId && (
                <Col span={12}>
                  <Form.Item
                    name="room_id"
                    label={<RequiredFieldLabel text="Select Room" />}
                    rules={[{ required: true, message: 'Please select a room' }]}
                  >
                    <Select
                      loading={fetchingRooms}
                      placeholder="Select a room"
                      optionFilterProp="children"
                      showSearch
                      onChange={handleRoomChange}
                      suffixIcon={<HomeOutlined />}
                    >
                      {rooms.map(room => (
                        <Option 
                          key={room._id} 
                          value={room._id}
                        >
                          {`Room ${room.room_number} - ${room.room_type?.name || 'N/A'} - ₹${room.room_type?.base_price || 0}/night`}
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              )}

              <Col span={propRoomId ? 12 : 12}>
                <Form.Item
                  name="room_type"
                  label={<RequiredFieldLabel text="Room Type" />}
                  rules={[{ required: true, message: 'Please select a room type' }]}
                >
                  <Select
                    loading={fetchingRoomTypes}
                    placeholder="Select room type"
                    disabled={selectedRoom !== null}
                    suffixIcon={<HomeOutlined />}
                  >
                    {roomTypes.map(type => (
                      <Option key={type._id} value={type._id}>
                        {type.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Form.Item label={<span><UserOutlined /> Book as Guest</span>}>
              <Switch
                checked={isGuestBooking}
                onChange={handleGuestSwitchChange}
                checkedChildren="Guest"
                unCheckedChildren="User"
              />
            </Form.Item>

            {!isGuestBooking ? (
              <Form.Item
                name="user_id"
                label={<RequiredFieldLabel text="Select User" />}
                rules={[{ required: !isGuestBooking, message: 'Please select a user' }]}
              >
                <Select
                  loading={fetchingUsers}
                  placeholder="Select a user to book for"
                  optionFilterProp="children"
                  showSearch
                  filterOption={(input, option) =>
                    option.children.toLowerCase().indexOf(input.toLowerCase()) >= 0
                  }
                  suffixIcon={<UserOutlined />}
                >
                  {users.map(user => (
                    <Option 
                      key={user._id} 
                      value={user._id}
                    >
                      {`${user.fullName} (${user.email})`}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            ) : (
              <>
                <Row gutter={16}>
                  <Col span={24}>
                    {renderGuestFields()}
                  </Col>
                </Row>
              </>
            )}

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="dates"
                  label={<RequiredFieldLabel text="Booking Dates" />}
                  rules={[{ validator: validateDates }]}
                >
                  <RangePicker 
                    style={{ width: '100%' }} 
                    disabledDate={(current) => {
                      return current && current < dayjs().startOf('day');
                    }}
                    suffixIcon={<CalendarOutlined />}
                    onChange={(dates) => {
                      if (dates && dates.length === 2) {
                        calculateTotalAmount(selectedRoom, dates);
                        
                        // Check availability if room is selected
                        const roomId = form.getFieldValue('room_id');
                        if (roomId) {
                          checkRoomAvailability(roomId, dates);
                        }
                      }
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Row gutter={8}>
                  <Col span={12}>
                    <Form.Item
                      name="adults"
                      label={<RequiredFieldLabel text="Adults" />}
                      rules={[
                        { required: true, message: 'Required' },
                        { type: 'number', min: 1, message: 'Min 1' }
                      ]}
                    >
                      <InputNumber 
                        min={1} 
                        style={{ width: '100%' }} 
                        prefix={<TeamOutlined />}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name="children"
                      label={<span><TeamOutlined /> Children</span>}
                      rules={[
                        { type: 'number', min: 0, message: 'Min 0' }
                      ]}
                    >
                      <InputNumber min={0} style={{ width: '100%' }} />
                    </Form.Item>
                  </Col>
                </Row>
              </Col>
            </Row>

            <Form.Item
              name="special_requests"
              label={<span><FileTextOutlined /> Special Requests</span>}
            >
              <TextArea 
                rows={4} 
                placeholder="Enter any special requests or notes" 
              />
            </Form.Item>
          </Panel>

          <Panel header={<span><CreditCardOutlined /> Payment Information</span>} key="2">
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="payment_method"
                  label={<RequiredFieldLabel text="Payment Method" />}
                  rules={[{ required: true, message: 'Please select payment method' }]}
                  initialValue="credit_card"
                >
                  <Select suffixIcon={<CreditCardOutlined />}>
                    <Option value="credit_card">Credit Card</Option>
                    <Option value="debit_card">Debit Card</Option>
                    <Option value="paypal">PayPal</Option>
                    <Option value="bank_transfer">Bank Transfer</Option>
                    <Option value="cash">Cash</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="payment_status"
                  label={<span><DollarOutlined /> Payment Status</span>}
                  initialValue="pending"
                >
                  <Select>
                    <Option value="pending">Pending</Option>
                    <Option value="completed">Completed</Option>
                    <Option value="failed">Failed</Option>
                    <Option value="refunded">Refunded</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              name="is_advance_payment"
              valuePropName="checked"
            >
              <Checkbox 
                onChange={(e) => {
                  setIsAdvancePayment(e.target.checked);
                  
                  // If turning off advance payment, reset advance amount
                  if (!e.target.checked) {
                    form.setFieldsValue({ advance_amount: 0 });
                  }
                }}
              >
                Advance Payment (Partial payment now, balance at check-out)
              </Checkbox>
            </Form.Item>
            
            {isAdvancePayment && (
              <Alert
                message="Advance Payment Information"
                description="Collect partial payment now. The remaining balance will be due at check-out."
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />
            )}
            
            {selectedRoom && numberOfNights > 0 && (
              <Alert
                message="Price Calculation"
                description={
                  <div>
                    <p>Room rate: {nightlyRate} {form.getFieldValue('currency') || 'INR'} per night</p>
                    <p>Number of nights: {numberOfNights}</p>
                    <p>Total amount: {totalAmount} {form.getFieldValue('currency') || 'INR'}</p>
                  </div>
                }
                type="info"
                showIcon
                style={{ marginBottom: 16 }}
              />
            )}

            <Row gutter={16}>
              <Col span={isAdvancePayment ? 8 : 12}>
                <Form.Item
                  name="payment_amount"
                  label={<RequiredFieldLabel text="Total Amount" />}
                  rules={[
                    { required: true, message: 'Please enter total payment amount' },
                    { type: 'number', min: 0, message: 'Amount must be positive' }
                  ]}
                >
                  <InputNumber 
                    style={{ width: '100%' }} 
                    min={0} 
                    step={0.01} 
                    precision={2}
                    prefix={<DollarOutlined />}
                    onChange={(value) => {
                      setTotalAmount(value);
                      // No need to update form here as InputNumber is already bound to form
                    }}
                  />
                </Form.Item>
              </Col>
              
              {isAdvancePayment && (
                <Col span={8}>
                  <Form.Item
                    name="advance_amount"
                    label={<RequiredFieldLabel text="Advance Amount" />}
                    rules={[
                      { required: isAdvancePayment, message: 'Please enter advance amount' },
                      { type: 'number', min: 0, message: 'Amount must be positive' },
                      ({ getFieldValue }) => ({
                        validator(_, value) {
                          if (!value || getFieldValue('payment_amount') >= value) {
                            return Promise.resolve();
                          }
                          return Promise.reject(new Error('Advance cannot exceed total amount'));
                        },
                      }),
                    ]}
                    initialValue={0} // Set initial value to 0
                  >
                    <InputNumber 
                      style={{ width: '100%' }} 
                      min={0} 
                      step={0.01} 
                      precision={2}
                      prefix={<DollarOutlined />}
                      onChange={(value) => {
                        // Ensure the form field is updated
                        form.setFieldsValue({ advance_amount: value });
                      }}
                    />
                  </Form.Item>
                </Col>
              )}
              
              {isAdvancePayment && (
                <Col span={8}>
                  <Form.Item
                    label="Balance Due"
                    shouldUpdate={(prevValues, currentValues) => 
                      prevValues.payment_amount !== currentValues.payment_amount || 
                      prevValues.advance_amount !== currentValues.advance_amount
                    }
                  >
                    {({ getFieldValue }) => {
                      const total = getFieldValue('payment_amount') || 0;
                      const advance = getFieldValue('advance_amount') || 0;
                      const balance = Math.max(0, total - advance);
                      return (
                        <InputNumber
                          style={{ width: '100%' }}
                          value={balance}
                          disabled
                          prefix={<DollarOutlined />}
                          precision={2}
                        />
                      );
                    }}
                  </Form.Item>
                </Col>
              )}
              
              <Col span={isAdvancePayment ? 8 : 12}>
                <Form.Item
                  name="currency"
                  label={<span><DollarOutlined /> Currency</span>}
                  initialValue="INR"
                >
                  <Select>
                    <Option value="INR">INR</Option>
                    <Option value="USD">USD</Option>
                    <Option value="EUR">EUR</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name="transaction_id"
                  label={<span><FileTextOutlined /> Transaction ID</span>}
                >
                  <Input placeholder="Enter transaction ID if available" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name="paid_at"
                  label={<span><CalendarOutlined /> Payment Date</span>}
                >
                  <DatePicker 
                    style={{ width: '100%' }}
                    placeholder="Select payment date if paid"
                  />
                </Form.Item>
              </Col>
            </Row>
          </Panel>
        </Collapse>

        <Divider />

        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit" loading={loading}>
              Book Room
            </Button>
            <Button onClick={() => {
              form.resetFields();
              setIsGuestBooking(false);
            }}>
              Reset
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
}

export default BookRoom;
