
import mongoose from 'mongoose';
import Room from './room.model.js';
import Counter from './counter.model.js';

const bookingSchema = new mongoose.Schema({
  booking_id: {
    type: String,
    unique: true,
    required: true,
    trim: true
  },
  user_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: function() { return !this.is_guest; }
  },
  is_guest: {
    type: Boolean,
    default: false
  },
  guest_name: {
    type: String,
    required: function() { return this.is_guest; }
  },
  guest_email: {
    type: String,
    required: false
  },
  guest_phone: {
    type: String,
    required: function() { return this.is_guest; }
  },
  guest_address: {
    type: String,
    required: function() { return this.is_guest; }
  },
  room_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Room',
    required: true
  },
  room_type: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'RoomType',
    required: true
  },
  booking_dates: [{
    type: Date,
    required: true
  }],
  // Add check-in and check-out time fields
  check_in_time: {
    type: String,
    default: "14:00", // Default check-in time (2 PM)
    validate: {
      validator: function(v) {
        return /^([01]\d|2[0-3]):([0-5]\d)$/.test(v); // Validate time format (HH:MM)
      },
      message: props => `${props.value} is not a valid time format! Use HH:MM (24-hour format)`
    }
  },
  check_out_time: {
    type: String,
    default: "12:00", // Default check-out time (12 PM)
    validate: {
      validator: function(v) {
        return /^([01]\d|2[0-3]):([0-5]\d)$/.test(v); // Validate time format (HH:MM)
      },
      message: props => `${props.value} is not a valid time format! Use HH:MM (24-hour format)`
    }
  },
  booking_status: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'cancelled', 'checked-in', 'checked-out', 'no-show'],
    default: 'pending'
  },
  guests: {
    adults: {
      type: Number,
      required: true,
      min: 1
    },
    children: {
      type: Number,
      default: 0
    }
  },
  special_requests: {
    type: String
  },
  payment: {
    method: {
      type: String,
      enum: ['credit_card', 'debit_card', 'paypal', 'bank_transfer', 'cash', 'upi', 'wallet'],
      required: true
    },
    amount: {
      type: Number,
      required: true
    },
    advance_amount: {
      type: Number,
      default: 0
    },
    balance_amount: {
      type: Number,
      default: function() {
        // Ensure we're dealing with valid numbers
        const amount = this.amount || 0;
        const advanceAmount = this.advance_amount || 0;
        return Math.max(0, amount - advanceAmount);
      }
    },
    currency: {
      type: String,
      default: 'INR'
    },
    status: {
      type: String,
      enum: ['pending', 'partially_paid', 'completed', 'refunded', 'failed'],
      default: 'pending'
    },
    transaction_id: String,
    paid_at: Date,
    processed_by: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    notes: String,
    // Add payment history to track all payments
    payment_history: [{
      type: {
        type: String,
        enum: ['advance', 'balance', 'full', 'refund'],
        required: true
      },
      amount: {
        type: Number,
        required: true
      },
      method: {
        type: String,
        enum: ['credit_card', 'debit_card', 'paypal', 'bank_transfer', 'cash', 'upi', 'wallet'],
        required: true
      },
      transaction_id: String,
      paid_at: {
        type: Date,
        default: Date.now
      },
      processed_by: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      },
      notes: String
    }]
  },
  check_in: {
    date: Date,
    time: String, // Store actual check-in time
    processed_by: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    notes: String
  },
  check_out: {
    date: Date,
    time: String, // Store actual check-out time
    processed_by: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    notes: String
  },
  cancellation: {
    date: Date,
    reason: String,
    processed_by: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    fee: Number,
    refund_amount: Number
  }
}, { timestamps: true });

// Add method to check room availability with time consideration
bookingSchema.statics.isRoomAvailableForDateAndTime = async function(roomId, date, checkInTime, checkOutTime) {
  const formattedDate = new Date(date);
  formattedDate.setHours(0, 0, 0, 0); // Set to start of day for date comparison
  
  // Find bookings for this room on the specified date
  const bookingsOnDate = await this.find({
    room_id: roomId,
    booking_status: { $in: ['pending', 'approved', 'checked-in'] },
    booking_dates: formattedDate
  });
  
  // If no bookings on this date, room is available
  if (bookingsOnDate.length === 0) {
    return true;
  }
  
  // Convert times to minutes for easier comparison
  const requestedCheckInMinutes = convertTimeToMinutes(checkInTime);
  const requestedCheckOutMinutes = convertTimeToMinutes(checkOutTime);
  
  // Check for time conflicts with existing bookings
  for (const booking of bookingsOnDate) {
    const existingCheckInMinutes = convertTimeToMinutes(booking.check_in_time);
    const existingCheckOutMinutes = convertTimeToMinutes(booking.check_out_time);
    
    // Check if there's an overlap in time
    // An overlap occurs if:
    // 1. Requested check-in is before existing check-out AND
    // 2. Requested check-out is after existing check-in
    if (requestedCheckInMinutes < existingCheckOutMinutes && 
        requestedCheckOutMinutes > existingCheckInMinutes) {
      return false; // Time conflict found
    }
  }
  
  return true; // No time conflicts found
};

// Helper function to convert HH:MM time to minutes for comparison
function convertTimeToMinutes(timeString) {
  const [hours, minutes] = timeString.split(':').map(Number);
  return hours * 60 + minutes;
}

// Method to approve booking
bookingSchema.methods.approveBooking = async function(notes) {
  if (this.booking_status !== 'pending') {
    throw new Error('Only pending bookings can be approved');
  }
  
  this.booking_status = 'approved';
  
  if (notes) {
    this.notes = notes;
  }
  
  return this.save();
};

// Method to reject booking
bookingSchema.methods.rejectBooking = async function(reason, adminId) {
  if (this.booking_status !== 'pending') {
    throw new Error('Only pending bookings can be rejected');
  }
  
  this.booking_status = 'rejected';
  
  if (reason) {
    this.cancellation = {
      date: new Date(),
      reason,
      processed_by: adminId
    };
  }
  
  // Update room status back to available
  const room = await Room.findById(this.room_id);
  if (room && room.current_status === 'reserved') {
    room.current_status = 'available';
    await room.save();
  }
  
  return this.save();
};

// Method to cancel booking
bookingSchema.methods.cancelBooking = async function(reason, adminId, cancellationFee = 0, refundAmount = 0) {
  if (!['pending', 'approved'].includes(this.booking_status)) {
    throw new Error('Only pending or approved bookings can be cancelled');
  }
  
  this.booking_status = 'cancelled';
  
  this.cancellation = {
    date: new Date(),
    reason,
    processed_by: adminId,
    fee: cancellationFee,
    refund_amount: refundAmount
  };
  
  // Update room status back to available
  const room = await Room.findById(this.room_id);
  if (room && room.current_status === 'reserved') {
    room.current_status = 'available';
    await room.save();
  }
  
  return this.save();
};

// Method to check in
bookingSchema.methods.checkIn = async function(adminId, notes, actualCheckInTime) {
  if (this.booking_status !== 'approved') {
    throw new Error('Only approved bookings can be checked in');
  }
  
  this.booking_status = 'checked-in';
  
  this.check_in = {
    date: new Date(),
    time: actualCheckInTime || this.check_in_time, // Use actual time or default
    processed_by: adminId,
    notes: notes || ''
  };
  
  // Update room status to occupied
  const room = await Room.findById(this.room_id);
  if (room) {
    room.current_status = 'occupied';
    await room.save();
  }
  
  return this.save();
};

// Method to check out
bookingSchema.methods.checkOut = async function(adminId, notes, actualCheckOutTime) {
  if (this.booking_status !== 'checked-in') {
    throw new Error('Only checked-in bookings can be checked out');
  }
  
  // Check if there's an unpaid balance
  if (this.payment && this.payment.balance_amount > 0 && this.payment.status !== 'completed') {
    throw new Error('Cannot check out with unpaid balance. Please collect payment first.');
  }
  
  this.booking_status = 'checked-out';
  
  this.check_out = {
    date: new Date(),
    time: actualCheckOutTime || this.check_out_time, // Use actual time or default
    processed_by: adminId,
    notes: notes || ''
  };
  
  // Update room status to cleaning
  const room = await Room.findById(this.room_id);
  if (room) {
    room.current_status = 'cleaning';
    await room.save();
  }
  
  return this.save();
};

// Method to mark as no-show
bookingSchema.methods.markAsNoShow = async function(adminId, notes) {
  if (this.booking_status !== 'approved') {
    throw new Error('Only approved bookings can be marked as no-show');
  }
  
  this.booking_status = 'no-show';
  
  if (notes) {
    this.notes = notes;
  }
  
  // Update room status back to available
  const room = await Room.findById(this.room_id);
  if (room && room.current_status === 'reserved') {
    room.current_status = 'available';
    await room.save();
  }
  
  return this.save();
};

// Method to record payment
bookingSchema.methods.recordPayment = async function(method, amount, transactionId, paidAt, processedBy) {
  this.payment = {
    method,
    amount,
    status: 'completed',
    transaction_id: transactionId,
    paid_at: paidAt,
    processed_by: processedBy
  };
  
  return this.save();
};

// Pre-save middleware to generate custom booking ID
bookingSchema.pre('save', async function(next) {
  try {
    // Only generate booking_id for new documents
    if (this.isNew && !this.booking_id) {
      // Get the next sequence number from the counter
      const sequenceNumber = await Counter.getNextSequence('booking_id');
      
      // Format the sequence number with leading zeros (e.g., 001, 010, 100)
      const formattedNumber = String(sequenceNumber).padStart(3, '0');
      
      // Set the custom booking ID
      this.booking_id = `TTS${formattedNumber}`;
      
      // Log for debugging
      console.log(`Generated booking ID: ${this.booking_id}`);
    }
    next();
  } catch (error) {
    console.error('Error generating booking ID:', error);
    next(error);
  }
});

const Booking = mongoose.model('Booking', bookingSchema);

export default Booking;
