
import fs from 'fs';
import appRoot from 'app-root-path';
import { errorResponse, successResponse } from '../config/app.response.js';
import User from '../models/user.model.js';
import logger from '../middleware/winston.logger.js';
import MyQueryHelper from '../config/api.feature.js';

// TODO: Controller for get user info
const getUser = async (req, res) => {
  try {
    const { user } = req;

    if (!user) {
      return res.status(404).json(errorResponse(
        4,
        'UNKNOWN ACCESS',
        'User does not exist'
      ));
    }

    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'User information get successful',
      {
        userName: user.userName,
        fullName: user.fullName,
        email: user.email,
        phone: user.phone,
        avatar: `${process.env.APP_BASE_URL}${user.avatar}`,
        gender: user.gender,
        dob: user.dob,
        address: user.address,
        role: user.role,
        verified: user.verified,
        status: user.status,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    ));
  } catch (error) {
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error
    ));
  }
};

// TODO: Controller for get user info using id by admin
const getUserById = async (req, res) => {
  try {
    // check if user exists
    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json(errorResponse(
        4,
        'UNKNOWN ACCESS',
        'User does not exist'
      ));
    }

    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'User information get successful',
      {
        id: user._id,
        userName: user.userName,
        fullName: user.fullName,
        email: user.email,
        phone: user.phone,
        avatar: `${process.env.APP_BASE_URL}${user.avatar}`,
        gender: user.gender,
        dob: user.dob,
        address: user.address,
        role: user.role,
        verified: user.verified,
        status: user.status,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    ));
  } catch (error) {
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error
    ));
  }
};

// TODO: Controller for update user info
const updateUser = async (req, res) => {
  try {
    const { user } = req;
    const {
      fullName, phone, gender, dob, address
    } = req.body;

    if (!user) {
      return res.status(404).json(errorResponse(
        4,
        'UNKNOWN ACCESS',
        'User does not exist'
      ));
    }

    if (fullName && phone && gender && dob && address) {
      // update user info & save database
      const updatedUser = await User.findByIdAndUpdate(
        user._id,
        {
          fullName, phone, gender, dob, address
        },
        { runValidators: true, new: true }
      );

      res.status(200).json(successResponse(
        0,
        'SUCCESS',
        'User info updated successful',
        {
          userName: updatedUser.userName,
          fullName: updatedUser.fullName,
          email: updatedUser.email,
          phone: updatedUser.phone,
          avatar: `${process.env.APP_BASE_URL}${updatedUser.avatar}`,
          gender: updatedUser.gender,
          dob: updatedUser.dob,
          address: updatedUser.address,
          role: updatedUser.role,
          verified: updatedUser.verified,
          status: updatedUser.status,
          createdAt: updatedUser.createdAt,
          updatedAt: updatedUser.updatedAt
        }
      ));
    } else {
      if (!fullName) {
        return res.status(400).json(errorResponse(
          1,
          'FAILED',
          'User `fullName` field is required'
        ));
      }
      if (!phone) {
        return res.status(400).json(errorResponse(
          1,
          'FAILED',
          'User `phone` field is required'
        ));
      }
      if (!gender) {
        return res.status(400).json(errorResponse(
          1,
          'FAILED',
          'User `gender` field is required'
        ));
      }
      if (!address) {
        return res.status(400).json(errorResponse(
          1,
          'FAILED',
          'User `address` field is required'
        ));
      }
    }
  } catch (error) {
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error
    ));
  }
};

// TODO: Controller for update user avatar/image
const avatarUpdate = async (req, res) => {
  try {
    const { user, file } = req;

    if (!user) {
      return res.status(404).json(errorResponse(
        4,
        'UNKNOWN ACCESS',
        'User does not exist'
      ));
    }

    if (file) {
      if (user?.avatar?.includes('/uploads/users')) {
        fs.unlink(`${appRoot}/public/${user.avatar}`, (err) => {
          if (err) { logger.error(err); }
        });
      }

      const updatedUser = await User.findByIdAndUpdate(
        user._id,
        { avatar: `/uploads/users/${file.filename}` },
        { runValidators: true, new: true }
      );

      res.status(200).json(successResponse(
        0,
        'SUCCESS',
        'User avatar updated successful',
        {
          userName: updatedUser.userName,
          fullName: updatedUser.fullName,
          email: updatedUser.email,
          phone: updatedUser.phone,
          avatar: `${process.env.APP_BASE_URL}${updatedUser.avatar}`,
          gender: updatedUser.gender,
          dob: updatedUser.dob,
          address: updatedUser.address,
          role: updatedUser.role,
          verified: updatedUser.verified,
          status: updatedUser.status,
          createdAt: updatedUser.createdAt,
          updatedAt: updatedUser.updatedAt
        }
      ));
    } else {
      return res.status(400).json(errorResponse(
        1,
        'FAILED',
        'User `avatar` field is required'
      ));
    }
  } catch (error) {
    // if any error delete uploaded avatar image
    if (req?.file?.filename) {
      fs.unlink(`${appRoot}/public/uploads/users/${req.file.filename}`, (err) => {
        if (err) { logger.error(err); }
      });
    }
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error
    ));
  }
};

const blockedUser = async (req, res) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json(errorResponse(
        4,
        'UNKNOWN ACCESS',
        'User does not exist'
      ));
    } else {
      user.status = 'blocked';
      await user.save();
      res.status(200).json(successResponse(
        0,
        'SUCCESS',
        'User blocked successful'
      ));
    }
  } catch (error) {
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error
    ));
  }
};

const unblockedUser = async (req, res) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json(errorResponse(
        4,
        'UNKNOWN ACCESS',
        'User does not exist'
      ));
    } else {
      user.status = 'active';
      await user.save();
      res.status(200).json(successResponse(
        0,
        'SUCCESS',
        'User unblocked successful'
      ));
    }
  } catch (error) {
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error
    ));
  }
};

const deleteUser = async (req, res) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json(errorResponse(
        4,
        'UNKNOWN ACCESS',
        'User does not exist'
      ));
    } else {
      await user.remove();
      res.status(200).json(successResponse(
        0,
        'SUCCESS',
        'User deleted successful'
      ));
    }
  } catch (error) {
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error
    ));
  }
};

const getUsersList = async (req, res) => {
  try {
    // Build query based on request parameters
    let query = {};
    
    // Filter by role if provided
    if (req.query.role) {
      // Handle special case for 'staff' role
      if (req.query.role === 'staff') {
        // In this system, staff users have role 'staff'
        query.role = 'staff';
      } else {
        query.role = req.query.role;
      }
    }
    
    // Filter by status if provided
    if (req.query.status) {
      query.status = req.query.status;
    }
    
    // Count total documents matching the query
    const totalCount = await User.countDocuments(query);
    
    // Create base query with filters
    const baseQuery = User.find(query)
      .select('-password')
      .sort('-createdAt')
      .lean();
    
    // Apply search, filtering, sorting, and pagination
    const userQuery = new MyQueryHelper(baseQuery, req.query)
      .search(['userName', 'fullName', 'email', 'phone'])
      .filter()
      .sort()
      .paginate();
    
    const users = await userQuery.query;
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const totalPages = Math.ceil(totalCount / limit);
    
    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Users list retrieved successfully',
      {
        rows: users,
        total_rows: totalCount,
        current_page: page,
        total_pages: totalPages,
        has_previous: page > 1,
        has_next: page < totalPages,
        filters: {
          role: req.query.role || 'all',
          status: req.query.status || 'all'
        }
      }
    ));
  } catch (error) {
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error
    ));
  }
};

const deleteUserById = async (req, res) => {
  try {
    const user = await User.findById(req.params.id);

    if (!user) {
      return res.status(404).json(errorResponse(
        4,
        'UNKNOWN ACCESS',
        'User does not exist'
      ));
    } else {
      await user.remove();
      res.status(200).json(successResponse(
        0,
        'SUCCESS',
        'User deleted successful'
      ));
    }
  } catch (error) {
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error
    ));
  }
};    



export {
  getUser,
  getUserById,
  updateUser,
  avatarUpdate,
  blockedUser,
  unblockedUser,
  deleteUser,
  deleteUserById,
  getUsersList
};
