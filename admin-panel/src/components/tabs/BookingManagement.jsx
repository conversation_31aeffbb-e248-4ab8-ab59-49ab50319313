import { 
  CalendarOutlined, DollarOutlined, CheckCircleOutlined, 
  CloseCircleOutlined, UserOutlined, HomeOutlined, SyncOutlined,
  DeleteOutlined, ExclamationCircleFilled, FilterOutlined,
  EyeOutlined, EditOutlined, CreditCardOutlined,
  PlusCircleOutlined
} from '@ant-design/icons';
import { Drawer, Button, Table, Tag, Space, Form, Input, Select, DatePicker, Descriptions, 
  Statistic, Card, Row, Col, Tabs, Typography, Skeleton, Badge, Avatar, Tooltip, Divider,
  Modal,Alert,
  Pagination
 } from 'antd';
import { useState, useEffect } from 'react';
import ApiService from '../../utils/apiService';
import notificationWithIcon from '../../utils/notification';
import dayjs from 'dayjs';
import { format } from 'date-fns';
import Swal from 'sweetalert2';
import { Empty, InputNumber } from 'antd';

const { Title, Text } = Typography;
const { TabPane } = Tabs;
const { Option } = Select;
const { RangePicker } = DatePicker;

const BookingManagement = () => {
  useEffect(() => {
    // Add custom CSS for the booking management page
    const style = document.createElement('style');
    style.innerHTML = `
      .booking-management .custom-tabs .ant-tabs-nav {
        margin-bottom: 16px;
      }
      
      .booking-management .tab-label {
        display: flex;
        align-items: center;
        gap: 8px;
        font-weight: 500;
      }
      
      .booking-management .custom-table .ant-table-thead > tr > th {
        background-color: #f0f5ff;
        color: #1d4ed8;
        font-weight: 600;
      }
      
      .booking-management .action-buttons .details-btn:hover {
        background-color: #1d4ed8;
        border-color: #1d4ed8;
      }
      
      .booking-management .action-buttons .status-btn:hover {
        background-color: #f59e0b;
        border-color: #f59e0b;
        color: white;
      }
      
      .booking-management .action-buttons .payment-btn:hover {
        background-color: #10b981;
        border-color: #10b981;
        color: white;
      }
      
      .booking-management .action-buttons .delete-btn:hover {
        background-color: #ef4444;
        border-color: #ef4444;
      }
      
      .booking-management .ant-card {
        border-radius: 8px;
        overflow: hidden;
      }
      
      .booking-management .ant-table {
        border-radius: 8px;
        overflow: hidden;
      }
    `;
    document.head.appendChild(style);
    
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  const [bookings, setBookings] = useState([]);
  const [loading, setLoading] = useState(false);
  const [statistics, setStatistics] = useState(null);
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [detailsVisible, setDetailsVisible] = useState(false);
  const [statusModalVisible, setStatusModalVisible] = useState(false);
  const [paymentModalVisible, setPaymentModalVisible] = useState(false);
  const [statusForm] = Form.useForm();
  const [paymentForm] = Form.useForm();
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 25,
    total: 0
  });
  const [filters, setFilters] = useState({
    status: '',
    dateRange: null
  });

  // Add a new state for the balance payment modal
  const [balancePaymentModalVisible, setBalancePaymentModalVisible] = useState(false);
  const [balancePaymentForm] = Form.useForm();

  // Fetch bookings
  const fetchBookings = async (page = 1, limit = 25, status = '', startDate = '', endDate = '') => {
    setLoading(true);
    try {
      let url = `/api/v1/bookings?page=${page}&limit=${limit}`;
      
      if (status) {
        url += `&status=${status}`;
      }
      
      if (startDate && endDate) {
        url += `&start_date=${startDate}&end_date=${endDate}`;
      }
      
      const response = await ApiService.get(url);
      
      if (response?.result_code === 0) {
        setBookings(response.result.data.bookings);
        setPagination({
          current: response.result.data.pagination.page,
          pageSize: response.result.data.pagination.limit,
          total: response.result.data.pagination.total
        });
      }
    } catch (error) {
      notificationWithIcon('error', 'ERROR', 'Failed to fetch bookings');
    } finally {
      setLoading(false);
    }
  };

  // Fetch booking statistics
  const fetchStatistics = async () => {
    try {
      const response = await ApiService.get('/api/v1/booking-statistics');
      
      if (response?.result_code === 0) {
        setStatistics(response.result.data);
      }
    } catch (error) {
      notificationWithIcon('error', 'ERROR', 'Failed to fetch booking statistics');
    }
  };

  // Handle table pagination change
  const handleTableChange = (pagination) => {
    fetchBookings(
      pagination.current,
      pagination.pageSize,
      filters.status,
      filters.dateRange ? filters.dateRange[0].format('YYYY-MM-DD') : '',
      filters.dateRange ? filters.dateRange[1].format('YYYY-MM-DD') : ''
    );
  };

  // Handle filter changes
  const handleStatusChange = (value) => {
    setFilters({
      ...filters,
      status: value
    });
    fetchBookings(1, pagination.pageSize, value, 
      filters.dateRange ? filters.dateRange[0].format('YYYY-MM-DD') : '',
      filters.dateRange ? filters.dateRange[1].format('YYYY-MM-DD') : ''
    );
  };

  const handleDateRangeChange = (dates) => {
    setFilters({
      ...filters,
      dateRange: dates
    });
    
    if (dates) {
      fetchBookings(
        1, 
        pagination.pageSize, 
        filters.status,
        dates[0].format('YYYY-MM-DD'),
        dates[1].format('YYYY-MM-DD')
      );
    } else {
      fetchBookings(1, pagination.pageSize, filters.status, '', '');
    }
  };

  // View booking details
  const viewBookingDetails = (booking) => {
    setSelectedBooking(booking);
    setDetailsVisible(true);
  };

  // Update booking status
  const showStatusModal = (booking) => {
    setSelectedBooking(booking);
    statusForm.setFieldsValue({
      status: booking.booking_status,
      notes: ''
    });
    setStatusModalVisible(true);
  };

  const handleStatusUpdate = async (values) => {
    try {
      // If status is being changed to checked-out, use our special handling
      if (values.status === 'checked-out') {
        handleCheckOut(selectedBooking);
        setStatusModalVisible(false);
        return;
      }
      
      // For other status changes, proceed as normal
      const response = await ApiService.put(`/api/v1/bookings/${selectedBooking._id}/status`, values);
      
      if (response?.result_code === 0) {
        notificationWithIcon('success', 'SUCCESS', `Booking status updated to ${values.status}`);
        setStatusModalVisible(false);
        fetchBookings(pagination.current, pagination.pageSize, filters.status);
      }
    } catch (error) {
      notificationWithIcon('error', 'ERROR', 'Failed to update booking status');
    }
  };

  // Record payment
  const showPaymentModal = (booking) => {
    setSelectedBooking(booking);
    paymentForm.resetFields();
    
    // Calculate remaining balance
    const totalAmount = booking.payment?.amount || 0;
    const paidAmount = booking.payment?.advance_amount || 0;
    const remainingBalance = Math.max(0, totalAmount - paidAmount);
    
    // Check if payment history exists
    const paymentHistory = booking.payment?.payment_history || [];
    
    // If there's an advance payment but no payment history, we need to create one
    if (paidAmount > 0 && paymentHistory.length === 0) {
      // This is a legacy booking with advance payment but no history
      // We'll handle this when recording the next payment
      console.log('Legacy booking with advance payment but no payment history');
    }
    
    // Pre-fill form with existing payment data and calculated values
    paymentForm.setFieldsValue({
      method: booking.payment?.method || 'cash',
      amount: remainingBalance, // Default to remaining balance
      status: remainingBalance === 0 ? 'completed' : 'partially_paid',
      advance_amount: paidAmount, // Already paid amount
      balance_amount: remainingBalance, // Remaining balance
      transaction_id: '',
      currency: booking.payment?.currency || 'INR',
      notes: `Additional payment on ${format(new Date(), 'yyyy-MM-dd')}`
    });
    
    setPaymentModalVisible(true);
  };

  const handlePaymentRecord = async (values) => {
    try {
      // Get existing payment data
      const existingPayment = selectedBooking.payment || {};
      const existingAdvance = existingPayment.advance_amount || 0;
      const existingTotal = existingPayment.amount || 0;
      
      // Calculate new payment amounts
      const newPaymentAmount = Number(values.amount) || 0;
      const totalPaid = existingAdvance + newPaymentAmount;
      const remainingBalance = Math.max(0, existingTotal - totalPaid);
      
      // Determine payment type and status
      let paymentType, paymentStatus;
      
      if (totalPaid >= existingTotal) {
        // Full payment completed
        paymentType = 'balance';
        paymentStatus = 'completed';
      } else if (existingAdvance === 0) {
        // First payment but not full
        paymentType = 'advance';
        paymentStatus = 'partially_paid';
      } else {
        // Additional payment but still not full
        paymentType = 'partial';
        paymentStatus = 'partially_paid';
      }
      
      // Create a new payment history entry
      const paymentEntry = {
        type: paymentType,
        amount: newPaymentAmount,
        method: values.method,
        transaction_id: values.transaction_id || '',
        paid_at: new Date().toISOString(),
        notes: values.notes || `${paymentType} payment`
      };
      
      // Get existing payment history or initialize empty array
      const paymentHistory = existingPayment.payment_history || [];
      
      // If there's an advance payment but no payment history, create a historical entry
      let updatedPaymentHistory = [...paymentHistory];
      if (existingAdvance > 0 && paymentHistory.length === 0) {
        // Create a historical entry for the advance payment
        updatedPaymentHistory.push({
          type: 'advance',
          amount: existingAdvance,
          method: existingPayment.method || 'cash',
          transaction_id: existingPayment.transaction_id || '',
          paid_at: existingPayment.paid_at || new Date().toISOString(),
          notes: 'Initial advance payment (reconstructed)'
        });
      }
      
      // Add the new payment entry
      updatedPaymentHistory.push(paymentEntry);
      
      const payload = {
        payment: {
          method: values.method,
          amount: existingTotal, // Keep total amount unchanged
          status: paymentStatus,
          advance_amount: totalPaid, // Update advance to total paid so far
          balance_amount: remainingBalance, // Update remaining balance
          currency: values.currency || existingPayment.currency || 'INR',
          transaction_id: values.transaction_id || existingPayment.transaction_id || '',
          notes: values.notes || existingPayment.notes || '',
          paid_at: paymentStatus === 'completed' ? new Date().toISOString() : existingPayment.paid_at,
          // Add the new payment to payment history
          payment_history: updatedPaymentHistory
        }
      };
      
      console.log('Payment payload:', payload);
      
      const response = await ApiService.put(`/api/v1/bookings/${selectedBooking._id}/payment`, payload);
      
      if (response?.result_code === 0) {
        notificationWithIcon('success', 'SUCCESS', 'Payment information updated successfully');
        
        // Show additional success message if payment is completed
        if (paymentStatus === 'completed') {
          notificationWithIcon('success', 'PAYMENT COMPLETED', 'Full payment has been received');
        }
        
        setPaymentModalVisible(false);
        fetchBookings(pagination.current, pagination.pageSize, filters.status);
      }
    } catch (error) {
      notificationWithIcon('error', 'ERROR', 'Failed to update payment information');
    }
  };

  // Delete booking with SweetAlert confirmation
  const handleDeleteBooking = (booking) => {
    Swal.fire({
      title: 'Are you sure?',
      text: "You won't be able to revert this deletion!",
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: 'Yes, delete it!',
      cancelButtonText: 'No, cancel'
    }).then(async (result) => {
      if (result.isConfirmed) {
        try {
          const response = await ApiService.delete(`/api/v1/admin/bookings/${booking._id}`);
          
          if (response?.result_code === 0) {
            Swal.fire(
              'Deleted!',
              'The booking has been deleted successfully.',
              'success'
            );
            fetchBookings(pagination.current, pagination.pageSize, filters.status);
          } else {
            throw new Error(response?.result?.error || 'Failed to delete booking');
          }
        } catch (error) {
          Swal.fire(
            'Error!',
            error.message || 'Failed to delete booking',
            'error'
          );
        }
      }
    });
  };

  // Add a function to handle check-out with balance payment verification
  const handleCheckOut = (booking) => {
    // Check if there's an unpaid balance
    const hasUnpaidBalance = booking.payment && 
      booking.payment.balance_amount > 0 && 
      booking.payment.status !== 'completed';
    
    if (hasUnpaidBalance) {
      // Show balance payment modal if there's an unpaid balance
      setSelectedBooking(booking);
      balancePaymentForm.resetFields();
      balancePaymentForm.setFieldsValue({
        method: booking.payment.method,
        amount: booking.payment.balance_amount,
        transaction_id: '',
        currency: booking.payment.currency || 'INR',
        notes: 'Balance payment at check-out'
      });
      setBalancePaymentModalVisible(true);
    } else {
      // If balance is already paid, proceed directly to check-out
      proceedToCheckOut(booking);
    }
  };

  // Add a function to handle balance payment
  const handleBalancePayment = async (values) => {
    try {
      // Create a new payment history entry for the balance payment
      const balancePaymentEntry = {
        type: 'balance',
        amount: selectedBooking.payment.balance_amount,
        method: values.method,
        transaction_id: values.transaction_id || '',
        paid_at: new Date().toISOString(),
        notes: values.notes || 'Balance payment at check-out'
      };
      
      // Get existing payment history or initialize empty array
      const paymentHistory = selectedBooking.payment.payment_history || [];
      
      const payload = {
        payment: {
          method: selectedBooking.payment.method, // Keep original payment method
          amount: selectedBooking.payment.amount, // Keep the total amount the same
          status: 'completed', // Mark as completed since full payment is now made
          advance_amount: selectedBooking.payment.advance_amount, // Keep advance amount unchanged
          balance_amount: 0, // Set balance to 0 since it's now paid
          currency: selectedBooking.payment.currency || 'INR',
          transaction_id: selectedBooking.payment.transaction_id, // Keep original transaction ID
          paid_at: selectedBooking.payment.paid_at, // Keep original payment date
          notes: selectedBooking.payment.notes, // Keep original notes
          // Add the new balance payment to payment history
          payment_history: [...paymentHistory, balancePaymentEntry]
        }
      };
      
      const response = await ApiService.put(`/api/v1/bookings/${selectedBooking._id}/payment`, payload);
      
      if (response?.result_code === 0) {
        notificationWithIcon('success', 'SUCCESS', 'Balance payment recorded successfully');
        setBalancePaymentModalVisible(false);
        
        // Now proceed to check-out
        proceedToCheckOut(selectedBooking);
        
        // Refresh bookings list
        fetchBookings(pagination.current, pagination.pageSize, filters.status);
      }
    } catch (error) {
      notificationWithIcon('error', 'ERROR', 'Failed to record balance payment');
    }
  };

  // Add a function to proceed with check-out
  const proceedToCheckOut = async (booking) => {
    try {
      const values = {
        notes: `Checked out on ${format(new Date(), 'yyyy-MM-dd HH:mm')}`,
        actual_check_out_time: format(new Date(), 'HH:mm')
      };
      
      const response = await ApiService.put(`/api/v1/bookings/${booking._id}/check-out`, values);
      
      if (response?.result_code === 0) {
        notificationWithIcon('success', 'SUCCESS', 'Guest checked out successfully');
        fetchBookings(pagination.current, pagination.pageSize, filters.status);
      }
    } catch (error) {
      notificationWithIcon('error', 'ERROR', 'Failed to check out guest');
    }
  };

  // Initialize
  useEffect(() => {
    fetchBookings();
    fetchStatistics();
  }, []);

  // Table columns
  const columns = [
    {
      title: 'Booking ID',
      dataIndex: 'booking_id',
      key: 'booking_id',
      render: (text) => <Tag color="blue">{text}</Tag>
    },
    {
      title: 'Guest',
      dataIndex: 'user',
      key: 'user',
      render: (user, record) => {
        // Check if it's a guest booking
        if (record.is_guest) {
          return (
            <div className="flex items-center space-x-2">
              <Avatar 
                icon={<UserOutlined />} 
                className="bg-blue-400"
                size="small"
              />
              <div className="flex flex-col">
                <span className="font-medium">{record.guest_name || 'Guest'}</span>
                <span className="text-xs text-gray-500">{record.guest_email || 'No email'}</span>
                {record.guest_phone && <span className="text-xs text-gray-500">{record.guest_phone}</span>}
                <Tag color="blue" className="mt-1 w-14 text-center text-xs">Guest</Tag>
              </div>
            </div>
          );
        }
        
        // Regular user booking
        if (!user) return <span>Guest User</span>;
        if (typeof user === 'string') return <span>{user}</span>;
        
        return (
          <div className="flex items-center space-x-2">
            <Avatar 
              icon={<UserOutlined />} 
              className="bg-purple-500"
              size="small"
            />
            <div className="flex flex-col">
              <span className="font-medium">{user.fullName || user.userName || 'Unknown'}</span>
              {user.email && <span className="text-xs text-gray-500">{user.email}</span>}
            </div>
          </div>
        );
      },
    },
    {
      title: 'Room',
      dataIndex: 'room',
      key: 'room',
      render: (room) => {
        if (!room) return <span className="text-gray-400">Not assigned</span>;
        
        return (
          <div className="flex items-center">
            <Badge status="processing" color="green" />
            <span className="ml-2">
              Room {room.room_number} 
              <span className="text-xs text-gray-500 ml-1">(Floor {room.floor})</span>
            </span>
          </div>
        );
      }
    },
    {
      title: 'Dates',
      key: 'dates',
      render: (_, record) => {
        if (!record.booking_dates || !Array.isArray(record.booking_dates) || record.booking_dates.length === 0) {
          return <span className="text-gray-400">No dates</span>;
        }
        
        try {
          const startDate = new Date(record.booking_dates[0]);
          const endDate = new Date(record.booking_dates[record.booking_dates.length - 1]);
          
          // Check if dates are valid
          if (isNaN(startDate) || isNaN(endDate)) {
            return <span className="text-gray-400">Invalid dates</span>;
          }
          
          const nights = Math.round((endDate - startDate) / (1000 * 60 * 60 * 24));
          
          return (
            <div className="flex flex-col">
              <div className="flex items-center">
                <CalendarOutlined className="text-blue-500 mr-1" />
                <span>
                  {format(startDate, 'MMM dd')} - {format(endDate, 'MMM dd, yyyy')}
                </span>
              </div>
              <span className="text-xs text-gray-500 mt-1">
                {nights} {nights === 1 ? 'night' : 'nights'}
              </span>
            </div>
          );
        } catch (error) {
          console.error('Error formatting dates:', error);
          return <span className="text-gray-400">Error formatting dates</span>;
        }
      },
    },
    {
      title: 'Status',
      dataIndex: 'booking_status',
      key: 'booking_status',
      render: status => {
        let color = 'blue';
        let icon = null;
        
        if (status === 'approved') {
          color = 'green';
          icon = <CheckCircleOutlined />;
        } else if (status === 'rejected' || status === 'cancelled') {
          color = 'red';
          icon = <CloseCircleOutlined />;
        } else if (status === 'checked-in') {
          color = 'purple';
          icon = <HomeOutlined />;
        } else if (status === 'checked-out') {
          color = 'cyan';
          icon = <CheckCircleOutlined />;
        } else if (status === 'no-show') {
          color = 'orange';
          icon = <CloseCircleOutlined />;
        }
        
        return (
          <Tag color={color} icon={icon} className="px-2 py-1 capitalize">
            {status}
          </Tag>
        );
      },
    },
    {
      title: 'Payment',
      dataIndex: 'payment',
      key: 'payment',
      render: payment => {
        if (!payment) return <Tag color="default">NOT RECORDED</Tag>;
        
        const status = typeof payment === 'object' ? (payment.status || 'NOT RECORDED') : 'NOT RECORDED';
        let color = 'default';
        let icon = null;
        
        if (status === 'completed') {
          color = 'green';
          icon = <CheckCircleOutlined />;
        } else if (status === 'pending') {
          color = 'orange';
          icon = <SyncOutlined spin />;
        } else if (status === 'refunded') {
          color = 'blue';
          icon = <DollarOutlined />;
        } else if (status === 'failed') {
          color = 'red';
          icon = <CloseCircleOutlined />;
        } else if (status === 'partially_refunded') {
          color = 'purple';
          icon = <DollarOutlined />;
        }
        
        return (
          <div className="flex flex-col gap-1">
            <Tag color={color} icon={icon} className="px-2 py-1 capitalize">
              {status.replace(/_/g, ' ')}
            </Tag>
            {payment.amount && (
              <div className="text-xs font-medium">
                {payment.currency || 'INR'} {payment.amount.toFixed(2)}
              </div>
            )}
            {payment.method && (
              <div className="text-xs text-gray-500 capitalize">
                {payment.method.replace(/_/g, ' ')}
              </div>
            )}
          </div>
        );
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space size="small" className="action-buttons">
          <Tooltip title="View Details">
            <Button 
              type="primary" 
              size="small" 
              icon={<EyeOutlined />}
              onClick={() => viewBookingDetails(record)}
              className="details-btn"
            />
          </Tooltip>
          <Tooltip title="Update Status">
            <Button 
              type="default" 
              size="small" 
              icon={<EditOutlined />}
              onClick={() => showStatusModal(record)}
              className="status-btn"
            />
          </Tooltip>
          <Tooltip title="Payment Info">
            <Button 
              type="default" 
              size="small" 
              icon={<CreditCardOutlined />}
              onClick={() => showPaymentModal(record)}
              className="payment-btn"
            />
          </Tooltip>
          <Tooltip title="Delete Booking">
            <Button 
              type="primary" 
              danger
              size="small" 
              icon={<DeleteOutlined />}
              onClick={() => handleDeleteBooking(record)}
              className="delete-btn"
            />
          </Tooltip>
        </Space>
      ),
    },
  ];

  return (
    <div className="booking-management">
      <Tabs defaultActiveKey="1" className="custom-tabs">
        <TabPane 
          tab={
            <span className="tab-label">
              <CalendarOutlined />
              Bookings
            </span>
          } 
          key="1"
        >
          <Card 
            className="mb-4 shadow-md hover:shadow-lg transition-all duration-300"
            bordered={false}
          >
            <div className="flex flex-wrap justify-between items-center mb-4">
              <div className="mb-2 md:mb-0">
                <Title level={4} className="text-color-primary m-0">
                  <CalendarOutlined className="mr-2" /> Booking Management
                </Title>
                <Text type="secondary">Manage all hotel bookings in one place</Text>
              </div>
              <div className="flex flex-wrap gap-2">
                <Select
                  placeholder="Filter by status"
                  style={{ width: 150 }}
                  allowClear
                  onChange={handleStatusChange}
                  suffixIcon={<FilterOutlined />}
                  className="rounded-md"
                >
                  <Option value="pending">Pending</Option>
                  <Option value="approved">Approved</Option>
                  <Option value="rejected">Rejected</Option>
                  <Option value="cancelled">Cancelled</Option>
                  <Option value="checked-in">Checked In</Option>
                  <Option value="checked-out">Checked Out</Option>
                  <Option value="no-show">No Show</Option>
                </Select>
                <RangePicker 
                  onChange={handleDateRangeChange}
                  style={{ width: 250 }}
                  className="rounded-md"
                />
                <Tooltip title="Reset Filters">
                  <Button 
                    type="primary" 
                    icon={<SyncOutlined />} 
                    onClick={() => {
                      setFilters({ status: '', dateRange: null });
                      fetchBookings();
                    }}
                    className="rounded-md"
                  >
                    Reset
                  </Button>
                </Tooltip>
              </div>
            </div>
            
            <Table 
              columns={columns} 
              dataSource={bookings} 
              rowKey="_id"
              loading={loading}
              pagination={pagination}
              onChange={handleTableChange}
              scroll={{ x: 'max-content' }}
              className="custom-table"
              rowClassName="hover:bg-blue-50 transition-colors duration-200"
            />
          </Card>
        </TabPane>
        
        <TabPane 
          tab={
            <span>
              <DollarOutlined />
              Statistics
            </span>
          } 
          key="2"
        >
          <Card>
            <Title level={4}>Booking Statistics</Title>
            
            {statistics ? (
              <>
                <Row gutter={[16, 16]} className="mb-4">
                  <Col xs={24} sm={12} md={6}>
                    <Card>
                      <Statistic 
                        title="Total Bookings" 
                        value={statistics.total_bookings} 
                        prefix={<CalendarOutlined />} 
                      />
                    </Card>
                  </Col>
                  <Col xs={24} sm={12} md={6}>
                    <Card>
                      <Statistic 
                        title="Active Bookings" 
                        value={statistics.active_bookings} 
                        prefix={<CheckCircleOutlined />} 
                        valueStyle={{ color: '#3f8600' }}
                      />
                    </Card>
                  </Col>
                  <Col xs={24} sm={12} md={6}>
                    <Card>
                      <Statistic 
                        title="Total Revenue" 
                        value={statistics.total_revenue} 
                        prefix="$" 
                        precision={2}
                        valueStyle={{ color: '#1890ff' }}
                      />
                    </Card>
                  </Col>
                  <Col xs={24} sm={12} md={6}>
                    <Card>
                      <Statistic 
                        title="Occupancy Rate" 
                        value={statistics.occupancy_rate} 
                        suffix="%" 
                        precision={1}
                        valueStyle={{ 
                          color: parseFloat(statistics.occupancy_rate) > 70 ? '#3f8600' : '#cf1322'
                        }}
                      />
                    </Card>
                  </Col>
                </Row>
                
                <Divider>Bookings by Status</Divider>
                <Row gutter={[16, 16]}>
                  {statistics.bookings_by_status.map(item => (
                    <Col key={item._id} xs={24} sm={12} md={8} lg={6}>
                      <Card>
                        <Statistic 
                          title={`${item._id.toUpperCase()}`}
                          value={item.count}
                          valueStyle={{ 
                            color: 
                              item._id === 'approved' ? '#3f8600' : 
                              item._id === 'rejected' || item._id === 'cancelled' ? '#cf1322' : 
                              '#1890ff'
                          }}
                        />
                      </Card>
                    </Col>
                  ))}
                </Row>
                
                <Divider>Monthly Booking Trends</Divider>
                <div className="monthly-trends">
                  {/* You can add a chart here using a library like recharts */}
                  <Table 
                    dataSource={statistics.bookings_by_month}
                    columns={[
                      {
                        title: 'Month',
                        dataIndex: 'month',
                        key: 'month',
                      },
                      {
                        title: 'Bookings',
                        dataIndex: 'count',
                        key: 'count',
                      }
                    ]}
                    pagination={false}
                  />
                </div>
              </>
            ) : (
              <div className="text-center py-4">
                <Skeleton active />
              </div>
            )}
          </Card>
        </TabPane>
      </Tabs>
      
      {/* Booking Details Drawer */}
      <Drawer
        title="Booking Details"
        placement="right"
        onClose={() => setDetailsVisible(false)}
        open={detailsVisible}
        width={600}
        extra={
          <Space>
            <Button 
              key="delete" 
              type="primary" 
              danger
              icon={<DeleteOutlined />}
              onClick={() => {
                setDetailsVisible(false); // Close the details drawer first
                // Then show the SweetAlert confirmation
                handleDeleteBooking(selectedBooking);
              }}
            >
              Delete
            </Button>
          </Space>
        }
      >
        {selectedBooking && (
          <Descriptions bordered column={1}>
            <Descriptions.Item label="Booking ID">
              <Tag color="blue">{selectedBooking.booking_id}</Tag>
            </Descriptions.Item>
            <Descriptions.Item label="System ID">
              {selectedBooking._id}
            </Descriptions.Item>
            
            {/* Show different information based on booking type */}
            {selectedBooking.is_guest ? (
              <>
                <Descriptions.Item label="Guest Name">
                  {selectedBooking.guest_name || 'Guest'}
                </Descriptions.Item>
                <Descriptions.Item label="Guest Email">
                  {selectedBooking.guest_email || 'Not provided'}
                </Descriptions.Item>
                <Descriptions.Item label="Guest Phone">
                  {selectedBooking.guest_phone || 'Not provided'}
                </Descriptions.Item>
                 <Descriptions.Item label="Guest Phone">
                  {selectedBooking.guest_phone || 'Not provided'}
                </Descriptions.Item>
                 <Descriptions.Item label="Guest Address">
                  {selectedBooking.guest_address || 'Not provided'}
                </Descriptions.Item>
                <Descriptions.Item label="Booking Type">
                  <Tag color="blue">Guest Booking</Tag>
                </Descriptions.Item>
              </>
            ) : (
              <Descriptions.Item label="Guest">
                {selectedBooking.user?.fullName || selectedBooking.user?.userName || 'Unknown User'}
                {selectedBooking.user?.email && (
                  <div className="text-xs text-gray-500 mt-1">{selectedBooking.user.email}</div>
                )}
                {selectedBooking.user?.phone && (
                  <div className="text-xs text-gray-500">{selectedBooking.user.phone}</div>
                )}
              </Descriptions.Item>
            )}
            
            <Descriptions.Item label="Room">
              Room {selectedBooking.room?.room_number} (Floor {selectedBooking.room?.floor})
            </Descriptions.Item>
            <Descriptions.Item label="Room Type">
              {selectedBooking.room_type?.name}
            </Descriptions.Item>
            <Descriptions.Item label="Booking Dates">
              {selectedBooking.booking_dates && Array.isArray(selectedBooking.booking_dates) 
                ? selectedBooking.booking_dates.map((date, index) => {
                    // Check if date is valid before formatting
                    const dateObj = new Date(date);
                    return dateObj instanceof Date && !isNaN(dateObj) 
                      ? (index > 0 ? ' to ' : '') + format(dateObj, 'MMM dd, yyyy')
                      : 'Invalid date';
                  })
                : 'No dates available'}
            </Descriptions.Item>
            <Descriptions.Item label="Status">
              <Tag color={
                selectedBooking.booking_status === 'approved' ? 'green' :
                selectedBooking.booking_status === 'rejected' || selectedBooking.booking_status === 'cancelled' ? 'red' :
                selectedBooking.booking_status === 'checked-in' ? 'purple' :
                selectedBooking.booking_status === 'checked-out' ? 'cyan' :
                selectedBooking.booking_status === 'no-show' ? 'orange' : 'blue'
              }>
                {selectedBooking.booking_status.toUpperCase()}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="Payment Status">
              <Tag color={
                selectedBooking.payment?.status === 'completed' ? 'green' :
                selectedBooking.payment?.status === 'pending' ? 'orange' :
                selectedBooking.payment?.status === 'refunded' ? 'blue' :
                selectedBooking.payment?.status === 'failed' ? 'red' :
                selectedBooking.payment?.status === 'partially_refunded' ? 'purple' : 'default'
              }>
                {(selectedBooking.payment?.status || 'NOT RECORDED').toUpperCase()}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label="Payment Amount">
              {selectedBooking.payment?.currency || 'INR'} {selectedBooking.payment?.amount?.toFixed(2) || 0}
            </Descriptions.Item>
            <Descriptions.Item label="Payment Method">
              {selectedBooking.payment?.method 
                ? selectedBooking.payment.method.replace(/_/g, ' ').toUpperCase() 
                : 'N/A'}
            </Descriptions.Item>
            <Descriptions.Item label="Advance Amount">
              {selectedBooking.payment?.advance_amount?.toFixed(2) || 0}
            </Descriptions.Item>
            <Descriptions.Item label="Balance Amount">
              {selectedBooking.payment?.balance_amount?.toFixed(2) || 0}
            </Descriptions.Item>
            {selectedBooking.payment?.transaction_id && (
              <Descriptions.Item label="Transaction ID">
                {selectedBooking.payment.transaction_id}
              </Descriptions.Item>
            )}
            {selectedBooking.payment?.paid_at && (
              <Descriptions.Item label="Paid At">
                {(() => {
                  const paidDate = new Date(selectedBooking.payment.paid_at);
                  return paidDate instanceof Date && !isNaN(paidDate)
                    ? format(paidDate, 'MMM dd, yyyy HH:mm')
                    : 'Invalid date';
                })()}
              </Descriptions.Item>
            )}
            {selectedBooking.payment?.notes && (
              <Descriptions.Item label="Payment Notes">
                {selectedBooking.payment.notes}
              </Descriptions.Item>
            )}
            <Descriptions.Item label="Number of Guests">
              {typeof selectedBooking.guests === 'object' 
                ? `${selectedBooking.guests.adults || 0} Adults, ${selectedBooking.guests.children || 0} Children`
                : (typeof selectedBooking.guests === 'number' 
                    ? selectedBooking.guests 
                    : '0')}
            </Descriptions.Item>
            <Descriptions.Item label="Special Requests">
              {selectedBooking.special_requests || 'None'}
            </Descriptions.Item>
            <Descriptions.Item label="Check-in Time">
              {selectedBooking.check_in_time || '14:00'}
              {selectedBooking.check_in && selectedBooking.check_in.time && (
                <div>Actual: {selectedBooking.check_in.time}</div>
              )}
            </Descriptions.Item>
            <Descriptions.Item label="Check-out Time">
              {selectedBooking.check_out_time || '12:00'}
              {selectedBooking.check_out && selectedBooking.check_out.time && (
                <div>Actual: {selectedBooking.check_out.time}</div>
              )}
            </Descriptions.Item>
            {selectedBooking.check_in && selectedBooking.check_in.date && (
              <Descriptions.Item label="Check-in">
                {(() => {
                  const checkInDate = new Date(selectedBooking.check_in.date);
                  return checkInDate instanceof Date && !isNaN(checkInDate)
                    ? format(checkInDate, 'MMM dd, yyyy HH:mm')
                    : 'Invalid date';
                })()}
                {selectedBooking.check_in.processed_by && (
                  <div>By: {selectedBooking.check_in.processed_by.fullName}</div>
                )}
              </Descriptions.Item>
            )}
            {selectedBooking.check_out && selectedBooking.check_out.date && (
              <Descriptions.Item label="Check-out">
                {(() => {
                  const checkOutDate = new Date(selectedBooking.check_out.date);
                  return checkOutDate instanceof Date && !isNaN(checkOutDate)
                    ? format(checkOutDate, 'MMM dd, yyyy HH:mm')
                    : 'Invalid date';
                })()}
                {selectedBooking.check_out.processed_by && (
                  <div>By: {selectedBooking.check_out.processed_by.fullName}</div>
                )}
              </Descriptions.Item>
            )}
            {selectedBooking.cancellation && selectedBooking.cancellation.date && (
              <Descriptions.Item label="Cancellation">
                <div>Date: {(() => {
                  const cancellationDate = new Date(selectedBooking.cancellation.date);
                  return cancellationDate instanceof Date && !isNaN(cancellationDate)
                    ? format(cancellationDate, 'MMM dd, yyyy')
                    : 'Invalid date';
                })()}</div>
                <div>Reason: {selectedBooking.cancellation.reason || 'Not provided'}</div>
                {selectedBooking.cancellation.fee > 0 && (
                  <div>Fee: ${selectedBooking.cancellation.fee}</div>
                )}
                {selectedBooking.cancellation.refund_amount > 0 && (
                  <div>Refund: ${selectedBooking.cancellation.refund_amount}</div>
                )}
              </Descriptions.Item>
            )}
            <Descriptions.Item label="Created At">
              {(() => {
                const createdDate = new Date(selectedBooking.createdAt);
                return createdDate instanceof Date && !isNaN(createdDate)
                  ? format(createdDate, 'MMM dd, yyyy HH:mm')
                  : 'Invalid date';
              })()}
            </Descriptions.Item>
            <Descriptions.Item label="Updated At">
              {(() => {
                const updatedDate = new Date(selectedBooking.updatedAt);
                return updatedDate instanceof Date && !isNaN(updatedDate)
                  ? format(updatedDate, 'MMM dd, yyyy HH:mm')
                  : 'Invalid date';
              })()}
            </Descriptions.Item>
            {/* Add payment history section to booking details */}
            {selectedBooking.payment?.payment_history && selectedBooking.payment.payment_history.length > 0 && (
              <>
                <Divider orientation="left">Payment History</Divider>
                <Table
                  size="small"
                  pagination={false}
                  dataSource={selectedBooking.payment.payment_history.map((payment, index) => ({
                    ...payment,
                    key: index
                  }))}
                  columns={[
                    {
                      title: 'Date',
                      dataIndex: 'paid_at',
                      key: 'paid_at',
                      render: (text) => text ? format(new Date(text), 'MMM dd, yyyy HH:mm') : 'N/A'
                    },
                    {
                      title: 'Type',
                      dataIndex: 'type',
                      key: 'type',
                      render: (text) => (
                        <Tag color={
                          text === 'advance' ? 'blue' :
                          text === 'balance' ? 'green' :
                          text === 'full' ? 'purple' :
                          text === 'refund' ? 'red' : 'default'
                        }>
                          {text.toUpperCase()}
                        </Tag>
                      )
                    },
                    {
                      title: 'Amount',
                      dataIndex: 'amount',
                      key: 'amount',
                      render: (text) => `${selectedBooking.payment.currency} ${Number(text).toFixed(2)}`
                    },
                    {
                      title: 'Method',
                      dataIndex: 'method',
                      key: 'method',
                      render: (text) => text.replace(/_/g, ' ').toUpperCase()
                    },
                    {
                      title: 'Transaction ID',
                      dataIndex: 'transaction_id',
                      key: 'transaction_id'
                    },
                    {
                      title: 'Notes',
                      dataIndex: 'notes',
                      key: 'notes'
                    }
                  ]}
                />
              </>
            )}
          </Descriptions>
        )}
      </Drawer>
      
      {/* Status Update Drawer */}
      <Drawer
        title="Update Booking Status"
        placement="right"
        onClose={() => setStatusModalVisible(false)}
        open={statusModalVisible}
        width={400}
      >
        <Form
          form={statusForm}
          layout="vertical"
          onFinish={handleStatusUpdate}
        >
          <Form.Item
            name="status"
            label="Status"
            rules={[{ required: true, message: 'Please select a status' }]}
          >
            <Select>
              <Option value="pending">Pending</Option>
              <Option value="approved">Approved</Option>
              <Option value="rejected">Rejected</Option>
              <Option value="cancelled">Cancelled</Option>
              <Option value="checked-in">Checked In</Option>
              <Option value="checked-out">Checked Out</Option>
              <Option value="no-show">No Show</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="notes"
            label="Notes"
          >
            <Input.TextArea rows={4} />
          </Form.Item>
          
          <Form.Item>
            <Button type="primary" htmlType="submit" loading={loading}>
              Update Status
            </Button>
          </Form.Item>
        </Form>
      </Drawer>

      {/* Payment Modal */}
      <Drawer
        title="Record Payment"
        placement="right"
        onClose={() => setPaymentModalVisible(false)}
        open={paymentModalVisible}
        width={500}
      >
        {selectedBooking?.payment && (
          <Alert
            message="Payment Summary"
            description={
              <div>
                <p><strong>Total Amount:</strong> {selectedBooking.payment.currency} {selectedBooking.payment.amount?.toFixed(2)}</p>
                <p><strong>Already Paid:</strong> {selectedBooking.payment.currency} {selectedBooking.payment.advance_amount?.toFixed(2)}</p>
                <p><strong>Remaining Balance:</strong> {selectedBooking.payment.currency} {selectedBooking.payment.balance_amount?.toFixed(2)}</p>
                <p><strong>Current Status:</strong> {selectedBooking.payment.status?.toUpperCase()}</p>
              </div>
            }
            type="info"
            showIcon
            style={{ marginBottom: 16 }}
          />
        )}

        <Form
          form={paymentForm}
          layout="vertical"
          onFinish={handlePaymentRecord}
        >
          <Form.Item
            name="method"
            label="Payment Method"
            rules={[{ required: true, message: 'Please select payment method' }]}
          >
            <Select>
              <Option value="credit_card">Credit Card</Option>
              <Option value="debit_card">Debit Card</Option>
              <Option value="upi">UPI</Option>
              <Option value="bank_transfer">Bank Transfer</Option>
              <Option value="cash">Cash</Option>
              <Option value="paypal">PayPal</Option>
              <Option value="wallet">Digital Wallet</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="amount"
            label="Payment Amount"
            rules={[{ required: true, message: 'Please enter amount' }]}
            extra="Enter the amount being paid now"
          >
            <InputNumber 
              style={{ width: '100%' }}
              formatter={value => `₹ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => value.replace(/₹\s?|(,*)/g, '')}
              min={0}
              max={selectedBooking?.payment?.balance_amount || 0}
              onChange={handleAmountChange}
            />
          </Form.Item>
          
          <Form.Item
            name="advance_amount"
            label="Already Paid Amount"
            extra="This is the amount already paid previously"
          >
            <InputNumber 
              style={{ width: '100%' }}
              formatter={value => `₹ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => value.replace(/₹\s?|(,*)/g, '')}
              disabled
            />
          </Form.Item>

          <Form.Item
            name="balance_amount"
            label="Remaining Balance"
            extra="This is the amount that will remain after this payment"
          >
            <InputNumber 
              style={{ width: '100%' }}
              formatter={value => `₹ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => value.replace(/₹\s?|(,*)/g, '')}
              disabled
            />
          </Form.Item>
          
          <Form.Item
            name="transaction_id"
            label="Transaction ID"
          >
            <Input />
          </Form.Item>
          
          <Form.Item
            name="currency"
            label="Currency"
            initialValue="INR"
          >
            <Select>
              <Option value="INR">Indian Rupee (INR)</Option>
              <Option value="USD">US Dollar (USD)</Option>
              <Option value="EUR">Euro (EUR)</Option>
              <Option value="GBP">British Pound (GBP)</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="notes"
            label="Payment Notes"
          >
            <Input.TextArea rows={3} />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                Record Payment
              </Button>
              <Button onClick={() => setPaymentModalVisible(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Drawer>

      {/* Balance Payment Modal */}
      <Drawer
        title="Record Balance Payment"
        placement="right"
        onClose={() => setBalancePaymentModalVisible(false)}
        open={balancePaymentModalVisible}
        width={400}
      >
        <Alert
          message="Balance Payment Required"
          description={`A balance of ${selectedBooking?.payment?.currency || 'INR'} ${selectedBooking?.payment?.balance_amount?.toFixed(2) || 0} is due. Please collect payment before check-out.`}
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />
        
        <Form
          form={balancePaymentForm}
          layout="vertical"
          onFinish={handleBalancePayment}
        >
          <Form.Item
            name="method"
            label="Payment Method"
            rules={[{ required: true, message: 'Please select payment method' }]}
          >
            <Select>
              <Option value="credit_card">Credit Card</Option>
              <Option value="debit_card">Debit Card</Option>
              <Option value="upi">UPI</Option>
              <Option value="bank_transfer">Bank Transfer</Option>
              <Option value="cash">Cash</Option>
              <Option value="paypal">PayPal</Option>
              <Option value="wallet">Digital Wallet</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="amount"
            label="Balance Amount"
            rules={[{ required: true, message: 'Please enter amount' }]}
          >
            <InputNumber 
              style={{ width: '100%' }}
              formatter={value => `₹ ${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')}
              parser={value => value.replace(/₹\s?|(,*)/g, '')}
              disabled
            />
          </Form.Item>
          
          <Form.Item
            name="transaction_id"
            label="Transaction ID"
          >
            <Input />
          </Form.Item>
          
          <Form.Item
            name="currency"
            label="Currency"
            initialValue="INR"
          >
            <Select disabled>
              <Option value="INR">Indian Rupee (INR)</Option>
              <Option value="USD">US Dollar (USD)</Option>
              <Option value="EUR">Euro (EUR)</Option>
              <Option value="GBP">British Pound (GBP)</Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="notes"
            label="Payment Notes"
          >
            <Input.TextArea rows={3} />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                Record Payment & Check Out
              </Button>
              <Button onClick={() => setBalancePaymentModalVisible(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Drawer>
    </div>
  );
};

export default BookingManagement;

// Add this function to handle amount changes
const handleAmountChange = (value) => {
  const totalAmount = selectedBooking?.payment?.amount || 0;
  const paidAmount = selectedBooking?.payment?.advance_amount || 0;
  const newPaymentAmount = Number(value) || 0;
  
  // Calculate new remaining balance
  const newRemainingBalance = Math.max(0, totalAmount - paidAmount - newPaymentAmount);
  
  // Update the balance amount field
  paymentForm.setFieldsValue({
    balance_amount: newRemainingBalance
  });
  
  // Update status based on whether payment will be completed
  if (newRemainingBalance === 0) {
    paymentForm.setFieldsValue({
      status: 'completed'
    });
  } else {
    paymentForm.setFieldsValue({
      status: 'partially_paid'
    });
  }
};
