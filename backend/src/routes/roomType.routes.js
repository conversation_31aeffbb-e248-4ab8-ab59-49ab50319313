import express from 'express';
import { isAuthenticatedUser, isAdmin } from '../middleware/app.authentication.js';
import {
  createRoomType,
  updateRoomType,
  getRoomTypeList,
  getRoomType,
  deleteRoomType
} from '../controllers/roomType.controllers.js';

const router = express.Router();

// Room type routes
router.post('/room-types', isAuthenticatedUser, isAdmin, createRoomType);
router.put('/room-types/:id', isAuthenticatedUser, isAdmin, updateRoomType);
router.get('/room-types', getRoomTypeList);
router.get('/room-types/:id', getRoomType);
router.delete('/room-types/:id', isAuthenticatedUser, isAdmin, deleteRoomType);

export default router;
