import React, { useState } from 'react';
import { Card, Statistic, Select } from 'antd';
import { Calendar, Clock, CheckCircle, XCircle, Activity } from 'react-feather';
import { 
  AreaChart, Area, 
  LineChart, Line,
  PieChart, Pie, Cell,
  XAxis, YAxis, CartesianGrid, 
  Tooltip, Responsive<PERSON><PERSON><PERSON>,
  Legend
} from 'recharts';
import { motion, AnimatePresence } from 'framer-motion';

function BookingCard({ loading, data }) {
  const [selectedChart, setSelectedChart] = useState('area');
  const [hoveredStat, setHoveredStat] = useState(null);

  const formatter = (value) => <span>{value}</span>;

  const stats = [
    {
      title: 'Total Bookings',
      value: data?.bookings?.total || 0,
      icon: <Calendar size={20} />,
      color: 'indigo',
      key: 'total'
    },
    {
      title: 'Active Bookings',
      value: data?.active_bookings || 0,
      icon: <Activity size={20} />,
      color: 'cyan',
      key: 'active'
    },
    {
      title: 'Checked In',
      value: data?.bookings?.checked_in || 0,
      icon: <CheckCircle size={20} />,
      color: 'green',
      key: 'checkedIn'
    },
    {
      title: 'Cancelled',
      value: data?.bookings?.cancelled || 0,
      icon: <XCircle size={20} />,
      color: 'red',
      key: 'cancelled'
    }
  ];

  const COLORS = ['#4f46e5', '#0891b2', '#16a34a', '#dc2626'];

  const renderChart = () => {
    switch (selectedChart) {
      case 'area':
        return (
          <AreaChart
            data={data?.bookings?.trends || []}
            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
          >
            <defs>
              <linearGradient id="colorBookings" x1="0" y1="0" x2="0" y2="1">
                <stop offset="5%" stopColor="#4f46e5" stopOpacity={0.8}/>
                <stop offset="95%" stopColor="#4f46e5" stopOpacity={0}/>
              </linearGradient>
            </defs>
            <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip 
              contentStyle={{ 
                background: 'rgba(255, 255, 255, 0.95)',
                borderRadius: '8px',
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
              }}
            />
            <Area 
              type="monotone" 
              dataKey="value" 
              stroke="#4f46e5" 
              fillOpacity={1} 
              fill="url(#colorBookings)"
            />
          </AreaChart>
        );

      case 'line':
        return (
          <LineChart
            data={data?.bookings?.trends || []}
            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
            <XAxis dataKey="name" />
            <YAxis />
            <Tooltip 
              contentStyle={{ 
                background: 'rgba(255, 255, 255, 0.95)',
                borderRadius: '8px',
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
              }}
            />
            <Line 
              type="monotone" 
              dataKey="value" 
              stroke="#4f46e5"
              strokeWidth={2}
              dot={{ r: 4 }}
              activeDot={{ r: 8 }}
            />
          </LineChart>
        );

      case 'pie':
        return (
          <PieChart>
            <Pie
              data={stats}
              cx="50%"
              cy="50%"
              innerRadius={60}
              outerRadius={80}
              paddingAngle={5}
              dataKey="value"
            >
              {stats.map((entry, index) => (
                <Cell key={entry.key} fill={COLORS[index]} />
              ))}
            </Pie>
            <Tooltip 
              contentStyle={{ 
                background: 'rgba(255, 255, 255, 0.95)',
                borderRadius: '8px',
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
              }}
            />
            <Legend />
          </PieChart>
        );

      default:
        return null;
    }
  };

  return (
    <Card
      title={
        <div className='flex items-center justify-between px-6 py-4'>
          <div className='flex items-center space-x-3'>
            <Calendar className='text-indigo-600' />
            <span className='text-lg font-semibold'>Bookings Overview</span>
          </div>
          <Select
            defaultValue="area"
            onChange={setSelectedChart}
            className='w-32'
            options={[
              { value: 'area', label: 'Area Chart' },
              { value: 'line', label: 'Line Chart' },
              { value: 'pie', label: 'Pie Chart' },
            ]}
          />
        </div>
      }
      loading={loading}
      className='shadow-lg hover:shadow-xl transition-all duration-300'
      bodyStyle={{ padding: 0 }}
    >
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6 p-6'>
        {/* Stats Section */}
        <div className='space-y-4'>
          <div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
            {stats.map((stat) => (
              <motion.div
                key={stat.key}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onHoverStart={() => setHoveredStat(stat.key)}
                onHoverEnd={() => setHoveredStat(null)}
              >
                <Card 
                  className={`bg-gradient-to-r from-${stat.color}-50 to-${stat.color}-100 
                    shadow-md hover:shadow-lg transition-all duration-300 border-none
                    ${hoveredStat === stat.key ? 'ring-2 ring-' + stat.color + '-400' : ''}`}
                >
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className='px-4 py-3'
                  >
                    <Statistic
                      title={
                        <div className='flex items-center space-x-2 text-gray-600 font-medium mb-2'>
                          <span className={`text-${stat.color}-600`}>{stat.icon}</span>
                          <span>{stat.title}</span>
                        </div>
                      }
                      value={stat.value}
                      formatter={formatter}
                      valueStyle={{ 
                        color: `var(--${stat.color}-600)`, 
                        fontSize: '24px', 
                        fontWeight: '600' 
                      }}
                    />
                  </motion.div>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Chart Section */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
          className='bg-white rounded-lg shadow-md p-4'
        >
          <AnimatePresence mode='wait'>
            <motion.div
              key={selectedChart}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.3 }}
              className='h-[300px]'
            >
              <ResponsiveContainer width="100%" height="100%">
                {renderChart()}
              </ResponsiveContainer>
            </motion.div>
          </AnimatePresence>
        </motion.div>
      </div>
    </Card>
  );
}

export default BookingCard;
