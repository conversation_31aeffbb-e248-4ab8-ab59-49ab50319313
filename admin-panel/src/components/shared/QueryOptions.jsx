import React from 'react';
import { Input, Select } from 'antd';
import { SearchOutlined } from '@ant-design/icons';

function QueryOptions({ query, setQuery, searchPlaceholder = 'Search...' }) {
  return (
    <div className='flex flex-col sm:flex-row gap-4 mb-4'>
      <Input
        className='w-full sm:w-[240px]'
        placeholder={searchPlaceholder}
        prefix={<SearchOutlined />}
        onChange={(e) => setQuery((prevState) => ({ ...prevState, search: e.target.value }))}
        value={query.search}
        size='large'
        allowClear
      />

      <Select
        className='w-full sm:w-[240px]'
        onChange={(value) => setQuery((prevState) => ({ ...prevState, sortOrder: value }))}
        placeholder='-- select sort order --'
        value={query.sortOrder || query.sort} // Support both sortOrder and legacy sort
        size='large'
      >
        <Select.Option value='asc'>Sort By Ascending Order</Select.Option>
        <Select.Option value='desc'>Sort By Descending Order</Select.Option>
      </Select>
    </div>
  );
}

export default React.memo(QueryOptions);
