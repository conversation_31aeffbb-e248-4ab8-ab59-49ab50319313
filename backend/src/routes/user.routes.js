import express from 'express';
import {
  getUser,
  getUserById,
  updateUser,
  deleteUser,
  deleteUserById,
  avatarUpdate,
  getUsersList,
  blockedUser,
  unblockedUser
} from '../controllers/user.controllers.js';
import { 
  isAuthenticatedUser, 
  isAdmin, 
  isBlocked 
} from '../middleware/app.authentication.js';
import { avatarUpload } from '../middleware/user.avatar.upload.js';

const router = express.Router();

// User profile routes
router.get('/get-user', isAuthenticatedUser, isBlocked, getUser);
router.put('/update-user', isAuthenticatedUser, isBlocked, updateUser);
router.delete('/delete-user', isAuthenticatedUser, isBlocked, deleteUser);
router.put(
  '/avatar-update', 
  isAuthenticatedUser, 
  isBlocked, 
  avatarUpload.single('avatar'), 
  avatarUpdate
);

// Admin routes for user management
router.get('/all-users-list', isAuthenticatedUser, isBlocked, isAdmin, getUsersList);
router.get('/get-user/:id', isAuthenticatedUser, isBlocked, isAdmin, getUserById);
router.delete('/delete-user/:id', isAuthenticatedUser, isBlocked, isAdmin, deleteUserById);
router.put('/blocked-user/:id', isAuthenticatedUser, isBlocked, isAdmin, blockedUser);
router.put('/unblocked-user/:id', isAuthenticatedUser, isBlocked, isAdmin, unblockedUser);

export default router;
