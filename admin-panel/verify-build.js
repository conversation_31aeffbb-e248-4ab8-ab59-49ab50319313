const fs = require('fs');
const path = require('path');

// Verify build directory
const buildPath = path.join(__dirname, 'build');
const indexPath = path.join(buildPath, 'index.html');
const mainJsPath = path.join(__dirname, 'main.js');
const preloadJsPath = path.join(__dirname, 'preload.js');

console.log('Verifying build files...');
console.log('Build directory exists:', fs.existsSync(buildPath));
console.log('Index file exists:', fs.existsSync(indexPath));
console.log('main.js exists:', fs.existsSync(mainJsPath));
console.log('preload.js exists:', fs.existsSync(preloadJsPath));

// Check if assets directory exists
const assetsPath = path.join(__dirname, 'assets');
const iconsPath = path.join(assetsPath, 'icons');
const iconPath = path.join(iconsPath, 'icon.ico');

console.log('Assets directory exists:', fs.existsSync(assetsPath));
console.log('Icons directory exists:', fs.existsSync(iconsPath));
console.log('Icon file exists:', fs.existsSync(iconPath));

if (!fs.existsSync(buildPath) || !fs.existsSync(indexPath)) {
  console.error('ERROR: Build directory or index.html not found. Run "npm run build" first.');
  process.exit(1);
}

if (!fs.existsSync(mainJsPath) || !fs.existsSync(preloadJsPath)) {
  console.error('ERROR: main.js or preload.js not found.');
  process.exit(1);
}

if (!fs.existsSync(iconPath)) {
  console.warn('WARNING: Icon file not found. The app will use default Electron icon.');
}

console.log('Build verification completed successfully!');
