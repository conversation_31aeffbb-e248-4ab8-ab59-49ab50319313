
import express from 'express';
const router = express.Router();
import { avatarUpload } from '../middleware/user.avatar.upload.js';
import { apiLimiter } from '../middleware/access.limiter.js';
import { isAuthenticatedUser, isBlocked, isRefreshTokenValid } from '../middleware/app.authentication.js';
import { register, loginUser, logoutUser, forgotPassword, resetPassword, changePassword, sendEmailVerificationLink, emailVerification, refreshToken } from '../controllers/auth.controllers.js';

// routes for register, login and logout user
router.route('/auth/registration').post(avatarUpload.single('avatar'), register);
router.route('/auth/login').post(apiLimiter, avatarUpload.none(), loginUser);
router.route('/auth/logout').post(isAuthenticatedUser, isBlocked, logoutUser);

// routes for forgot & change password
router.route('/auth/forgot-password').post(forgotPassword);
router.route('/auth/reset-password/:token').post(resetPassword);
router.route('/auth/change-password').post(isAuthenticatedUser, isBlocked, changePassword);

// routes for user email verification
router.route('/auth/send-email-verification-link').post(isAuthenticatedUser, isBlocked, sendEmailVerificationLink);
router.route('/auth/verify-email/:token').post(isAuthenticatedUser, isBlocked, emailVerification);

// route for get user refresh JWT Token
router.route('/auth/refresh-token').post(refreshToken);

export default router;
