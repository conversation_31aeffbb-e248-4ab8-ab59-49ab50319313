export const userStatusAsResponse = (status) => {
  if (status === 'register') {
    return {
      color: '#108ee9',
      level: 'REGISTER'
    };
  }
  if (status === 'login') {
    return {
      color: '#87d068',
      level: 'LOGIN'
    };
  }
  if (status === 'logout') {
    return {
      color: '#2db7f5',
      level: 'LOGOUT'
    };
  }
  if (status === 'blocked') {
    return {
      color: '#f55000',
      level: 'BLOCKED'
    };
  }
  if (status === 'active') {
    return {
      color: '#52c41a',
      level: 'ACTIVE'
    };
  }
  if (status === 'inactive') {
    return {
      color: '#faad14',
      level: 'INACTIVE'
    };
  }
  if (status === 'verified') {
    return {
      color: '#52c41a',
      level: 'VERIFIED'
    };
  }
  if (status === 'unverified') {
    return {
      color: '#f5222d',
      level: 'UNVERIFIED'
    };
  }
  if (status === 'admin') {
    return {
      color: '#722ed1',
      level: 'ADMIN'
    };
  }
  if (status === 'user') {
    return {
      color: '#1890ff',
      level: 'USER'
    };
  }
  if (status === 'guest') {
    return {
      color: '#d48806',
      level: 'GUEST'
    };
  }

  return {
    color: 'default',
    level: 'UNKNOWN'
  };
};

export const roomStatusAsResponse = (status) => {
  if (status === 'available') {
    return {
      color: '#87d068',
      level: 'AVAILABLE'
    };
  }
  if (status === 'unavailable') {
    return {
      color: '#f55000',
      level: 'UNAVAILABLE'
    };
  }
  if (status === 'booked') {
    return {
      color: '#2db7f5',
      level: 'BOOKED'
    };
  }
  if (status === 'maintenance') {
    return {
      color: '#faad14',
      level: 'MAINTENANCE'
    };
  }
  if (status === 'cleaning') {
    return {
      color: '#722ed1',
      level: 'CLEANING'
    };
  }
  if (status === 'out_of_service') {
    return {
      color: '#ff4d4f',
      level: 'OUT OF SERVICE'
    };
  }
  if (status === 'operational') {
    return {
      color: '#52c41a',
      level: 'OPERATIONAL'
    };
  }
  if (status === 'checked-in') {
    return {
      color: '#1890ff',
      level: 'CHECKED IN'
    };
  }
  if (status === 'checked-out') {
    return {
      color: '#d48806',
      level: 'CHECKED OUT'
    };
  }
  return {
    color: 'default',
    level: 'UNKNOWN'
  };
};

export const roomTypeAsColor = (type) => {
  if (type === 'single') {
    return 'purple';
  }
  if (type === 'couple') {
    return 'magenta';
  }
  if (type === 'family') {
    return 'volcano';
  }
  if (type === 'presidential') {
    return 'geekblue';
  }
  if (type === 'dorm') {xw
    return 'green';
  }
  return 'default';
};

export const bookingStatusAsResponse = (status) => {
  if (status === 'pending') {
    return {
      color: 'blue',
      level: 'PENDING'
    };
  }
  if (status === 'cancelled') {
    return {
      color: 'volcano',
      level: 'CANCELLED'
    };
  }
  if (status === 'approved') {
    return {
      color: 'lime',
      level: 'APPROVED'
    };
  }
  if (status === 'rejected') {
    return {
      color: 'red',
      level: 'REJECTED'
    };
  }
  if (status === 'in-reviews') {
    return {
      color: 'purple',
      level: 'IN REVIEWS'
    };
  }
  if (status === 'completed') {
    return {
      color: 'green',
      level: 'COMPLETED'
    };
  }
  if (status === 'checked-in') {
    return {
      color: 'cyan',
      level: 'CHECKED IN'
    };
  }
  if (status === 'checked-out') {
    return {
      color: 'orange',
      level: 'CHECKED OUT'
    };
  }
  return {
    color: 'default',
    level: 'UNKNOWN'
  };
};

export const getRoomStatusColor = (status) => {
  const statusColor = {
    'available': '#52c41a',     // green
    'occupied': '#1890ff',      // blue
    'reserved': '#722ed1',      // purple
    'cleaning': '#faad14',      // gold
    'maintenance': '#fa8c16',   // orange
    'out_of_order': '#f5222d',  // red
    'blocked': '#cf1322',       // dark red
    'inspection': '#13c2c2'     // cyan
  };

  return statusColor[status?.toLowerCase()] || '#d9d9d9'; // default gray
};

export const formatRoomStatus = (status) => {
  if (!status) return 'UNKNOWN';
  
  // Convert snake_case to Title Case
  return status
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(' ');
};

export const roomStatusOptions = [
  { value: 'available', label: 'Available', color: '#52c41a' },
  { value: 'occupied', label: 'Occupied', color: '#1890ff' },
  { value: 'reserved', label: 'Reserved', color: '#722ed1' },
  { value: 'cleaning', label: 'Cleaning', color: '#faad14' },
  { value: 'maintenance', label: 'Maintenance', color: '#fa8c16' },
  { value: 'out_of_order', label: 'Out of Order', color: '#f5222d' },
  { value: 'blocked', label: 'Blocked', color: '#cf1322' },
  { value: 'inspection', label: 'Inspection', color: '#13c2c2' }
];
