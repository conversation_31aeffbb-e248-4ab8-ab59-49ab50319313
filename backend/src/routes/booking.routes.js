import express from 'express';
import { isAuthenticatedUser, isAdmin } from '../middleware/app.authentication.js';
import {
  createBooking,
  getAllBookings,
  getBookingById,
  updateBookingStatus,
  updatePayment,
  getBookingStatistics,
  getUserBookings,
  deleteBooking
} from '../controllers/booking.controllers.js';

const router = express.Router();

// User routes
router.post('/bookings', isAuthenticatedUser, createBooking);
router.get('/user/bookings', isAuthenticatedUser, getUserBookings);

// Admin routes
router.post('/admin/bookings', isAuthenticatedUser, isAdmin, createBooking);
router.get('/bookings', isAuthenticatedUser, isAdmin, getAllBookings);
router.get('/bookings/:id', isAuthenticatedUser, isAdmin, getBookingById);
router.put('/bookings/:id/status', isAuthenticatedUser, isAdmin, updateBookingStatus);
router.put('/bookings/:id/payment', isAuthenticatedUser, isAdmin, updatePayment);
router.get('/booking-statistics', isAuthenticatedUser, isAdmin, getBookingStatistics);
router.delete('/admin/bookings/:id', isAuthenticatedUser, isAdmin, deleteBooking);

export default router;
