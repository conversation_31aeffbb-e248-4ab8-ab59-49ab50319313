import React, { useState, useEffect } from 'react';
import { Table, Tag, Button, Space, Tooltip, Empty, Skeleton, Result, Pagination, Input, Select, Modal } from 'antd';
import { EyeOutlined, EditOutlined, FileTextOutlined, BarChartOutlined, SearchOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { Link } from 'react-router-dom';
import useFetchData from '../../hooks/useFetchData';
import ApiService from '../../utils/apiService';
import notificationWithIcon from '../../utils/notification';
import RoomStatusUpdateModal from '../shared/RoomStatusUpdateModal';
import BookingBillModal from '../shared/BookingBillModal';

const { Option } = Select;

const Orders = () => {
  const [query, setQuery] = useState({
    page: 1,
    rows: 10,
    search: '',
    sort: 'createdAt',
    status: ''
  });
  const [fetchAgain, setFetchAgain] = useState(false);
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [statusModalVisible, setStatusModalVisible] = useState(false);
  const [billModalVisible, setBillModalVisible] = useState(false);
  const [reportModalVisible, setReportModalVisible] = useState(false);

  // Fetch bookings with the updated API endpoint
  const [loading, error, response] = useFetchData(`/api/v1/bookings?keyword=${query.search}&limit=${query.rows}&page=${query.page}&sort=${query.sort}${query.status ? `&status=${query.status}` : ''}`, fetchAgain);

  // Debug logs
  useEffect(() => {
    console.log('API Response:', response);
    console.log('Bookings data:', response?.data?.bookings);
    console.log('Error:', error);
  }, [response, error]);

  const handleRefresh = () => {
    setFetchAgain(!fetchAgain);
  };

  const handleStatusUpdate = (booking) => {
    setSelectedBooking(booking);
    setStatusModalVisible(true);
  };

  const handleGenerateBill = (booking) => {
    setSelectedBooking(booking);
    setBillModalVisible(true);
  };

  const handleGenerateReport = (booking) => {
    setSelectedBooking(booking);
    setReportModalVisible(true);
  };

  const getCustomerInfo = (booking) => {
    if (booking.is_guest) {
      return {
        name: booking.guest_name || 'Guest',
        email: booking.guest_email || 'N/A',
        phone: booking.guest_phone || 'N/A'
      };
    }
    
    // Handle populated user reference
    if (booking.user) {
      return {
        name: booking.user.fullName || booking.user.userName || 'N/A',
        email: booking.user.email || 'N/A',
        phone: booking.user.phone || 'N/A'
      };
    }
    
    // Handle non-populated user_id reference
    return {
      name: booking.user_id?.fullName || booking.user_id?.userName || 'N/A',
      email: booking.user_id?.email || 'N/A',
      phone: booking.user_id?.phone || 'N/A'
    };
  };

  const getStatusTag = (status) => {
    const statusColors = {
      pending: 'orange',
      approved: 'green',
      rejected: 'red',
      cancelled: 'red',
      'checked-in': 'blue',
      'checked-out': 'purple',
      'no-show': 'gray'
    };

    return (
      <Tag color={statusColors[status] || 'default'}>
        {status?.toUpperCase() || 'N/A'}
      </Tag>
    );
  };

  const getPaymentStatusTag = (status) => {
    const statusColors = {
      pending: 'orange',
      completed: 'green',
      refunded: 'blue',
      failed: 'red'
    };

    return (
      <Tag color={statusColors[status] || 'default'}>
        {status?.toUpperCase() || 'N/A'}
      </Tag>
    );
  };

  const formatBookingDates = (dates) => {
    if (!dates || !Array.isArray(dates) || dates.length === 0) return 'N/A';
    
    if (dates.length === 1) {
      return dayjs(dates[0]).format('MMM D, YYYY');
    }
    
    const startDate = dayjs(dates[0]).format('MMM D, YYYY');
    const endDate = dayjs(dates[dates.length - 1]).format('MMM D, YYYY');
    return `${startDate} - ${endDate}`;
  };

  // Generate a simple booking report
  const generateBookingReport = (booking) => {
    const customerInfo = getCustomerInfo(booking);
    
    return (
      <div className="p-4">
        <h2 className="text-xl font-bold mb-4">Booking Report</h2>
        
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <h3 className="font-semibold">Booking Details</h3>
            <p>ID: {booking._id}</p>
            <p>Status: {booking.booking_status}</p>
            <p>Created: {dayjs(booking.createdAt).format('MMM D, YYYY HH:mm')}</p>
            <p>Last Updated: {dayjs(booking.updatedAt).format('MMM D, YYYY HH:mm')}</p>
          </div>
          
          <div>
            <h3 className="font-semibold">Customer Information</h3>
            <p>Name: {customerInfo.name}</p>
            <p>Email: {customerInfo.email}</p>
            <p>Phone: {customerInfo.phone}</p>
            <p>Type: {booking.is_guest ? 'Guest Booking' : 'Registered User'}</p>
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div>
            <h3 className="font-semibold">Room Information</h3>
            <p>Room Number: {booking.room?.room_number || 'N/A'}</p>
            <p>Room Type: {booking.room_type?.name || 'N/A'}</p>
            <p>Guests: {booking.guests?.adults || 0} Adults, {booking.guests?.children || 0} Children</p>
          </div>
          
          <div>
            <h3 className="font-semibold">Stay Information</h3>
            <p>Dates: {formatBookingDates(booking.booking_dates)}</p>
            <p>Check-in Time: {booking.check_in_time || '14:00'}</p>
            <p>Check-out Time: {booking.check_out_time || '12:00'}</p>
            <p>Special Requests: {booking.special_requests || 'None'}</p>
          </div>
        </div>
        
        <div>
          <h3 className="font-semibold">Payment Information</h3>
          <p>Amount: {booking.payment?.amount || 0} {booking.payment?.currency || 'INR'}</p>
          <p>Method: {booking.payment?.method?.replace('_', ' ') || 'N/A'}</p>
          <p>Status: {booking.payment?.status || 'N/A'}</p>
          <p>Transaction ID: {booking.payment?.transaction_id || 'N/A'}</p>
          <p>Paid At: {booking.payment?.paid_at ? dayjs(booking.payment.paid_at).format('MMM D, YYYY') : 'N/A'}</p>
        </div>
      </div>
    );
  };

  return (
    <div className="p-4">
      <div className="flex justify-between items-center mb-4">
        <h1 className="text-2xl font-bold">Bookings</h1>
        <div className="flex space-x-2">
          <Input
            placeholder="Search bookings..."
            prefix={<SearchOutlined />}
            onChange={(e) => setQuery({ ...query, search: e.target.value, page: 1 })}
            onPressEnter={handleRefresh}
            className="w-64"
          />
          <Select
            placeholder="Filter by status"
            allowClear
            style={{ width: 150 }}
            onChange={(value) => setQuery({ ...query, status: value || '', page: 1 })}
          >
            <Option value="pending">Pending</Option>
            <Option value="approved">Approved</Option>
            <Option value="checked-in">Checked In</Option>
            <Option value="checked-out">Checked Out</Option>
            <Option value="cancelled">Cancelled</Option>
            <Option value="rejected">Rejected</Option>
            <Option value="no-show">No Show</Option>
          </Select>
          <Button type="primary" onClick={handleRefresh}>
            Refresh
          </Button>
        </div>
      </div>

      <Skeleton loading={loading} paragraph={{ rows: 10 }} active>
        {error ? (
          <Result
            title="Failed to fetch"
            subTitle={error}
            status="error"
          />
        ) : !response || !response.data || !response.data.bookings || response.data.bookings.length === 0 ? (
          <Empty
            className="mt-10"
            description={
              <div>
                <span>No bookings found.</span>
                <pre className="mt-4 text-xs text-left bg-gray-100 p-2 rounded overflow-auto">
                  {JSON.stringify(response, null, 2)}
                </pre>
              </div>
            }
          />
        ) : (
          <div className="overflow-x-auto bg-white rounded-lg shadow">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Room</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Customer</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Booking Dates</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Payment</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {response.data.bookings.map((booking) => {
                  const customerInfo = getCustomerInfo(booking);
                  return (
                    <tr key={booking._id} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">
                          {booking.room?.room_number || 'N/A'}
                        </div>
                        <div className="text-xs text-gray-500">
                          {booking.room_type?.name || 'N/A'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900">{customerInfo.name}</div>
                        <div className="text-xs text-gray-500">{customerInfo.email}</div>
                        <div className="text-xs text-gray-500">{customerInfo.phone}</div>
                        {booking.is_guest && (
                          <Tag color="blue" className="mt-1">Guest Booking</Tag>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {formatBookingDates(booking.booking_dates)}
                        </div>
                        <div className="text-xs text-gray-500">
                          Check-in: {booking.check_in_time || '14:00'}
                        </div>
                        <div className="text-xs text-gray-500">
                          Check-out: {booking.check_out_time || '12:00'}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {getStatusTag(booking.booking_status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900">
                          {booking.payment?.amount || 0} {booking.payment?.currency || 'INR'}
                        </div>
                        <div className="text-xs text-gray-500">
                          {booking.payment?.method?.replace('_', ' ') || 'N/A'}
                        </div>
                        {getPaymentStatusTag(booking.payment?.status)}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {dayjs(booking.createdAt).format('MMM D, YYYY')}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <Space>
                          <Tooltip title="Generate Bill">
                            <Button 
                              type="default" 
                              icon={<FileTextOutlined />} 
                              size="small"
                              onClick={() => handleGenerateBill(booking)}
                            />
                          </Tooltip>
                          <Tooltip title="Generate Report">
                            <Button 
                              type="default" 
                              icon={<BarChartOutlined />} 
                              size="small"
                              onClick={() => handleGenerateReport(booking)}
                            />
                          </Tooltip>
                        
                        </Space>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        )}
      </Skeleton>

      {response?.data?.pagination?.pages > 1 && (
        <Pagination
          className="my-5 text-center"
          onChange={(page) => setQuery({ ...query, page })}
          total={response.data.pagination.total}
          current={response.data.pagination.page}
          pageSize={response.data.pagination.limit}
          showSizeChanger
          onShowSizeChange={(current, size) => setQuery({ ...query, rows: size, page: 1 })}
        />
      )}

      {selectedBooking && (
        <>
          <RoomStatusUpdateModal
            visible={statusModalVisible}
            booking={selectedBooking}
            onClose={() => {
              setStatusModalVisible(false);
              setSelectedBooking(null);
            }}
            onSuccess={() => {
              setStatusModalVisible(false);
              setSelectedBooking(null);
              handleRefresh();
            }}
          />
          
          <BookingBillModal
            visible={billModalVisible}
            booking={selectedBooking}
            onCancel={() => {
              setBillModalVisible(false);
              setSelectedBooking(null);
            }}
            onExport={() => {
              notificationWithIcon('success', 'Bill generated successfully');
              setBillModalVisible(false);
              setSelectedBooking(null);
            }}
          />
          
          <Modal
            title="Booking Report"
            open={reportModalVisible}
            onCancel={() => {
              setReportModalVisible(false);
              setSelectedBooking(null);
            }}
            width={800}
            footer={[
              <Button key="close" onClick={() => setReportModalVisible(false)}>
                Close
              </Button>,
              <Button 
                key="print" 
                type="primary" 
                onClick={() => {
                  window.print();
                }}
              >
                Print Report
              </Button>
            ]}
          >
            {selectedBooking && generateBookingReport(selectedBooking)}
          </Modal>
        </>
      )}
    </div>
  );
};

export default Orders;
