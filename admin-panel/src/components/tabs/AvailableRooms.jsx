import React, { useState, useEffect } from 'react';
import { Card, Tabs, Table, Tag, Button, Tooltip, Empty, Result, Select } from 'antd';
import { PropertySafetyOutlined, CheckCircleOutlined, FilterOutlined } from '@ant-design/icons';
import ApiService from '../../utils/apiService';
import notificationWithIcon from '../../utils/notification';

const { TabPane } = Tabs;
const { Option } = Select;

function AvailableRooms() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [rooms, setRooms] = useState({
    available: [],
    cleaning: []
  });
  const [allRooms, setAllRooms] = useState([]);
  const [roomTypes, setRoomTypes] = useState([]);
  const [selectedRoomType, setSelectedRoomType] = useState(null);

  useEffect(() => {
    fetchRooms();
    fetchRoomTypes();
  }, []);

  useEffect(() => {
    filterRoomsByType(selectedRoomType);
  }, [selectedRoomType, allRooms]);

  const fetchRoomTypes = async () => {
    try {
      const response = await ApiService.get('/api/v1/room-types');
      if (response?.result?.data) {
        setRoomTypes(response.result.data);
      }
    } catch (error) {
      notificationWithIcon('error', 'Error', 'Failed to fetch room types');
    }
  };

  const fetchRooms = async () => {
    try {
      setLoading(true);
      setError(null);
      // Request all rooms by setting a large limit
      const response = await ApiService.get('/api/v1/all-rooms-list?limit=1000');
      
      // Handle the "No rooms found" response (result_code: 4)
      if (response?.result_code === 4) {
        setRooms({
          available: [],
          cleaning: []
        });
        setAllRooms([]);
        return;
      }
      
      if (!response || !response.result || !response.result.data) {
        throw new Error('Invalid response format');
      }
      
      const fetchedRooms = response?.result?.data?.rows || [];
      setAllRooms(fetchedRooms);
      
      filterRoomsByType(selectedRoomType, fetchedRooms);
    } catch (error) {
      // Don't treat "No rooms found" as an error
      if (error.message && error.message.includes("No rooms found")) {
        setRooms({
          available: [],
          cleaning: []
        });
        setAllRooms([]);
        return;
      }
      
      setError('Failed to fetch rooms status');
      notificationWithIcon('error', 'Error', 'Failed to fetch rooms status');
    } finally {
      setLoading(false);
    }
  };

  const filterRoomsByType = (roomTypeId, roomsToFilter = allRooms) => {
    const filteredRooms = roomTypeId 
      ? roomsToFilter.filter(room => room.room_type?._id === roomTypeId)
      : roomsToFilter;
    
    setRooms({
      available: filteredRooms.filter(
        room => room.current_status === 'available' && room.today_occupancy?.status !== 'occupied'
      ),
      cleaning: filteredRooms.filter(
        room => room.current_status === 'cleaning'
      ),
    });
  };

  const handleRoomTypeChange = (value) => {
    setSelectedRoomType(value);
  };

  const handleClearFilter = () => {
    setSelectedRoomType(null);
  };

  const handleRequestCleaning = async (roomId) => {
    try {
      const response = await ApiService.put(`/api/v1/edit-room/${roomId}`, {
        current_status: 'cleaning',
        floor: String(rooms.available.find(room => room._id === roomId)?.floor || ''),
        is_active: true
      });
      
      if (response?.result_code === 0) {
        notificationWithIcon('success', 'Success', 'Room status updated to cleaning');
        fetchRooms();
      }
    } catch (error) {
      notificationWithIcon('error', 'Error', 'Failed to update room status');
    }
  };

  const handleMarkAsCleaned = async (roomId) => {
    try {
      const response = await ApiService.put(`/api/v1/edit-room/${roomId}`, {
        current_status: 'available',
        floor: String(rooms.cleaning.find(room => room._id === roomId)?.floor || ''),
        is_active: true
      });
      
      if (response?.result_code === 0) {
        notificationWithIcon('success', 'Success', 'Room marked as available');
        fetchRooms();
      }
    } catch (error) {
      notificationWithIcon('error', 'Error', 'Failed to update room status');
    }
  };

  const getStatusColor = (status) => {
    const statusColors = {
      available: 'green',
      occupied: 'blue',
      reserved: 'orange',
      cleaning: 'gold',
      maintenance: 'red',
      out_of_order: 'red',
      blocked: 'gray',
      inspection: 'purple'
    };
    return statusColors[status] || 'default';
  };

  const columns = {
    available: [
      {
        title: 'Room Number',
        dataIndex: 'room_number',
        key: 'room_number',
      },
      {
        title: 'Room Type',
        dataIndex: ['room_type', 'name'],
        key: 'room_type',
      },
      {
        title: 'Floor',
        dataIndex: 'floor',
        key: 'floor',
      },
      {
        title: 'Price/Night',
        dataIndex: ['room_type', 'base_price'],
        key: 'price',
        render: (price) => `${price} INR`,
      },
      {
        title: 'Status',
        dataIndex: 'current_status',
        key: 'status',
        render: (status) => (
          <Tag color={getStatusColor(status)}>
            {status.toUpperCase()}
          </Tag>
        ),
      },
      {
        title: 'Actions',
        key: 'actions',
        render: (_, record) => (
          <Tooltip title="Request cleaning for this room">
            <Button
              type="primary"
              onClick={() => handleRequestCleaning(record._id)}
              icon={<PropertySafetyOutlined />}
            >
              Request Cleaning
            </Button>
          </Tooltip>
        ),
      },
    ],
    cleaning: [
      {
        title: 'Room Number',
        dataIndex: 'room_number',
        key: 'room_number',
      },
      {
        title: 'Room Type',
        dataIndex: ['room_type', 'name'],
        key: 'room_type',
      },
      {
        title: 'Floor',
        dataIndex: 'floor',
        key: 'floor',
      },
      {
        title: 'Status',
        dataIndex: 'current_status',
        key: 'status',
        render: (status) => (
          <Tag color={getStatusColor(status)}>
            {status.toUpperCase()}
          </Tag>
        ),
      },
      {
        title: 'Actions',
        key: 'actions',
        render: (_, record) => (
          <Tooltip title="Mark this room as cleaned and available">
            <Button
              type="primary"
              onClick={() => handleMarkAsCleaned(record._id)}
              icon={<CheckCircleOutlined />}
            >
              Mark as Cleaned
            </Button>
          </Tooltip>
        ),
      },
    ],
  };

  // Only show error for critical errors, not for "No rooms found"
  if (error && !error.includes("No rooms found")) {
    return (
      <Card title="Room Availability Status">
        <Result
          status="error"
          title="Failed to load rooms"
          subTitle={error}
          extra={
            <Button type="primary" onClick={fetchRooms}>
              Try Again
            </Button>
          }
        />
      </Card>
    );
  }

  const renderEmptyState = (tabKey) => (
    <Empty
      image={Empty.PRESENTED_IMAGE_SIMPLE}
      description={
        tabKey === 'available' 
          ? "No available rooms found" 
          : "No rooms currently being cleaned"
      }
    >
      <Button type="primary" onClick={fetchRooms}>
        Refresh
      </Button>
    </Empty>
  );

  return (
    <Card title="Room Availability Status">
      <div style={{ marginBottom: 16 }}>
        <span style={{ marginRight: 8 }}><FilterOutlined /> Filter by Room Type:</span>
        <Select
          style={{ width: 240 }}
          placeholder="Select Room Type"
          allowClear
          onChange={handleRoomTypeChange}
          value={selectedRoomType}
        >
          {roomTypes.map(type => (
            <Option key={type._id} value={type._id}>{type.name}</Option>
          ))}
        </Select>
        {selectedRoomType && (
          <Button 
            type="link" 
            onClick={handleClearFilter}
            style={{ marginLeft: 8 }}
          >
            Clear Filter
          </Button>
        )}
      </div>
      <Tabs defaultActiveKey="1">
        <TabPane
          tab={
            <span>
              Available Rooms
              {rooms.available.length > 0 && (
                <Tag className="ml-2" color="green">
                  {rooms.available.length}
                </Tag>
              )}
            </span>
          }
          key="1"
        >
          <Table
            loading={loading}
            dataSource={rooms.available}
            columns={columns.available}
            rowKey="_id"
            pagination={{ pageSize: 10 }}
            locale={{
              emptyText: renderEmptyState('available')
            }}
          />
        </TabPane>
        <TabPane
          tab={
            <span>
              Rooms Under Cleaning
              {rooms.cleaning.length > 0 && (
                <Tag className="ml-2" color="gold">
                  {rooms.cleaning.length}
                </Tag>
              )}
            </span>
          }
          key="2"
        >
          <Table
            loading={loading}
            dataSource={rooms.cleaning}
            columns={columns.cleaning}
            rowKey="_id"
            pagination={{ pageSize: 10 }}
            locale={{
              emptyText: renderEmptyState('cleaning')
            }}
          />
        </TabPane>
      </Tabs>
    </Card>
  );
}

export default AvailableRooms;
