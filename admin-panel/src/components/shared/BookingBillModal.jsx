import React, { useState, useEffect } from 'react';
import { Modal, Form, Input, Button, InputNumber, Table, Typography, Select, Divider, Empty } from 'antd';
import dayjs from 'dayjs';
import { PDFDownloadLink } from '@react-pdf/renderer';
import BillPDF from './BillPDF';

const { Text } = Typography;
const { Option } = Select;

const BookingBillModal = ({ booking, visible, onCancel, onExport }) => {
  const [form] = Form.useForm();
  const [extraCharges, setExtraCharges] = useState([]);
  const [serviceCharge, setServiceCharge] = useState(0);
  const [basePrice, setBasePrice] = useState(booking?.payment?.amount || 0);
  const [discounts, setDiscounts] = useState([]);
  
  useEffect(() => {
    if (booking?.payment?.amount !== undefined) {
      setBasePrice(booking.payment.amount);
    } else {
      // Set a default value if payment is missing
      setBasePrice(0);
    }
  }, [booking]);

  const handleAddExtraCharge = () => {
    const values = form.getFieldsValue();
    if (values.description && values.amount) {
      setExtraCharges([...extraCharges, {
        description: values.description,
        amount: values.amount,
        id: Date.now(),
        type: 'charge'
      }]);
      form.setFieldsValue({ description: '', amount: 0 });
    }
  };

  const handleAddDiscount = () => {
    const values = form.getFieldsValue();
    if (values.discountDescription && values.discountAmount && values.discountType) {
      setDiscounts([...discounts, {
        description: values.discountDescription,
        amount: values.discountAmount,
        type: values.discountType,
        id: Date.now()
      }]);
      form.setFieldsValue({ discountDescription: '', discountAmount: 0, discountType: 'fixed' });
    }
  };

  const handleRemoveCharge = (id) => {
    setExtraCharges(extraCharges.filter(charge => charge.id !== id));
  };

  const handleRemoveDiscount = (id) => {
    setDiscounts(discounts.filter(discount => discount.id !== id));
  };

  const calculateDiscounts = (subtotal) => {
    let remainingAmount = subtotal;
    let totalDiscount = 0;

    discounts.forEach(discount => {
      if (discount.type === 'percentage') {
        const discountAmount = (remainingAmount * discount.amount) / 100;
        totalDiscount += discountAmount;
        remainingAmount -= discountAmount;
      } else {
        totalDiscount += discount.amount;
        remainingAmount -= discount.amount;
      }
    });

    return {
      totalDiscount,
      afterDiscount: Math.max(0, subtotal - totalDiscount)
    };
  };

  const calculateTotal = () => {
    // Calculate subtotal (base price + extra charges)
    const subtotal = basePrice + extraCharges.reduce((sum, charge) => sum + charge.amount, 0);
    
    // Calculate total discount
    const totalDiscount = discounts.reduce((sum, discount) => {
      if (discount.type === 'percentage') {
        return sum + (subtotal * discount.amount / 100);
      }
      return sum + discount.amount;
    }, 0);
    
    // Calculate amount after discounts
    const afterDiscount = Math.max(0, subtotal - totalDiscount);
    
    // Calculate service charge amount
    const serviceChargeAmount = afterDiscount * (serviceCharge / 100);
    
    // Calculate total
    const total = afterDiscount + serviceChargeAmount;
    
    // Get balance amount from booking if available
    const balanceAmount = booking.payment?.balance_amount || 0;
    
    return {
      basePrice,
      subtotal,
      totalDiscount,
      afterDiscount,
      serviceChargeAmount,
      total,
      balanceAmount
    };
  };

  const generateBillData = () => {
    console.log('generateBillData - booking:', booking);
    return {
      bookingId: booking._id,
      customerInfo: booking.is_guest ? {
        name: booking.guest_name,
        email: booking.guest_email,
        phone: booking.guest_phone
      } : {
        name: booking.user?.fullName,
        email: booking.user?.email,
        phone: booking.user?.phone
      },
      roomInfo: {
        number: booking.room?.room_number,
        type: booking.room_type.name
      },
      dates: {
        checkIn: booking.booking_dates[0],
        checkOut: booking.booking_dates[booking.booking_dates.length - 1]
      },
      originalPriceBreakdown: booking.price_breakdown,
      adjustedBasePrice: basePrice,
      extraCharges,
      discounts,
      serviceCharge: {
        percentage: serviceCharge,
        amount: calculateTotal().serviceChargeAmount
      },
      finalBreakdown: calculateTotal(),
      // Include payment history from the booking
      paymentHistory: booking.payment?.payment_history || [],
      // Include payment status
      paymentStatus: booking.payment?.status || 'pending',
      paymentCurrency: booking.payment?.currency || 'INR'
    };
  };

  const chargesColumns = [
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount) => `₹${amount.toFixed(2)}`
    },
    {
      title: 'Action',
      key: 'action',
      render: (_, record) => (
        <Button type="link" danger onClick={() => handleRemoveCharge(record.id)}>
          Remove
        </Button>
      )
    }
  ];

  const discountColumns = [
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount, record) => record.type === 'percentage' ? `${amount}%` : `₹${amount.toFixed(2)}`
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      render: (type) => type === 'percentage' ? 'Percentage' : 'Fixed Amount'
    },
    {
      title: 'Action',
      key: 'action',
      render: (_, record) => (
        <Button type="link" danger onClick={() => handleRemoveDiscount(record.id)}>
          Remove
        </Button>
      )
    }
  ];

  return (
    <Modal
      title="Generate Bill"
      open={visible}
      onCancel={onCancel}
      width={800}
      footer={[
        <Button key="cancel" onClick={onCancel}>
          Cancel
        </Button>,
        <PDFDownloadLink
          key="download"
          document={<BillPDF billData={generateBillData()} />}
          fileName={`bill-${booking._id}.pdf`}
          style={{ textDecoration: 'none' }}
        >
          {({ loading }) => (
            <Button type="primary" loading={loading}>
              Download PDF Bill
            </Button>
          )}
        </PDFDownloadLink>
      ]}
    >
      <div className="space-y-4">
        <div className="border p-4 rounded">
          <h3 className="font-semibold mb-2">Booking Details</h3>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Text strong>Room:</Text> {booking.room?.room_number}
            </div>
            <div>
              <Text strong>Dates:</Text> {booking.booking_dates?.map(date => 
                dayjs(date).format('MMM D, YYYY')).join(' - ')}
            </div>
          </div>
        </div>

        <div className="border p-4 rounded">
          <h3 className="font-semibold mb-2">Base Price Adjustment</h3>
          <div className="flex items-center gap-4">
            <Text>Original Base Price: ₹{booking.payment?.amount?.toFixed(2) || '0.00'}</Text>
            <InputNumber
              prefix="₹"
              min={0}
              precision={2}
              value={basePrice}
              onChange={setBasePrice}
              style={{ width: '200px' }}
            />
          </div>
        </div>

        <div className="border p-4 rounded">
          <h3 className="font-semibold mb-2">Extra Charges</h3>
          <Form form={form} layout="inline">
            <Form.Item name="description">
              <Input placeholder="Description" />
            </Form.Item>
            <Form.Item name="amount">
              <InputNumber 
                placeholder="Amount" 
                prefix="₹"
                min={0}
                precision={2}
              />
            </Form.Item>
            <Button type="primary" onClick={handleAddExtraCharge}>
              Add Charge
            </Button>
          </Form>
          
          <Table 
            columns={chargesColumns} 
            dataSource={extraCharges}
            pagination={false}
            className="mt-4"
          />
        </div>

        <div className="border p-4 rounded">
          <h3 className="font-semibold mb-2">Discounts</h3>
          <Form form={form} layout="inline">
            <Form.Item name="discountDescription">
              <Input placeholder="Description" />
            </Form.Item>
            <Form.Item name="discountAmount">
              <InputNumber 
                placeholder="Amount"
                min={0}
                precision={2}
              />
            </Form.Item>
            <Form.Item name="discountType">
              <Select style={{ width: 120 }} placeholder="Type">
                <Option value="fixed">Fixed (₹)</Option>
                <Option value="percentage">Percentage (%)</Option>
              </Select>
            </Form.Item>
            <Button type="primary" onClick={handleAddDiscount}>
              Add Discount
            </Button>
          </Form>
          
          <Table 
            columns={discountColumns} 
            dataSource={discounts}
            pagination={false}
            className="mt-4"
          />
        </div>

        <div className="border p-4 rounded">
          <h3 className="font-semibold mb-2">Service Charge</h3>
          <InputNumber
            prefix="%"
            min={0}
            max={100}
            precision={2}
            value={serviceCharge}
            onChange={setServiceCharge}
          />
        </div>

        <Divider />

        <div className="border p-4 rounded bg-gray-50">
          <h3 className="font-semibold mb-4">Bill Summary</h3>
          <div className="space-y-2">
            <div className="flex justify-between">
              <Text>Adjusted Base Price:</Text>
              <Text>₹{calculateTotal().basePrice.toFixed(2)}</Text>
            </div>
            {extraCharges.length > 0 && (
              <div className="flex justify-between">
                <Text>Extra Charges:</Text>
                <Text>₹{extraCharges.reduce((sum, charge) => sum + charge.amount, 0).toFixed(2)}</Text>
              </div>
            )}
            <div className="flex justify-between">
              <Text>Subtotal:</Text>
              <Text>₹{calculateTotal().subtotal.toFixed(2)}</Text>
            </div>
            {discounts.length > 0 && (
              <div className="flex justify-between text-red-500">
                <Text>Total Discounts:</Text>
                <Text>-₹{calculateTotal().totalDiscount.toFixed(2)}</Text>
              </div>
            )}
            <div className="flex justify-between">
              <Text>After Discounts:</Text>
              <Text>₹{calculateTotal().afterDiscount.toFixed(2)}</Text>
            </div>
            {serviceCharge > 0 && (
              <div className="flex justify-between">
                <Text>Service Charge ({serviceCharge}%):</Text>
                <Text>₹{calculateTotal().serviceChargeAmount.toFixed(2)}</Text>
              </div>
            )}
            <Divider />
            <div className="flex justify-between text-lg font-bold">
              <Text>Final Total:</Text>
              <Text>₹{calculateTotal().total.toFixed(2)}</Text>
            </div>
          </div>
        </div>

        <div className="border p-4 rounded">
          <h3 className="font-semibold mb-2">Payment History</h3>
          {booking.payment?.payment_history && booking.payment.payment_history.length > 0 ? (
            <Table 
              columns={[
                {
                  title: 'Date',
                  dataIndex: 'paid_at',
                  key: 'paid_at',
                  render: (text) => text ? dayjs(text).format('MMM D, YYYY HH:mm') : 'N/A'
                },
                {
                  title: 'Type',
                  dataIndex: 'type',
                  key: 'type',
                  render: (text) => text.charAt(0).toUpperCase() + text.slice(1)
                },
                {
                  title: 'Amount',
                  dataIndex: 'amount',
                  key: 'amount',
                  render: (text) => `${booking.payment.currency} ${Number(text).toFixed(2)}`
                },
                {
                  title: 'Method',
                  dataIndex: 'method',
                  key: 'method',
                  render: (text) => text.replace(/_/g, ' ').toUpperCase()
                },
                {
                  title: 'Transaction ID',
                  dataIndex: 'transaction_id',
                  key: 'transaction_id'
                },
                {
                  title: 'Notes',
                  dataIndex: 'notes',
                  key: 'notes'
                }
              ]} 
              dataSource={booking.payment.payment_history.map((payment, index) => ({
                ...payment,
                key: index
              }))}
              pagination={false}
              className="mt-4"
            />
          ) : (
            <Empty description="No payment history available" />
          )}
        </div>
      </div>
    </Modal>
  );
};

export default BookingBillModal;
