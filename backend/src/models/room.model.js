
import mongoose from 'mongoose';
import Booking from './booking.model.js';

const roomSchema = new mongoose.Schema({
  room_number: {
    type: String,
    required: [true, 'Room number is required'],
    unique: true,
    trim: true
  },
  floor: {
    type: Number,
    required: [true, 'Floor number is required']
  },
  room_type: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'RoomType',
    required: true
  },
  room_images: [{
    url: {
      type: String,
      required: true
    },
    is_primary: {
      type: Boolean,
      default: false
    },
    caption: {
      type: String,
      default: ''
    }
  }],
  current_status: {
    type: String,
    enum: [
      'available',      // Room is ready for booking
      'occupied',       // Currently in use by guests
      'reserved',       // Booked for future date
      'cleaning',       // Under housekeeping
      'maintenance',    // Under maintenance/repair
      'out_of_order',   // Not available for extended period
      'blocked',        // Administratively blocked
      'inspection'      // Under quality inspection
    ],
    default: 'available'
  },
  maintenance_history: [{
    start_date: {
      type: Date,
      required: [true, 'Start date is required']
    },
    end_date: {
      type: Date,
      required: [true, 'End date is required']
    },
    reason: {
      type: String,
      required: [true, 'Reason is required']
    },
    status: {
      type: String,
      enum: [
        'scheduled',
        'in_progress',
        'completed',
        'cancelled'
      ],
      default: 'scheduled'
    },
    performed_by: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    notes: String,
    created_at: {
      type: Date,
      default: Date.now
    }
  }],
  special_notes: String,
  is_active: {
    type: Boolean,
    default: true
  },
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  last_cleaned: {
    date: Date,
    by: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    notes: String
  },
  next_maintenance_date: Date,
  features: {
    has_balcony: {
      type: Boolean,
      default: false
    },
    has_sea_view: {
      type: Boolean,
      default: false
    },
    is_accessible: {
      type: Boolean,
      default: false
    },
    is_smoking: {
      type: Boolean,
      default: false
    }
  },
  location: {
    building: String,
    wing: String,
    proximity_to_elevator: {
      type: Boolean,
      default: false
    },
    proximity_to_stairs: {
      type: Boolean,
      default: false
    }
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Add pre-save middleware to validate dates
roomSchema.pre('save', function(next) {
  // Validate maintenance dates
  if (this.maintenance_history && this.maintenance_history.length > 0) {
    for (const maintenance of this.maintenance_history) {
      if (maintenance.end_date < maintenance.start_date) {
        return next(new Error('Maintenance end date cannot be before start date'));
      }
    }
  }
  
  // Set next_maintenance_date based on upcoming scheduled maintenance
  const upcomingMaintenance = this.maintenance_history
    .filter(m => m.status === 'scheduled' && m.start_date > new Date())
    .sort((a, b) => a.start_date - b.start_date)[0];
    
  if (upcomingMaintenance) {
    this.next_maintenance_date = upcomingMaintenance.start_date;
  }
  
  next();
});

// Add method to check if room needs cleaning
roomSchema.methods.needsCleaning = function() {
  // If no cleaning record exists, room needs cleaning
  if (!this.last_cleaned || !this.last_cleaned.date) {
    return true;
  }
  
  // Check if room was cleaned more than 24 hours ago
  const lastCleanedDate = new Date(this.last_cleaned.date);
  const oneDayAgo = new Date();
  oneDayAgo.setDate(oneDayAgo.getDate() - 1);
  
  return lastCleanedDate < oneDayAgo;
};

// Add method to get room occupancy statistics
roomSchema.statics.getOccupancyStats = async function(startDate, endDate) {
  const bookings = await Booking.find({
    booking_dates: {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    },
    booking_status: { $in: ['approved', 'checked-in', 'completed'] }
  }).populate('room_id');
  
  // Group bookings by room type
  const roomTypeStats = {};
  bookings.forEach(booking => {
    if (!booking.room_id) return;
    
    const roomTypeId = booking.room_id.room_type.toString();
    if (!roomTypeStats[roomTypeId]) {
      roomTypeStats[roomTypeId] = {
        totalBookings: 0,
        totalNights: 0
      };
    }
    
    roomTypeStats[roomTypeId].totalBookings++;
    roomTypeStats[roomTypeId].totalNights += booking.booking_dates.length;
  });
  
  return roomTypeStats;
};

// Virtual for upcoming bookings
roomSchema.virtual('upcoming_bookings', {
  ref: 'Booking',
  localField: '_id',
  foreignField: 'room_id',
  match: {
    booking_status: { $in: ['pending', 'approved', 'checked-in'] }
  }
});

// Method to check room availability
roomSchema.methods.isAvailableForDates = async function(requestedDates, checkInTime = "14:00", checkOutTime = "12:00") {
  if (this.current_status !== 'available' && this.current_status !== 'cleaning') {
    return false;
  }

  const dates = Array.isArray(requestedDates) ? requestedDates : [requestedDates];
  
  // Check each date for availability with the specified check-in/out times
  for (const date of dates) {
    const isAvailable = await this.isAvailableForDateAndTime(date, checkInTime, checkOutTime);
    if (!isAvailable) {
      return false;
    }
  }
  
  return true;
};

// Method to check room availability with time consideration
roomSchema.methods.isAvailableForDateAndTime = async function(date, checkInTime, checkOutTime) {
  // Room must be available or under cleaning
  if (this.current_status !== 'available' && this.current_status !== 'cleaning') {
    return false;
  }

  const checkDate = new Date(date);
  checkDate.setHours(0, 0, 0, 0); // Normalize to midnight for date-only comparison

  // Check for overlapping maintenance
  const isUnderMaintenance = this.maintenance_history.some(maintenance => {
    if (['completed', 'cancelled'].includes(maintenance.status)) return false;

    const start = new Date(maintenance.start_date);
    const end = new Date(maintenance.end_date);

    return checkDate >= start && checkDate <= end;
  });

  if (isUnderMaintenance) {
    return false;
  }

  // Check for booking conflicts at the specified date and time
  const Booking = mongoose.model('Booking');

  const hasConflict = await Booking.exists({
    room: this._id,
    booking_date: checkDate,
    $or: [
      {
        check_in_time: { $lt: checkOutTime },
        check_out_time: { $gt: checkInTime }
      }
    ]
  });

  return !hasConflict;
};

// Get all booked dates for this room
roomSchema.methods.getBookedDates = async function(startDate, endDate) {
  const query = { room_id: this._id, booking_status: { $in: ['pending', 'approved', 'checked-in'] } };
  
  if (startDate && endDate) {
    query.booking_dates = { 
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    };
  }
  
  const bookings = await Booking.find(query);
  
  // Extract all booked dates
  const bookedDates = [];
  bookings.forEach(booking => {
    booking.booking_dates.forEach(date => {
      bookedDates.push(new Date(date).toISOString().split('T')[0]);
    });
  });
  
  return [...new Set(bookedDates)]; // Remove duplicates
};

// Get room availability calendar
roomSchema.methods.getAvailabilityCalendar = async function(year, month) {
  const startDate = new Date(year, month - 1, 1);
  const endDate = new Date(year, month, 0);
  
  const bookedDates = await this.getBookedDates(startDate, endDate);
  const maintenanceDates = this.getMaintenanceDates(startDate, endDate);
  
  const calendar = [];
  let currentDate = new Date(startDate);
  
  while (currentDate <= endDate) {
    const dateString = currentDate.toISOString().split('T')[0];
    calendar.push({
      date: dateString,
      isBooked: bookedDates.includes(dateString),
      isUnderMaintenance: maintenanceDates.includes(dateString),
      isAvailable: !bookedDates.includes(dateString) && !maintenanceDates.includes(dateString) && this.current_status === 'available'
    });
    
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  return calendar;
};

// Get maintenance dates
roomSchema.methods.getMaintenanceDates = function(startDate, endDate) {
  const maintenanceDates = [];
  
  this.maintenance_history.forEach(maintenance => {
    if (maintenance.status !== 'completed' && maintenance.status !== 'cancelled') {
      let currentDate = new Date(maintenance.start_date);
      const endMaintenance = new Date(maintenance.end_date);
      
      while (currentDate <= endMaintenance) {
        if ((!startDate || currentDate >= startDate) && (!endDate || currentDate <= endDate)) {
          maintenanceDates.push(currentDate.toISOString().split('T')[0]);
        }
        currentDate.setDate(currentDate.getDate() + 1);
      }
    }
  });
  
  return [...new Set(maintenanceDates)]; // Remove duplicates
};

// Method to schedule maintenance
roomSchema.methods.scheduleMaintenance = async function(startDate, endDate, reason, performedBy) {
  // Check if room is available for these dates
  const dates = [];
  let currentDate = new Date(startDate);
  
  while (currentDate <= new Date(endDate)) {
    dates.push(new Date(currentDate));
    currentDate.setDate(currentDate.getDate() + 1);
  }
  
  const isAvailable = await this.isAvailableForDates(dates);
  
  if (!isAvailable) {
    throw new Error('Room is not available for the requested maintenance dates');
  }
  
  this.maintenance_history.push({
    start_date: startDate,
    end_date: endDate,
    reason,
    status: 'scheduled',
    performed_by: performedBy,
    notes: ''
  });
  
  await this.save();
  return this;
};

// Static method to find available rooms
roomSchema.statics.findAvailableRooms = async function(roomTypeId, requestedDates) {
  const rooms = await this.find({
    room_type: roomTypeId,
    current_status: 'available',
    is_active: true
  });

  const availableRooms = [];
  for (const room of rooms) {
    const isAvailable = await room.isAvailableForDates(requestedDates);
    if (isAvailable) {
      availableRooms.push(room);
    }
  }

  return availableRooms;
};

// Method to mark room as cleaned
roomSchema.methods.markAsCleaned = async function(cleanedBy) {
  this.last_cleaned = {
    date: new Date(),
    by: cleanedBy
  };
  
  if (this.current_status === 'cleaning') {
    this.current_status = 'available';
  }
  
  await this.save();
  return this;
};

// Add method to check if room is due for maintenance
roomSchema.methods.isDueForMaintenance = function() {
  if (!this.next_maintenance_date) {
    return false;
  }
  
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  
  const maintenanceDate = new Date(this.next_maintenance_date);
  maintenanceDate.setHours(0, 0, 0, 0);
  
  return maintenanceDate <= today;
};

// Add method to get room revenue
roomSchema.methods.getRevenue = async function(startDate, endDate) {
  const bookings = await Booking.find({
    room_id: this._id,
    booking_status: { $in: ['completed', 'checked-in'] },
    createdAt: {
      $gte: new Date(startDate),
      $lte: new Date(endDate)
    }
  });
  
  return bookings.reduce((total, booking) => total + booking.total_amount, 0);
};

// Indexes for faster queries
roomSchema.index({ room_number: 1 });
roomSchema.index({ floor: 1 });
roomSchema.index({ room_type: 1 });
roomSchema.index({ current_status: 1 });
roomSchema.index({ is_active: 1 });
roomSchema.index({ 'features.has_sea_view': 1 });
roomSchema.index({ 'features.is_accessible': 1 });

const Room = mongoose.model('Room', roomSchema);

export default Room;
