import React from 'react';
import { Tag, Tooltip } from 'antd';
import { getRoomStatusColor, formatRoomStatus } from '../../utils/responseAsStatus';

const RoomStatus = ({ status }) => {
  const getStatusIcon = (status) => {
    switch (status) {
      case 'available':
        return '✓';
      case 'occupied':
        return '⌂';
      case 'reserved':
        return '⌚';
      case 'cleaning':
        return '🧹';
      case 'maintenance':
        return '🔧';
      case 'out_of_order':
        return '⚠';
      case 'blocked':
        return '⛔';
      case 'inspection':
        return '🔍';
      default:
        return '?';
    }
  };

  return (
    <Tooltip title={formatRoomStatus(status)}>
      <Tag color={getRoomStatusColor(status)}>
        {getStatusIcon(status)} {formatRoomStatus(status)}
      </Tag>
    </Tooltip>
  );
};

export default RoomStatus;