import React from 'react';
import { Document, Page, Text, View, StyleSheet } from '@react-pdf/renderer';
import dayjs from 'dayjs';
import { Image } from '@react-pdf/renderer';
import logo from '../../assets/images/ttsresidency.png';

const styles = StyleSheet.create({
  page: {
    padding: 40,
    fontFamily: 'Helvetica',
    backgroundColor: '#FFFFFF',
    fontSize: 10,
  },
  header: {
    flexDirection: 'row',
    marginBottom: 20,
    alignItems: 'center',
  },
  logo: {
    width: 100,
    height: 75,
    marginRight: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  logoText: {
    fontSize: 8,
    textAlign: 'center',
  },
  companyContainer: {
    flex: 1,
  },
  companyName: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 8,
    letterSpacing: 0.5,
    color: '#1e293b',
  },
  companyInfo: {
    fontSize: 10,
    color: '#4b5563', // Modern gray color
    marginBottom: 3,
    letterSpacing: 0.2,
  },
  companyContact: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginTop: 5,
  },
  contactItem: {
    marginRight: 15,
    fontSize: 9,
    color: '#6b7280', // Lighter gray for contact info
  },
  divider: {
    borderBottom: '1 solid #e5e7eb', // Lighter border color
    marginTop: 10,
    marginBottom: 25,
  },
  title: {
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 20,
    color: '#1e293b', // Dark slate color for headings
  },
  invoiceInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 15,
  },
  guestInfo: {
    marginBottom: 20,
  },
  guestInfoItem: {
    marginBottom: 5,
  },
  table: {
    display: 'table',
    width: 'auto',
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    marginBottom: 20,
    borderRadius: 4, // Adding subtle rounded corners
  },
  tableRow: {
    flexDirection: 'row',
  },
  tableHeader: {
    backgroundColor: '#f3f4f6', // Lighter background for header
  },
  tableHeaderCell: {
    padding: 8,
    fontWeight: 'bold',
    borderStyle: 'solid',
    borderBottomWidth: 1,
    borderRightWidth: 1,
    borderColor: '#e5e7eb',
    color: '#4b5563', // Modern gray for header text
  },
  tableCell: {
    padding: 8,
    borderStyle: 'solid',
    borderBottomWidth: 1,
    borderRightWidth: 1,
    borderColor: '#e5e7eb',
  },
  descriptionCol: {
    width: '40%',
  },
  quantityCol: {
    width: '20%',
  },
  rateCol: {
    width: '20%',
  },
  subtotalCol: {
    width: '20%',
  },
  summaryText: {
    marginTop: 5,
    marginBottom: 5,
  },
  paymentDetails: {
    marginTop: 15,
    marginBottom: 15,
  },
  termsTitle: {
    fontWeight: 'bold',
    marginBottom: 5,
    color: '#4b5563',
  },
  termsList: {
    marginLeft: 15,
  },
  bulletPoint: {
    marginBottom: 3,
  },
  footer: {
    marginTop: 20,
    fontSize: 9,
    color: '#6b7280',
    textAlign: 'center',
  },
  // Payment history styles
  paymentHistoryTitle: {
    fontWeight: 'bold',
    marginTop: 15,
    marginBottom: 5,
    color: '#4b5563',
  },
  paymentHistoryTable: {
    display: 'table',
    width: 'auto',
    borderStyle: 'solid',
    borderWidth: 1,
    borderColor: '#e5e7eb',
    marginBottom: 15,
    marginTop: 5,
    borderRadius: 4,
  },
  paymentHistoryRow: {
    flexDirection: 'row',
  },
  paymentHistoryHeader: {
    backgroundColor: '#f3f4f6',
  },
  paymentHistoryHeaderCell: {
    padding: 6,
    fontWeight: 'bold',
    borderStyle: 'solid',
    borderBottomWidth: 1,
    borderRightWidth: 1,
    borderColor: '#e5e7eb',
    color: '#4b5563',
    fontSize: 9,
  },
  paymentHistoryCell: {
    padding: 6,
    borderStyle: 'solid',
    borderBottomWidth: 1,
    borderRightWidth: 1,
    borderColor: '#e5e7eb',
    fontSize: 9,
  },
  dateCol: {
    width: '25%',
  },
  typeCol: {
    width: '20%',
  },
  amountCol: {
    width: '20%',
  },
  methodCol: {
    width: '20%',
  },
  notesCol: {
    width: '15%',
  },
});

const BillPDF = ({ billData }) => {
  // Calculate nights
  const nights = dayjs(billData.dates.checkOut).diff(dayjs(billData.dates.checkIn), 'day');

  // Calculate nightly rate
  const nightlyRate = nights > 0 ? (billData.adjustedBasePrice / nights) : billData.adjustedBasePrice;

  // Format payment method for display
  const formatPaymentMethod = (method) => {
    return method ? method.replace(/_/g, ' ').toUpperCase() : 'N/A';
  };

  // Format payment type for display
  const formatPaymentType = (type) => {
    switch(type) {
      case 'advance': return 'ADVANCE';
      case 'balance': return 'BALANCE';
      case 'full': return 'FULL PAYMENT';
      case 'partial': return 'PARTIAL';
      case 'refund': return 'REFUND';
      default: return type ? type.toUpperCase() : 'N/A';
    }
  };

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        <View style={styles.header}>
            <Image src={logo} style={styles.logo} />
          <View style={styles.companyContainer}>
            <Text style={styles.companyName}>TTS NeXus</Text>
            <Text style={styles.companyInfo}>
              <EMAIL> | +91 9361 496 966 | ttsresidency.com
            </Text>
            <Text style={styles.companyInfo}>
              TTS Nexus Complex,
              Kaliyal Junction,
              Arumanai Road,
              Kaliyal P.O,
              Pin : 629101,
              Kanyakumari District
            </Text>
          </View>
        </View>

        {/* Divider Line */}
        <View style={styles.divider} />

        {/* Bill Title */}
        <Text style={styles.title}>Hotel Invoice</Text>

        {/* Invoice Info */}
        <View style={styles.invoiceInfo}>
          <Text>Invoice No.: {billData.bookingId}</Text>
          <Text>Date: {dayjs().format('MMMM D, YYYY')}</Text>
        </View>

        {/* Guest Information */}
        <View style={styles.guestInfo}>
          <View style={styles.guestInfoItem}>
            <Text>Guest Name: {billData.customerInfo.name}</Text>
          </View>
          <View style={styles.guestInfoItem}>
            <Text>Guest Address: {billData.customerInfo.address || 'N/A'}</Text>
          </View>
          <View style={styles.guestInfoItem}>
            <Text>Guest Contact: {billData.customerInfo.phone || billData.customerInfo.email}</Text>
          </View>
        </View>

        {/* Services Table */}
        <View style={styles.table}>
          {/* Table Header */}
          <View style={[styles.tableRow, styles.tableHeader]}>
            <View style={[styles.tableHeaderCell, styles.descriptionCol]}>
              <Text>Service Description</Text>
            </View>
            <View style={[styles.tableHeaderCell, styles.quantityCol]}>
              <Text>Quantity</Text>
            </View>
            <View style={[styles.tableHeaderCell, styles.rateCol]}>
              <Text>Rate</Text>
            </View>
            <View style={[styles.tableHeaderCell, styles.subtotalCol]}>
              <Text>Subtotal</Text>
            </View>
          </View>

          {/* Room Charge Row */}
          <View style={styles.tableRow}>
            <View style={[styles.tableCell, styles.descriptionCol]}>
              <Text>Room Charge ({billData.roomInfo.type})</Text>
            </View>
            <View style={[styles.tableCell, styles.quantityCol]}>
              <Text>{nights} Nights</Text>
            </View>
            <View style={[styles.tableCell, styles.rateCol]}>
              <Text>₹{nightlyRate.toFixed(2)}</Text>
            </View>
            <View style={[styles.tableCell, styles.subtotalCol]}>
              <Text>₹{billData.adjustedBasePrice.toFixed(2)}</Text>
            </View>
          </View>

          {/* Extra Charges Rows */}
          {billData.extraCharges.map((charge, index) => (
            <View style={styles.tableRow} key={index}>
              <View style={[styles.tableCell, styles.descriptionCol]}>
                <Text>{charge.description}</Text>
              </View>
              <View style={[styles.tableCell, styles.quantityCol]}>
                <Text>{charge.quantity || '1'}</Text>
              </View>
              <View style={[styles.tableCell, styles.rateCol]}>
                <Text>₹{(charge.rate || (charge.amount / (charge.quantity || 1))).toFixed(2)}</Text>
              </View>
              <View style={[styles.tableCell, styles.subtotalCol]}>
                <Text>₹{charge.amount.toFixed(2)}</Text>
              </View>
            </View>
          ))}
        </View>

        {/* Summary Section - Simplified like in the image */}
        <View>
          <Text style={styles.summaryText}>Subtotal: ₹{billData.finalBreakdown.subtotal.toFixed(2)}</Text>

          {billData.discounts.length > 0 && (
            <Text style={styles.summaryText}>Discounts: -₹{billData.finalBreakdown.totalDiscount.toFixed(2)}</Text>
          )}
          {
            billData.finalBreakdown.serviceChargeAmount > 0 && (
              <Text style={styles.summaryText}>Service Charge: ₹{billData.finalBreakdown.serviceChargeAmount.toFixed(2)}</Text>
            )}

          <Text style={[styles.summaryText, { fontWeight: 'bold' }]}>
            Total Amount Due: ₹{billData.finalBreakdown.total.toFixed(2)}
          </Text>
        </View>

        {/* Payment Status */}
        <View style={{ marginTop: 10, marginBottom: 5 }}>
          <Text style={[styles.summaryText, { fontWeight: 'bold' }]}>
            Payment Status: {billData.paymentStatus ? billData.paymentStatus.toUpperCase() : 'PENDING'}
          </Text>
          {billData.paymentStatus === 'partially_paid' && (
            <Text style={styles.summaryText}>
              Amount Paid: ₹{(billData.finalBreakdown.total - billData.finalBreakdown.balanceAmount).toFixed(2)}
            </Text>
          )}
          {billData.paymentStatus === 'partially_paid' && (
            <Text style={styles.summaryText}>
              Balance Due: ₹{billData.finalBreakdown.balanceAmount.toFixed(2)}
            </Text>
          )}
        </View>

        {/* Payment Details */}
        <View style={styles.paymentDetails}>
          <Text style={styles.termsTitle}>Payment Details:</Text>
          <Text>
            Payment accepted via UPI, Bank Transfer, or Cash. Please settle all payments upon check-out.
          </Text>
        </View>

        {/* Payment History Section */}
        {billData.paymentHistory && billData.paymentHistory.length > 0 && (
          <View>
            <Text style={styles.paymentHistoryTitle}>Payment History:</Text>
            <View style={styles.paymentHistoryTable}>
              {/* Table Header */}
              <View style={[styles.paymentHistoryRow, styles.paymentHistoryHeader]}>
                <View style={[styles.paymentHistoryHeaderCell, styles.dateCol]}>
                  <Text>Date</Text>
                </View>
                <View style={[styles.paymentHistoryHeaderCell, styles.typeCol]}>
                  <Text>Type</Text>
                </View>
                <View style={[styles.paymentHistoryHeaderCell, styles.amountCol]}>
                  <Text>Amount</Text>
                </View>
                <View style={[styles.paymentHistoryHeaderCell, styles.methodCol]}>
                  <Text>Method</Text>
                </View>
                <View style={[styles.paymentHistoryHeaderCell, styles.notesCol]}>
                  <Text>Notes</Text>
                </View>
              </View>
              
              {/* Payment History Rows */}
              {billData.paymentHistory.map((payment, index) => (
                <View style={styles.paymentHistoryRow} key={index}>
                  <View style={[styles.paymentHistoryCell, styles.dateCol]}>
                    <Text>{payment.paid_at ? dayjs(payment.paid_at).format('MMM D, YYYY') : 'N/A'}</Text>
                  </View>
                  <View style={[styles.paymentHistoryCell, styles.typeCol]}>
                    <Text>{formatPaymentType(payment.type)}</Text>
                  </View>
                  <View style={[styles.paymentHistoryCell, styles.amountCol]}>
                    <Text>₹{payment.amount.toFixed(2)}</Text>
                  </View>
                  <View style={[styles.paymentHistoryCell, styles.methodCol]}>
                    <Text>{formatPaymentMethod(payment.method)}</Text>
                  </View>
                  <View style={[styles.paymentHistoryCell, styles.notesCol]}>
                    <Text>{payment.notes || '-'}</Text>
                  </View>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Terms and Conditions */}
        <View>
          <Text style={styles.termsTitle}>Terms and Conditions:</Text>
          <View style={styles.termsList}>
            <Text style={styles.bulletPoint}>• Late check-outs may incur additional charges.</Text>
            <Text style={styles.bulletPoint}>• Cancellations are subject to hotel policy.</Text>
            <Text style={styles.bulletPoint}>• No refunds on completed services.</Text>
          </View>
        </View>

        {/* Footer */}
        <View style={styles.footer}>
          <Text>For further assistance, feel free to reach <NAME_EMAIL></Text>
        </View>
      </Page>
    </Document>
  );
};

export default BillPDF;
