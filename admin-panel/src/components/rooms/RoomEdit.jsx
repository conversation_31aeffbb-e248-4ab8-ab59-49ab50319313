import { PlusOutlined } from '@ant-design/icons';
import {
  Button, Drawer, Form, Input, InputNumber, Result, Select, Upload, Switch
} from 'antd';
import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import useFetchData from '../../hooks/useFetchData';
import { reFetchData } from '../../store/slice/appSlice';
import ApiService from '../../utils/apiService';
import notificationWithIcon from '../../utils/notification';
import PageLoader from '../shared/PageLoader';
import dayjs from 'dayjs';
import { roomStatusOptions } from '../../utils/responseAsStatus';

function RoomEdit({ roomEditModal, setRoomEditModal }) {
  const [loading, setLoading] = useState(false);
  const [fileList, setFileList] = useState([]);
  const dispatch = useDispatch();
  const [form] = Form.useForm();

  // Fetch room types
  const [roomTypes, setRoomTypes] = useState([]);

  useEffect(() => {
    fetchRoomTypes();
  }, []);

  const fetchRoomTypes = async () => {
    try {
      const response = await ApiService.get('/api/v1/room-types');
      if (response?.result?.data) {
        setRoomTypes(response.result.data);
      }
    } catch (error) {
      notificationWithIcon('error', 'Error', 'Failed to fetch room types');
    }
  };

  // Fetch room data
  const [fetchLoading, fetchError, fetchResponse] = useFetchData(
    roomEditModal.roomId ? `/api/v1/get-room-by-id-or-slug-name/${roomEditModal.roomId}` : null
  );
  console.log(fetchResponse);

  useEffect(() => {
    if (fetchResponse?.data) {
      const roomData = fetchResponse.data;

      // Ensure we're handling the room_type correctly
      const roomTypeId = roomData.room_type && typeof roomData.room_type === 'object'
        ? roomData.room_type._id
        : roomData.room_type;

      const existingImages = roomData.room_images?.map((image, index) => ({
        uid: `-${index}`,
        name: `image-${index}`,
        status: 'done',
        url: image.url,
        caption: image.caption,
        is_primary: image.is_primary
      })) || [];

      setFileList(existingImages);

      form.setFieldsValue({
        room_number: roomData.room_number,
        floor: roomData.floor,
        room_type: roomTypeId,
        current_status: roomData.current_status || 'operational',
        special_notes: roomData.special_notes,
        is_active: roomData.is_active,
        maintenance_history: (roomData.maintenance_history || []).map(history => ({
          start_date: history.start_date ? dayjs(history.start_date) : null,
          end_date: history.end_date ? dayjs(history.end_date) : null,
          reason: history.reason,
          status: history.status,
          performed_by: history.performed_by && typeof history.performed_by === 'object'
            ? history.performed_by._id
            : history.performed_by,
          notes: history.notes
        }))
      });
    }
  }, [fetchResponse, form]);

  const normFile = (e) => {
    if (Array.isArray(e)) {
      return e;
    }
    return e?.fileList;
  };

  const handleImageChange = ({ fileList: newFileList }) => {
    if (newFileList.length > 5) {
      notificationWithIcon('error', 'ERROR', 'Maximum 5 images are allowed');
      return;
    }
    setFileList(newFileList);
  };

  const onFinish = async (values) => {
    if (fileList.length === 0) {
      notificationWithIcon('error', 'ERROR', 'At least one room image is required');
      return;
    }

    const formData = new FormData();

    // Append basic room information
    formData.append('room_number', values.room_number);
    formData.append('floor', Number(values.floor));
    formData.append('room_type', values.room_type);
    formData.append('current_status', values.current_status);
    formData.append('special_notes', values.special_notes);
    formData.append('is_active', String(values.is_active));

    // Handle maintenance history if exists
    if (values.maintenance_history?.length) {
      const maintenanceHistory = values.maintenance_history.map(history => ({
        start_date: history.start_date?.toISOString() || null,
        end_date: history.end_date?.toISOString() || null,
        reason: history.reason,
        status: history.status,
        performed_by: history.performed_by,
        notes: history.notes
      }));
      formData.append('maintenance_history', JSON.stringify(maintenanceHistory));
    }

    // Handle images
    fileList.forEach((file, index) => {
      if (file.originFileObj) {
        formData.append('room_images', file.originFileObj);
      }
    });
    // Handle existing images
    const existingImages = fileList
      .filter(file => !file.originFileObj)
      .map(file => ({
        url: file.url,
        is_primary: file.is_primary,
        caption: file.caption
      }));

    if (existingImages.length > 0) {
      formData.append('existing_images', JSON.stringify(existingImages));
    }

    try {
      setLoading(true);
      const response = await ApiService.put(
        `/api/v1/edit-room/${roomEditModal.roomId}`,
        formData,
        {
          headers: { 'Content-Type': 'multipart/form-data' }
        }
      );

      if (response?.result_code === 0) {
        notificationWithIcon('success', 'SUCCESS', 'Room updated successfully');
        form.resetFields();
        setFileList([]);
        dispatch(reFetchData());
        setRoomEditModal((prevState) => ({ ...prevState, open: false }));
      } else {
        throw new Error(response?.result?.error || 'Failed to update room');
      }
    } catch (error) {
      notificationWithIcon('error', 'ERROR', error.message);
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setRoomEditModal(prev => ({
      ...prev,
      open: false,
      roomId: null
    }));
    form.resetFields();
    setFileList([]);
  };

  return (
    <Drawer
      title="Edit Room Information"
      open={roomEditModal.open}
      onClose={handleCancel}
      width={1200}
      placement="right"
      extra={[
        <Button key="cancel" onClick={handleCancel}>
          Cancel
        </Button>,
        <Button 
          key="submit" 
          type="primary" 
          onClick={() => form.submit()}
          loading={loading}
        >
          Update Room
        </Button>
      ]}
    >
      {fetchLoading ? (
        <PageLoader />
      ) : fetchError ? (
        <Result
          status="error"
          title="Failed to fetch"
          subTitle={fetchError}
        />
      ) : (
        <Form
          form={form}
          name="room-edit-form"
          onFinish={onFinish}
          layout="vertical"
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Form.Item
              label="Room Number"
              name="room_number"
              rules={[{ required: true, message: 'Room number is required' }]}
            >
              <Input placeholder="Room Number" />
            </Form.Item>

            <Form.Item
              label="Floor"
              name="floor"
              rules={[{ required: true, message: 'Floor number is required' }]}
            >
              <InputNumber className="w-full" min={1} placeholder="Floor Number" />
            </Form.Item>

            <Form.Item
              label="Room Type"
              name="room_type"
              rules={[{ required: true, message: 'Room type is required' }]}
            >
              <Select placeholder="Select room type">
                {roomTypes.map(type => (
                  <Select.Option key={type._id} value={type._id}>
                    {type.name}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              label="Current Status"
              name="current_status"
              rules={[{ required: true, message: 'Please select room status' }]}
            >
              <Select>
                {roomStatusOptions.map(option => (
                  <Select.Option 
                    key={option.value} 
                    value={option.value}
                  >
                    <span style={{ color: option.color }}>●</span> {option.label}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>

            <Form.Item
              label="Special Notes"
              name="special_notes"
            >
              <Input.TextArea rows={4} placeholder="Special Notes" />
            </Form.Item>

            <Form.Item
              label="Active Status"
              name="is_active"
              valuePropName="checked"
            >
              <Switch />
            </Form.Item>

            <Form.Item
              label="Room Images"
              name="room_images"
              valuePropName="fileList"
              getValueFromEvent={normFile}
            >
              <Upload
                listType="picture-card"
                fileList={fileList}
                onChange={handleImageChange}
                beforeUpload={() => false}
              >
                <div>
                  <PlusOutlined />
                  <div style={{ marginTop: 8 }}>Upload</div>
                </div>
              </Upload>
            </Form.Item>
          </div>
        </Form>
      )}
    </Drawer>
  );
}

RoomEdit.defaultProps = {
  roomEditModal: {
    open: false,
    roomId: null
  },
  setRoomEditModal: () => {}
};

export default RoomEdit;
