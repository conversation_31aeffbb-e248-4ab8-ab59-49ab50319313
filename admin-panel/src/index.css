/* tailwind css */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* root css */
*,
*:before,
*:after {
  box-sizing: inherit;
}

html {
  font-size: 14px;
  box-sizing: border-box;
  scroll-behavior: smooth;
}

body {
  @apply !font-body-font;
  -ms-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

input,
select,
button,
textarea,
a {
  border-radius: 6 !important;
  -webkit-appearance: none;
}

/* antd customize start */

.ant-btn-primary {
  @apply text-txt-white bg-color-primary border-color-primary hover:!bg-color-secondary hover:border-color-secondary active:!bg-color-primary disabled:text-txt-grey disabled:bg-bg-gray disabled:border-bg-gray;
}

.ant-divider-horizontal.ant-divider-with-text-center::before,
.ant-divider-horizontal.ant-divider-with-text-center::after {
  border-top: 2px solid !important;
  @apply border-color-primary text-center;
}

.ant-message-custom-content {
  @apply inline-flex items-center;
}

.ant-popover-title {
  @apply bg-bg-black;
}

.ant-image-preview-img {
  margin: auto !important;
}

.ant-image-preview-img-wrapper img {
  display: inline !important;
}

.ant-layout-header {
  padding-inline: 0 !important;
}

.ant-tabs-tab {
  @apply !text-[12px] font-bold md:!text-[20px];
}

.ant-picker-calendar-date-content {
  @apply !h-[25px];
}
.ant-picker-content thead tr {
  @apply !h-[40px] border-b-bg-black border-b-2;
}

/* antd customize end */

/* app components style start */

.page-loader {
  @apply w-full h-[50vh] flex items-center justify-center z-z-fixed;
}

.default-transition {
  @apply transition-all duration-200;
}

.two-grid-column {
  @apply flex flex-col items-center justify-center md:flex-row md:justify-between md:space-x-4;
}

/* app components style end */

/* data table style start */

.table-layout {
  @apply w-full shadow bg-bg-white rounded my-2;
}
.table-layout-container {
  @apply border-t-2 border-l-2 border-gray-200 w-full rounded bg-bg-white overflow-x-auto;
}

.data-table {
  @apply w-full leading-normal font-text-font border-spacing-0 border-collapse overflow-hidden;
}
.data-table-head {
  background: rgba(205, 205, 205, 0.11);
  @apply tracking-wider text-left px-5 py-3 border-b-2 border-gray-200 hover:cursor-default;
}
.data-table-head-tr {
  @apply border-b-2 border-gray-200;
}
.data-table-head-tr-th {
  @apply border-r-2 py-3 px-3 text-[12px] text-gray-600 font-body-font font-bold uppercase tracking-wider whitespace-nowrap;
}
.data-table-body-tr {
  @apply hover:bg-gray-100 hover:cursor-default;
}
.data-table-body-tr-td {
  @apply py-3 px-3 border-b-2 border-r-2 border-gray-200 text-gray-900 text-[12px] font-body-font font-medium capitalize whitespace-nowrap;
}

/* data table style end */

/* dashboard components style start */

.logo-box {
  @apply px-2 py-4 flex flex-row items-center justify-start space-x-2 cursor-pointer;
}

.user-name {
  @apply whitespace-nowrap text-txt-white text-[25px] font-text-font font-medium truncate block;
}

/* dashboard components style end */

/* Modern Sidebar Styles */
.ant-layout-sider {
  @apply transition-all duration-300 ease-in-out;
}

.ant-menu-item {
  @apply my-1 rounded-lg !important;
}

.ant-menu-item-selected {
  @apply !bg-blue-100 !text-blue-600 !important;
}

.ant-menu-item:hover {
  @apply !text-blue-600 !important;
}

.ant-menu-item-active {
  @apply !bg-blue-50 !important;
}

/* Modern Header Styles */
.ant-layout-header {
  @apply !h-16 !leading-[4rem];
}

/* Header Button Styles */
.ant-layout-header .ant-btn {
  @apply text-gray-300;
}

.ant-layout-header .ant-btn:hover {
  @apply text-white;
}

/* Header Dropdown Styles */
.ant-layout-header .ant-dropdown-trigger {
  @apply text-gray-300 hover:text-white transition-colors duration-200;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .ant-layout-header {
    @apply !px-4;
  }
}

/* Content Area Styles */
.ant-layout-content {
  @apply !bg-gray-50;
}

/* Scrollbar Styles */
.ant-layout-content::-webkit-scrollbar {
  width: 8px;
}

.ant-layout-content::-webkit-scrollbar-track {
  @apply bg-transparent;
}

.ant-layout-content::-webkit-scrollbar-thumb {
  @apply bg-gray-300 rounded-full;
}

.ant-layout-content::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400;
}

/* Modern Calendar Styles */
.calendar-container {
  @apply p-6;
}

.calendar-container .fc {
  @apply font-body-font bg-white rounded-2xl shadow-lg overflow-hidden;
}

/* Header Styles */
.calendar-container .fc-header-toolbar {
  @apply mb-6 px-6 py-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-t-2xl;
}

.calendar-container .fc-toolbar-title {
  @apply text-2xl font-bold text-white capitalize;
}

/* Button Styles */
.calendar-container .fc-button-primary {
  @apply bg-white/20 border-0 text-white px-4 py-2 rounded-full transition-all
         hover:bg-white/30 hover:shadow-lg transform hover:-translate-y-0.5
         focus:ring-2 focus:ring-white/50 focus:outline-none;
}

.calendar-container .fc-button-primary:not(:disabled).fc-button-active {
  @apply bg-white/40 shadow-inner;
}

/* Day Header Styles */
.calendar-container .fc-col-header {
  @apply bg-gray-50;
}

.calendar-container .fc-col-header-cell {
  @apply py-4;
}

.calendar-container .fc-col-header-cell-cushion {
  @apply text-sm font-semibold text-gray-700 uppercase tracking-wider;
}

/* Day Cell Styles */
.calendar-container .fc-daygrid-day {
  @apply transition-colors duration-200;
}

.calendar-container .fc-daygrid-day-frame {
  @apply min-h-[120px] hover:bg-blue-50/50 transition-colors p-2;
}

.calendar-container .fc-daygrid-day-top {
  @apply flex justify-center p-2;
}

.calendar-container .fc-daygrid-day-number {
  @apply flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium
         hover:bg-blue-100 hover:text-blue-600 transition-colors;
}

/* Today Styles */
.calendar-container .fc-day-today {
  @apply bg-blue-50/70 !important;
}

.calendar-container .fc-day-today .fc-daygrid-day-number {
  @apply bg-blue-500 text-white;
}

/* Event Styles */
.calendar-container .fc-event {
  @apply rounded-xl border-none shadow-md transition-all duration-200
         hover:shadow-lg hover:-translate-y-0.5 cursor-pointer;
}

.calendar-container .fc-event-main {
  @apply p-2;
}

.calendar-container .fc-event-main-frame {
  @apply space-y-1;
}

.calendar-container .fc-event-time {
  @apply text-xs font-medium opacity-90;
}

.calendar-container .fc-event-title {
  @apply text-sm font-medium line-clamp-2;
}

/* More Link Styles */
.calendar-container .fc-more-link {
  @apply text-xs font-medium text-blue-600 hover:text-blue-700
         bg-blue-100 rounded-full px-3 py-1 transition-colors;
}

/* Time Grid Styles */
.calendar-container .fc-timegrid-slot-label {
  @apply text-sm text-gray-500 font-medium;
}

.calendar-container .fc-timegrid-axis {
  @apply text-sm text-gray-400 font-medium;
}

/* Week/Day View Styles */
.calendar-container .fc-timegrid-event {
  @apply rounded-lg shadow-md border-none;
}

/* Modal Styles */
.booking-details-modal .ant-modal-content {
  @apply rounded-2xl shadow-2xl border-0;
}

.booking-details-modal .ant-modal-header {
  @apply p-6 border-b border-gray-100;
}

.booking-details-modal .ant-modal-body {
  @apply p-6;
}

.booking-details-modal .ant-descriptions {
  @apply rounded-xl overflow-hidden border border-gray-100;
}

.booking-details-modal .ant-descriptions-item-label {
  @apply bg-gray-50 text-gray-700 px-6 py-4;
}

.booking-details-modal .ant-descriptions-item-content {
  @apply bg-white px-6 py-4;
}

/* Loading State */
.calendar-loading {
  @apply flex flex-col items-center justify-center h-[600px] bg-gray-50 rounded-2xl
         space-y-4 animate-pulse;
}

/* Update the button text styles */
.calendar-container .fc-button {
  @apply capitalize;
}

/* Update the view titles */
.calendar-container .fc-view-harness {
  @apply capitalize;
}

/* Add these styles to your CSS file */
.ant-table-cell .ant-image {
  transition: all 0.3s ease;
}

.ant-table-cell .ant-image:hover {
  transform: scale(1.05);
}

.ant-image-mask {
  border-radius: 4px;
}
