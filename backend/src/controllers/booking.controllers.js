
import Booking from '../models/booking.model.js';
import Room from '../models/room.model.js';
import RoomType from '../models/roomType.model.js';
import User from '../models/user.model.js';
import { successResponse, errorResponse } from '../config/app.response.js';
import logger from '../middleware/winston.logger.js';
import mongoose from 'mongoose';

/**
 * Create a new booking
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const createBooking = async (req, res) => {
  try {
    const {
      is_guest,
      guest_name,
      guest_email,
      guest_phone,
      guest_address,
      room_id,
      room_type_id,
      booking_dates,
      check_in_time,
      check_out_time,
      guests,
      special_requests,
      payment_method,
      payment
    } = req.body;

    // Check for all required fields and collect missing ones
    const requiredFields = {
      'room_id': room_id,
      'room_type_id': room_type_id,
      'booking_dates': booking_dates,
      'guests': guests
    };

    // Add guest-specific required fields if this is a guest booking
    if (is_guest) {
      requiredFields['guest_name'] = guest_name;
      requiredFields['guest_phone'] = guest_phone;
      requiredFields['guest_address'] = guest_address;
    } else {
      requiredFields['user_id'] = req.user?.id;
    }

    const missingFields = Object.entries(requiredFields)
      .filter(([_, value]) => !value)
      .map(([field]) => field);
    
    if (missingFields.length > 0) {
      return res.status(400).json(errorResponse(
        1,
        'VALIDATION_ERROR',
        `Missing required fields: ${missingFields.join(', ')}`
      ));
    }

    // Validate room exists
    const room = await Room.findById(room_id);
    if (!room) {
      return res.status(404).json(errorResponse(
        1,
        'FAILED',
        'Room not found'
      ));
    }

    // Validate room type exists
    const roomType = await RoomType.findById(room_type_id);
    if (!roomType) {
      return res.status(404).json(errorResponse(
        1,
        'FAILED',
        'Room type not found'
      ));
    }

    // Get default check-in/out times from room type if not provided
    const defaultCheckInTime = roomType.policies?.check_in_time || "14:00";
    const defaultCheckOutTime = roomType.policies?.check_out_time || "12:00";
    
    // Use provided times or defaults
    const bookingCheckInTime = check_in_time || defaultCheckInTime;
    const bookingCheckOutTime = check_out_time || defaultCheckOutTime;
    
    // Validate time format
    const timeRegex = /^([01]\d|2[0-3]):([0-5]\d)$/;
    if (!timeRegex.test(bookingCheckInTime) || !timeRegex.test(bookingCheckOutTime)) {
      return res.status(400).json(errorResponse(
        1,
        'FAILED',
        'Invalid time format. Use HH:MM (24-hour format)'
      ));
    }

    // Check for time-based availability on each requested date
    let isAvailable = true;
    for (const date of booking_dates) {
      // Find bookings for this room on the same date
      const existingBookings = await Booking.find({
        room_id,
        booking_status: { $in: ['pending', 'approved', 'checked-in'] },
        booking_dates: new Date(date)
      });

      // Check for time conflicts
      for (const existingBooking of existingBookings) {
        const existingCheckOut = existingBooking.check_out_time;
        const newCheckIn = bookingCheckInTime;
        
        // If new check-in is before existing check-out, there's a conflict
        if (compareTimeStrings(newCheckIn, existingCheckOut) < 0) {
          isAvailable = false;
          break;
        }
      }
      
      if (!isAvailable) break;
    }

    if (!isAvailable) {
      return res.status(400).json(errorResponse(
        1,
        'FAILED',
        'Room is not available for the requested dates and times'
      ));
    }

    // Prepare payment object
    let paymentData;
    if (payment) {
      // If a complete payment object is provided
      paymentData = {
        method: payment.method,
        amount: payment.amount || (roomType.pricing.daily_rate * booking_dates.length),
        currency: payment.currency || 'INR',
        status: payment.status || 'pending',
        transaction_id: payment.transaction_id || '',
        advance_amount: payment.advance_amount || 0,
        balance_amount: payment.balance_amount || 0,
        paid_at: payment.paid_at ? new Date(payment.paid_at) : undefined,
        notes: payment.notes || '',
        // Ensure payment history is included
        payment_history: payment.payment_history || []
      };
      
      // If there's an advance payment but no payment history, create one
      if (paymentData.advance_amount > 0 && (!paymentData.payment_history || paymentData.payment_history.length === 0)) {
        paymentData.payment_history = [{
          type: paymentData.advance_amount < paymentData.amount ? 'advance' : 'full',
          amount: paymentData.advance_amount,
          method: paymentData.method,
          transaction_id: paymentData.transaction_id || '',
          paid_at: paymentData.paid_at || new Date(),
          notes: 'Initial payment'
        }];
      }
    } else {
      // If only payment_method is provided
      paymentData = {
        method: payment_method,
        amount: roomType.pricing.daily_rate * booking_dates.length,
        status: 'pending',
        payment_history: []
      };
    }
    
    // Generate booking ID manually if needed
    const Counter = mongoose.model('Counter');
    const sequenceNumber = await Counter.getNextSequence('booking_id');
    const formattedNumber = String(sequenceNumber).padStart(3, '0');
    const bookingId = `TTS${formattedNumber}`;
    
    let booking;

    if(!is_guest) {
      // Create booking for registered user
      booking = new Booking({
        booking_id: bookingId, // Set booking ID explicitly
        user_id: req.user.id,
        room_id,
        room_type: room_type_id,
        booking_dates,
        check_in_time: bookingCheckInTime,
        check_out_time: bookingCheckOutTime,
        guests,
        special_requests,
        payment: paymentData
      });
    } else {
      // Create booking for guest
      booking = new Booking({
        booking_id: bookingId, // Set booking ID explicitly
        is_guest: true,
        guest_name,
        guest_email,
        guest_phone,
        guest_address,
        room_id,
        room_type: room_type_id,
        booking_dates,
        check_in_time: bookingCheckInTime,
        check_out_time: bookingCheckOutTime,
        guests,
        special_requests,
        payment: paymentData
      });
    }

    // Log the booking object before saving
    console.log('Booking object before save:', JSON.stringify({
      booking_id: booking.booking_id,
      room_id: booking.room_id,
      is_guest: booking.is_guest
    }));

    await booking.save();

    // Update room status to reserved
    room.current_status = 'reserved';
    await room.save();

    res.status(201).json(successResponse(
      0,
      'SUCCESS',
      'Booking created successfully',
      {
        ...booking.toObject(),
        booking_id: booking.booking_id // Include the custom booking ID in the response
      }
    ));
  } catch (error) {
    logger.error('Error creating booking:', error);
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

/**
 * Get all bookings with pagination and filtering
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getAllBookings = async (req, res) => {
  try {
    const { page = 1, limit = 10, status, startDate, endDate, search } = req.query;
    
    // Build query based on filters
    const query = {};
    
    if (status) {
      query.booking_status = status;
    }
    
    if (startDate && endDate) {
      query.booking_dates = {
        $elemMatch: {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        }
      };
    }
    
    if (search) {
      // Search by booking ID, guest name, or user email
      query.$or = [
        { booking_id: { $regex: search, $options: 'i' } },
        { guest_name: { $regex: search, $options: 'i' } },
        { guest_email: { $regex: search, $options: 'i' } },
        { guest_phone: { $regex: search, $options: 'i' } }
      ];
    }
    
    // Count total documents for pagination
    const total = await Booking.countDocuments(query);
    
    // Fetch bookings with pagination
    const bookings = await Booking.find(query)
      .populate('user_id', 'userName fullName email phone')
      .populate('room_id', 'room_number floor')
      .populate('room_type', 'name price_per_night')
      .sort({ createdAt: -1 })
      .skip((page - 1) * limit)
      .limit(parseInt(limit));
    
    // Format response data
    const formattedBookings = bookings.map(booking => ({
      _id: booking._id,
      booking_id: booking.booking_id,
      user: booking.user_id ? {
        _id: booking.user_id._id,
        userName: booking.user_id.userName,
        fullName: booking.user_id.fullName,
        email: booking.user_id.email,
        phone: booking.user_id.phone
      } : null,
      is_guest: booking.is_guest,
      guest_name: booking.guest_name,
      guest_email: booking.guest_email,
      guest_phone: booking.guest_phone,
      guest_address: booking.guest_address,
      room: booking.room_id ? {
        _id: booking.room_id._id,
        room_number: booking.room_id.room_number,
        floor: booking.room_id.floor
      } : null,
      room_type: booking.room_type ? {
        _id: booking.room_type._id,
        name: booking.room_type.name,
        price_per_night: booking.room_type.price_per_night
      } : null,
      booking_dates: booking.booking_dates,
      check_in_time: booking.check_in_time,
      check_out_time: booking.check_out_time,
      booking_status: booking.booking_status,
      guests: booking.guests,
      payment: booking.payment,
      createdAt: booking.createdAt,
      updatedAt: booking.updatedAt
    }));
    
    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Bookings fetched successfully',
      {
        bookings: formattedBookings,
        pagination: {
          total,
          page: parseInt(page),
          limit: parseInt(limit),
          pages: Math.ceil(total / limit)
        }
      }
    ));
  } catch (error) {
    logger.error('Error fetching bookings:', error);
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};
/**
 * Get booking by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getBookingById = async (req, res) => {
  try {
    const { id } = req.params;

    // Try to find booking by either MongoDB ID or custom booking ID
    let query = {};
    
    // Check if the ID is a valid MongoDB ObjectId
    if (mongoose.Types.ObjectId.isValid(id)) {
      query._id = id;
    } else {
      // If not a valid ObjectId, try to find by custom booking ID
      query.booking_id = id;
    }

    const booking = await Booking.findOne(query)
      .populate('user_id', 'userName fullName email phone')
      .populate('room_id', 'room_number floor')
      .populate('room_type', 'name price_per_night')
      .populate('payment.processed_by', 'userName fullName')
      .populate('check_in.processed_by', 'userName fullName')
      .populate('check_out.processed_by', 'userName fullName')
      .populate('cancellation.processed_by', 'userName fullName');

    if (!booking) {
      return res.status(404).json(errorResponse(
        1,
        'NOT FOUND',
        'Booking not found'
      ));
    }

    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Booking fetched successfully',
      booking
    ));
  } catch (error) {
    logger.error('Error fetching booking:', error);
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

/**
 * Update booking status
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const updateBookingStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { status, notes, cancellation_fee, refund_amount } = req.body;

    const booking = await Booking.findById(id)
      .populate('room_id')
      .populate('room_type');

    if (!booking) {
      return res.status(404).json(errorResponse(
        1,
        'NOT FOUND',
        'Booking not found'
      ));
    }

    // Update based on status
    switch (status) {
      case 'approved':
        await booking.approveBooking(notes);
        break;
      case 'rejected':
        await booking.rejectBooking(notes, req.user.id);
        break;
      case 'cancelled':
        await booking.cancelBooking(notes, req.user.id, cancellation_fee, refund_amount);
        break;
      case 'checked-in':
        await booking.checkIn(req.user.id, notes);
        break;
      case 'checked-out':
        await booking.checkOut(req.user.id, notes);
        break;
      case 'no-show':
        await booking.markAsNoShow(req.user.id, notes);
        break;
      default:
        return res.status(400).json(errorResponse(
          1,
          'INVALID STATUS',
          'Invalid booking status'
        ));
    }

    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      `Booking status updated to ${status}`,
      await Booking.findById(id)
        .populate('user_id', 'userName fullName email phone')
        .populate('room_id', 'room_number floor')
        .populate('room_type', 'name price_per_night')
    ));
  } catch (error) {
    logger.error('Error updating booking status:', error);
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

/**
 * Update payment for booking
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const updatePayment = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Handle both formats: nested payment object or flat properties
    const paymentData = req.body.payment || req.body;
    
    const { 
      method, 
      amount, 
      advance_amount,
      balance_amount,
      transaction_id, 
      notes, 
      currency, 
      status,
      paid_at,
      payment_history
    } = paymentData;

    // Validate required fields
    if (!method) {
      return res.status(400).json(errorResponse(
        1,
        'FAILED',
        'Payment method is required'
      ));
    }

    if (!amount) {
      return res.status(400).json(errorResponse(
        1,
        'FAILED',
        'Payment amount is required'
      ));
    }

    const booking = await Booking.findById(id);

    if (!booking) {
      return res.status(404).json(errorResponse(
        1,
        'NOT FOUND',
        'Booking not found'
      ));
    }

    // Get existing payment history or initialize empty array
    const existingPaymentHistory = booking.payment?.payment_history || [];
    
    // Create payment object with all required fields
    booking.payment = {
      method: method,
      amount: amount,
      advance_amount: advance_amount !== undefined ? advance_amount : (booking.payment?.advance_amount || 0),
      balance_amount: balance_amount !== undefined ? balance_amount : (booking.payment?.balance_amount || 0),
      currency: currency || booking.payment?.currency || 'INR',
      status: status || booking.payment?.status || 'pending',
      transaction_id: transaction_id || booking.payment?.transaction_id || '',
      paid_at: paid_at ? new Date(paid_at) : booking.payment?.paid_at,
      processed_by: req.user.id,
      notes: notes || booking.payment?.notes || '',
      // Merge existing payment history with new entries
      payment_history: payment_history ? 
        [...existingPaymentHistory, ...payment_history] : 
        existingPaymentHistory
    };

    // Log the payment object for debugging
    logger.info('Payment object before save:', JSON.stringify(booking.payment));

    // Save the booking with updated payment
    await booking.save();

    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Payment updated successfully',
      await Booking.findById(id)
        .populate('user_id', 'userName fullName email phone')
        .populate('room_id', 'room_number floor')
        .populate('room_type', 'name price_per_night')
        .populate('payment.processed_by', 'userName fullName')
    ));
  } catch (error) {
    logger.error('Error updating payment:', error);
    logger.error('Request body:', JSON.stringify(req.body));
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

/**
 * Get booking statistics
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getBookingStatistics = async (req, res) => {
  try {
    // Get total bookings
    const totalBookings = await Booking.countDocuments();

    // Get active bookings (approved, checked-in)
    const activeBookings = await Booking.countDocuments({
      booking_status: { $in: ['approved', 'checked-in'] }
    });

    // Get total revenue from completed payments
    const payments = await Booking.find({
      'payment.status': 'completed'
    });

    const totalRevenue = payments.reduce((sum, booking) => {
      return sum + (booking.payment?.amount || 0);
    }, 0);

    // Calculate occupancy rate
    const totalRooms = await Room.countDocuments();
    const occupiedRooms = await Room.countDocuments({
      current_status: { $in: ['occupied', 'reserved'] }
    });

    const occupancyRate = totalRooms > 0
      ? (occupiedRooms / totalRooms) * 100
      : 0;

    // Get bookings by status
    const bookingsByStatus = await Booking.aggregate([
      {
        $group: {
          _id: '$booking_status',
          count: { $sum: 1 }
        }
      }
    ]);

    // Get bookings by month (last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    const bookingsByMonth = await Booking.aggregate([
      {
        $match: {
          createdAt: { $gte: sixMonthsAgo }
        }
      },
      {
        $group: {
          _id: {
            year: { $year: '$createdAt' },
            month: { $month: '$createdAt' }
          },
          count: { $sum: 1 }
        }
      },
      {
        $sort: {
          '_id.year': 1,
          '_id.month': 1
        }
      }
    ]);

    // Format bookings by month
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];

    const formattedBookingsByMonth = bookingsByMonth.map(item => ({
      month: `${monthNames[item._id.month - 1]} ${item._id.year}`,
      count: item.count
    }));

    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Booking statistics retrieved successfully',
      {
        total_bookings: totalBookings,
        active_bookings: activeBookings,
        total_revenue: totalRevenue,
        occupancy_rate: occupancyRate.toFixed(2),
        bookings_by_status: bookingsByStatus,
        bookings_by_month: formattedBookingsByMonth
      }
    ));
  } catch (error) {
    logger.error('Error getting booking statistics:', error);
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

/**
 * Get user's bookings
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getUserBookings = async (req, res) => {
  try {
    const userId = req.user.id;

    const bookings = await Booking.find({ user_id: userId })
      .populate('room_id', 'room_number floor')
      .populate('room_type', 'name price_per_night')
      .sort({ createdAt: -1 });

    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'User bookings retrieved successfully',
      bookings
    ));
  } catch (error) {
    logger.error('Error getting user bookings:', error);
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

/**
 * Check in a guest
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const checkInGuest = async (req, res) => {
  try {
    const { bookingId } = req.params;
    const { notes, actual_check_in_time } = req.body;

    const booking = await Booking.findById(bookingId);
    if (!booking) {
      return res.status(404).json(errorResponse(
        1,
        'FAILED',
        'Booking not found'
      ));
    }

    await booking.checkIn(req.user.id, notes, actual_check_in_time);

    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Guest checked in successfully',
      booking
    ));
  } catch (error) {
    logger.error('Error checking in guest:', error);
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

/**
 * Check out a guest
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const checkOutGuest = async (req, res) => {
  try {
    const { bookingId } = req.params;
    const { notes, actual_check_out_time } = req.body;

    const booking = await Booking.findById(bookingId);
    if (!booking) {
      return res.status(404).json(errorResponse(
        1,
        'FAILED',
        'Booking not found'
      ));
    }

    await booking.checkOut(req.user.id, notes, actual_check_out_time);

    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Guest checked out successfully',
      booking
    ));
  } catch (error) {
    logger.error('Error checking out guest:', error);
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

/**
 * Delete booking
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const deleteBooking = async (req, res) => {
  try {
    const { id } = req.params;

    const booking = await Booking.findById(id);
    if (!booking) {
      return res.status(404).json(errorResponse(
        1,
        'NOT FOUND',
        'Booking not found'
      ));
    }

    // Check if booking can be deleted
    if (['checked-in', 'completed'].includes(booking.booking_status)) {
      return res.status(400).json(errorResponse(
        1,
        'FAILED',
        `Cannot delete booking with status '${booking.booking_status}'`
      ));
    }

    // If room status was changed to reserved by this booking, change it back
    if (booking.booking_status === 'approved' || booking.booking_status === 'pending') {
      const room = await Room.findById(booking.room_id);
      if (room && room.current_status === 'reserved') {
        room.current_status = 'available';
        await room.save();
      }
    }

    // Delete the booking
    await Booking.findByIdAndDelete(id);

    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Booking deleted successfully'
    ));
  } catch (error) {
    logger.error('Error deleting booking:', error);
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

// export {
//   createBooking,
//   getAllBookings,
//   getBookingById,
//   updateBookingStatus,
//   updatePayment,
//   getBookingStatistics,
//   getUserBookings,
//   deleteBooking
// };

// Helper function to compare time strings (HH:MM format)
function compareTimeStrings(time1, time2) {
  const [hours1, minutes1] = time1.split(':').map(Number);
  const [hours2, minutes2] = time2.split(':').map(Number);
  
  if (hours1 !== hours2) {
    return hours1 - hours2;
  }
  return minutes1 - minutes2;
}
