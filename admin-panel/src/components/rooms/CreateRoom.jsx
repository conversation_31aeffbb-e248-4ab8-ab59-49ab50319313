import React, { useState, useEffect } from 'react';
import {
  Form, Input, InputNumber, Select, Upload, Button, Divider, DatePicker, Card, Switch,
  Checkbox, Space, Row, Col
} from 'antd';
import { PlusOutlined, MinusCircleOutlined } from '@ant-design/icons';
import ApiService from '../../utils/apiService';
import notificationWithIcon from '../../utils/notification';
import { roomStatusOptions } from '../../utils/responseAsStatus';
import dayjs from 'dayjs';

const { TextArea } = Input;
const { Option } = Select;

function CreateRoom({ onSuccess }) {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const [fileList, setFileList] = useState([]);
  const [roomTypes, setRoomTypes] = useState([]);
  const [staffMembers, setStaffMembers] = useState([]);

  useEffect(() => {
    fetchRoomTypes();
    fetchStaffMembers();
  }, []);

  const fetchRoomTypes = async () => {
    try {
      const response = await ApiService.get('/api/v1/room-types');
      if (response?.result?.data) {
        setRoomTypes(response.result.data);
      }
    } catch (error) {
      notificationWithIcon('error', 'Error', 'Failed to fetch room types');
    }
  };

  const fetchStaffMembers = async () => {
    try {
      const response = await ApiService.get('/api/v1/all-users-list?role=staff');
      if (response?.result_code === 0 && response?.result?.data?.rows) {
        setStaffMembers(response.result.data.rows);
      }
    } catch (error) {
      notificationWithIcon('error', 'Error', 'Failed to fetch staff members');
    }
  };

  const onFinish = async (values) => {
    if (fileList.length === 0) {
      notificationWithIcon('error', 'Error', 'At least one room image is required');
      return;
    }

    const formData = new FormData();
    
    // Basic room information
    formData.append('room_number', values.room_number);
    formData.append('floor', values.floor);
    formData.append('room_type', values.room_type);
    formData.append('current_status', values.current_status);
    formData.append('special_notes', values.special_notes || '');
    formData.append('is_active', values.is_active);

    // Features
    formData.append('features', JSON.stringify({
      has_balcony: values.has_balcony || false,
      has_sea_view: values.has_sea_view || false,
      is_accessible: values.is_accessible || false,
      is_smoking: values.is_smoking || false
    }));

    // Location
    formData.append('location', JSON.stringify({
      building: values.building || '',
      wing: values.wing || '',
      proximity_to_elevator: values.proximity_to_elevator || false,
      proximity_to_stairs: values.proximity_to_stairs || false
    }));

    // Handle room images
    fileList.forEach((file, index) => {
      formData.append('room_images', file.originFileObj);
      formData.append('room_images_data', JSON.stringify({
        is_primary: index === 0,
        caption: file.caption || ''
      }));
    });

    // Handle maintenance history if provided
    if (values.maintenance_history?.length) {
      formData.append('maintenance_history', JSON.stringify(
        values.maintenance_history.map(maintenance => ({
          start_date: maintenance.start_date?.format('YYYY-MM-DD'),
          end_date: maintenance.end_date?.format('YYYY-MM-DD'),
          reason: maintenance.reason,
          status: maintenance.status || 'scheduled',
          performed_by: maintenance.performed_by,
          notes: maintenance.notes || ''
        }))
      ));
    }

    // Last cleaned information if provided
    if (values.last_cleaned?.date) {
      formData.append('last_cleaned', JSON.stringify({
        date: values.last_cleaned.date?.format('YYYY-MM-DD'),
        by: values.last_cleaned.by,
        notes: values.last_cleaned.notes || ''
      }));
    }

    try {
      setLoading(true);
      const response = await ApiService.post('/api/v1/create-room', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      });

      if (response?.result_code === 0) {
        notificationWithIcon('success', 'Success', 'Room created successfully');
        form.resetFields();
        setFileList([]);
        onSuccess?.();
      } else {
        throw new Error(response?.result?.error || 'Failed to create room');
      }
    } catch (error) {
      notificationWithIcon('error', 'Error', error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="max-w-3xl mx-auto">
      <Form
        form={form}
        layout="vertical"
        onFinish={onFinish}
        initialValues={{
          current_status: 'available',
          is_active: true,
          has_balcony: false,
          has_sea_view: false,
          is_accessible: false,
          is_smoking: false,
          proximity_to_elevator: false,
          proximity_to_stairs: false
        }}
      >
        <Divider>Basic Information</Divider>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Form.Item
            name="room_number"
            label="Room Number"
            rules={[{ required: true, message: 'Room number is required' }]}
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="floor"
            label="Floor"
            rules={[{ required: true, message: 'Floor number is required' }]}
          >
            <InputNumber min={1} style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name="room_type"
            label="Room Type"
            rules={[{ required: true, message: 'Room type is required' }]}
          >
            <Select placeholder="Select room type">
              {roomTypes.map(type => (
                <Select.Option key={type._id} value={type._id}>
                  {type.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item
            label="Current Status"
            name="current_status"
            rules={[{ required: true, message: 'Please select room status' }]}
          >
            <Select>
              {roomStatusOptions.map(option => (
                <Select.Option 
                  key={option.value} 
                  value={option.value}
                >
                  <span style={{ color: option.color }}>●</span> {option.label}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </div>

        <Form.Item
          name="is_active"
          label="Active Status"
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>

        <Form.Item
          name="special_notes"
          label="Special Notes"
        >
          <TextArea rows={4} />
        </Form.Item>

        <Divider>Room Features</Divider>
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name="has_balcony"
              valuePropName="checked"
            >
              <Checkbox>Has Balcony</Checkbox>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="has_sea_view"
              valuePropName="checked"
            >
              <Checkbox>Has Sea View</Checkbox>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="is_accessible"
              valuePropName="checked"
            >
              <Checkbox>Accessible Room</Checkbox>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="is_smoking"
              valuePropName="checked"
            >
              <Checkbox>Smoking Allowed</Checkbox>
            </Form.Item>
          </Col>
        </Row>

        <Divider>Room Location</Divider>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Form.Item
            name="building"
            label="Building"
          >
            <Input />
          </Form.Item>

          <Form.Item
            name="wing"
            label="Wing"
          >
            <Input />
          </Form.Item>
        </div>

        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name="proximity_to_elevator"
              valuePropName="checked"
            >
              <Checkbox>Near Elevator</Checkbox>
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name="proximity_to_stairs"
              valuePropName="checked"
            >
              <Checkbox>Near Stairs</Checkbox>
            </Form.Item>
          </Col>
        </Row>

        <Divider>Room Images</Divider>
        <Form.Item
          label="Room Images"
          required
          tooltip="At least one image is required"
        >
          <Upload
            listType="picture-card"
            fileList={fileList}
            onChange={({ fileList: newFileList }) => setFileList(newFileList)}
            beforeUpload={() => false}
          >
            <div>
              <PlusOutlined />
              <div style={{ marginTop: 8 }}>Upload</div>
            </div>
          </Upload>
        </Form.Item>

        <Divider>Cleaning Information</Divider>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Form.Item
            name={['last_cleaned', 'date']}
            label="Last Cleaned Date"
          >
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>

          <Form.Item
            name={['last_cleaned', 'by']}
            label="Cleaned By"
          >
            <Select placeholder="Select staff member">
              {staffMembers.map(staff => (
                <Select.Option key={staff._id} value={staff._id}>
                  {staff.fullName || `${staff.firstName || ''} ${staff.lastName || ''}`}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </div>

        <Form.Item
          name={['last_cleaned', 'notes']}
          label="Cleaning Notes"
        >
          <TextArea rows={2} />
        </Form.Item>

        <Divider>Maintenance History</Divider>
        <Form.List name="maintenance_history">
          {(fields, { add, remove }) => (
            <>
              {fields.map(({ key, name, ...restField }) => (
                <div key={key} className="space-y-4 p-4 border rounded mb-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Form.Item
                      {...restField}
                      name={[name, 'start_date']}
                      label="Start Date"
                      rules={[{ required: true, message: 'Start date is required' }]}
                    >
                      <DatePicker style={{ width: '100%' }} />
                    </Form.Item>

                    <Form.Item
                      {...restField}
                      name={[name, 'end_date']}
                      label="End Date"
                      rules={[{ required: true, message: 'End date is required' }]}
                    >
                      <DatePicker style={{ width: '100%' }} />
                    </Form.Item>
                  </div>

                  <Form.Item
                    {...restField}
                    name={[name, 'reason']}
                    label="Reason"
                    rules={[{ required: true, message: 'Reason is required' }]}
                  >
                    <Input />
                  </Form.Item>

                  <Form.Item
                    {...restField}
                    name={[name, 'status']}
                    label="Status"
                  >
                    <Select defaultValue="scheduled">
                      <Option value="scheduled">Scheduled</Option>
                      <Option value="in_progress">In Progress</Option>
                      <Option value="completed">Completed</Option>
                      <Option value="cancelled">Cancelled</Option>
                    </Select>
                  </Form.Item>

                  <Form.Item
                    {...restField}
                    name={[name, 'performed_by']}
                    label="Performed By"
                    rules={[{ required: true, message: 'Staff member is required' }]}
                  >
                    <Select placeholder="Select staff member">
                      {staffMembers.map(staff => (
                        <Select.Option key={staff._id} value={staff._id}>
                          {staff.fullName || `${staff.userName}`}
                        </Select.Option>
                      ))}
                    </Select>
                  </Form.Item>

                  <Form.Item
                    {...restField}
                    name={[name, 'notes']}
                    label="Notes"
                  >
                    <TextArea rows={2} />
                  </Form.Item>

                  <Button 
                    type="danger" 
                    onClick={() => remove(name)}
                    icon={<MinusCircleOutlined />}
                  >
                    Remove Maintenance Record
                  </Button>
                </div>
              ))}
              <Form.Item>
                <Button 
                  type="dashed" 
                  onClick={() => add()} 
                  block 
                  icon={<PlusOutlined />}
                >
                  Add Maintenance Record
                </Button>
              </Form.Item>
            </>
          )}
        </Form.List>

        <Form.Item>
          <Button type="primary" htmlType="submit" loading={loading} block>
            Create Room
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
}

export default CreateRoom;
