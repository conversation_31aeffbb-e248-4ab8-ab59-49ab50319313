import React, { useState } from 'react';
import { Card, Descriptions, Tag, List, Skeleton, Result, Button, Form, Input, InputNumber, Switch, Modal, Select, Divider, DatePicker, TimePicker } from 'antd';
import { EditOutlined, PlusOutlined, MinusCircleOutlined } from '@ant-design/icons';
import useFetchData from '../../hooks/useFetchData';
import ApiService from '../../utils/apiService';
import notificationWithIcon from '../../utils/notification';
import dayjs from 'dayjs';

const { TextArea } = Input;

function RoomTypeDetails({ id, isAdd = false }) {
  const [loading, error, response] = useFetchData(isAdd ? null : `/api/v1/room-types/${id}`);
  const [isEditing, setIsEditing] = useState(isAdd);
  const [form] = Form.useForm();
  const [submitLoading, setSubmitLoading] = useState(false);

  const handleEdit = () => {
    form.setFieldsValue({
      name: response?.data?.name,
      description: response?.data?.description,
      base_price: response?.data?.base_price,
      is_active: response?.data?.is_active,
      specifications: {
        room_size: {
          value: response?.data?.specifications?.room_size?.value,
          unit: response?.data?.specifications?.room_size?.unit
        },
        max_occupancy: {
          adults: response?.data?.specifications?.max_occupancy?.adults,
          children: response?.data?.specifications?.max_occupancy?.children
        },
        bed_configuration: response?.data?.specifications?.bed_configuration || []
      },
      amenities: {
        basic: response?.data?.amenities?.basic || [],
        additional: response?.data?.amenities?.additional || []
      },
      policies: {
        allow_pets: response?.data?.policies?.allow_pets,
        provide_breakfast: response?.data?.policies?.provide_breakfast,
        smoking_allowed: response?.data?.policies?.smoking_allowed,
        cancellation_policy: {
          type: response?.data?.policies?.cancellation_policy?.type,
          hours_before: response?.data?.policies?.cancellation_policy?.hours_before,
          refund_percentage: response?.data?.policies?.cancellation_policy?.refund_percentage
        },
        check_in_time: response?.data?.policies?.check_in_time ? dayjs(response?.data?.policies?.check_in_time, 'HH:mm') : null,
        check_out_time: response?.data?.policies?.check_out_time ? dayjs(response?.data?.policies?.check_out_time, 'HH:mm') : null
      },
      pricing: {
        hourly_rate: response?.data?.pricing?.hourly_rate,
        daily_rate: response?.data?.pricing?.daily_rate,
        weekly_discount: response?.data?.pricing?.weekly_discount,
        monthly_discount: response?.data?.pricing?.monthly_discount,
        cleaning_fee: response?.data?.pricing?.cleaning_fee,
        security_deposit: response?.data?.pricing?.security_deposit
      },
      maintenance: {
        last_renovation: response?.data?.maintenance?.last_renovation ? dayjs(response?.data?.maintenance?.last_renovation) : null,
        next_maintenance: response?.data?.maintenance?.next_maintenance ? dayjs(response?.data?.maintenance?.next_maintenance) : null,
        maintenance_notes: response?.data?.maintenance?.maintenance_notes
      },
    });
    setIsEditing(true);
  };

  const handleSubmit = async (values) => {
    setSubmitLoading(true);
    try {
      const formData = {
        name: values.name,
        description: values.description,
        is_active: values.is_active,
        base_price: values.base_price,
        specifications: {
          room_size: {
            value: values.specifications.room_size.value,
            unit: values.specifications.room_size.unit
          },
          max_occupancy: {
            adults: values.specifications.max_occupancy.adults,
            children: values.specifications.max_occupancy.children
          },
          bed_configuration: values.specifications.bed_configuration || []
        },
        amenities: {
          basic: values.amenities.basic || [],
          additional: values.amenities.additional || []
        },
        policies: {
          allow_pets: values.policies.allow_pets,
          provide_breakfast: values.policies.provide_breakfast,
          smoking_allowed: values.policies.smoking_allowed,
          cancellation_policy: {
            type: values.policies.cancellation_policy.type,
            hours_before: values.policies.cancellation_policy.hours_before,
            refund_percentage: values.policies.cancellation_policy.refund_percentage
          },
          check_in_time: values.policies.check_in_time ? values.policies.check_in_time.format('HH:mm') : null,
          check_out_time: values.policies.check_out_time ? values.policies.check_out_time.format('HH:mm') : null,
        },
        pricing: {
          hourly_rate: values.pricing.hourly_rate || 0,
          daily_rate: values.pricing.daily_rate,
          weekly_discount: values.pricing.weekly_discount || 0,
          monthly_discount: values.pricing.monthly_discount || 0,
          cleaning_fee: values.pricing.cleaning_fee || 0,
          security_deposit: values.pricing.security_deposit || 0
        },
        maintenance: {
          last_renovation: values.maintenance?.last_renovation ? values.maintenance.last_renovation.format('YYYY-MM-DD') : null,
          next_maintenance: values.maintenance?.next_maintenance ? values.maintenance.next_maintenance.format('YYYY-MM-DD') : null,
          maintenance_notes: values.maintenance?.maintenance_notes || ''
        }
      };

      const apiCall = isAdd 
        ? ApiService.post('/api/v1/room-types', formData)
        : ApiService.put(`/api/v1/room-types/${id}`, formData);

      const response = await apiCall;
      
      if (response?.result_code === 0) {
        notificationWithIcon('success', 'SUCCESS', 
          isAdd ? 'Room type created successfully' : 'Room type updated successfully'
        );
        setIsEditing(false);
        if (!isAdd) {
          window.location.reload();
        }
      } else {
        throw new Error(response?.result?.error || 'Failed to process room type');
      }
    } catch (error) {
      notificationWithIcon('error', 'ERROR', error.message || 'Failed to process room type');
    } finally {
      setSubmitLoading(false);
    }
  };

  const RoomTypeForm = () => (
    <Form
      form={form}
      layout="vertical"
      onFinish={handleSubmit}
    >
      <Divider>Basic Information</Divider>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Form.Item
          name="name"
          label="Room Type Name"
          rules={[{ required: true, message: 'Please enter room type name' }]}
        >
          <Input />
        </Form.Item>

        <Form.Item
          name="base_price"
          label="Base Price"
          rules={[{ required: true, message: 'Please enter base price' }]}
        >
          <InputNumber min={0} precision={2} style={{ width: '100%' }} />
        </Form.Item>

        <Form.Item
          name="is_active"
          label="Status"
          valuePropName="checked"
        >
          <Switch checkedChildren="Active" unCheckedChildren="Inactive" />
        </Form.Item>
      </div>

      <Form.Item
        name="description"
        label="Description"
        rules={[{ required: true, message: 'Please enter description' }]}
      >
        <TextArea rows={4} />
      </Form.Item>

      <Divider>Room Specifications</Divider>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Form.Item label="Room Size">
          <Input.Group compact>
            <Form.Item
              name={['specifications', 'room_size', 'value']}
              noStyle
              rules={[{ required: true, message: 'Required' }]}
            >
              <InputNumber style={{ width: '70%' }} min={0} />
            </Form.Item>
            <Form.Item
              name={['specifications', 'room_size', 'unit']}
              noStyle
            >
              <Select style={{ width: '30%' }}>
                <Select.Option value="sqft">sqft</Select.Option>
                <Select.Option value="sqm">sqm</Select.Option>
              </Select>
            </Form.Item>
          </Input.Group>
        </Form.Item>

        <Form.Item label="Max Occupancy">
          <Input.Group compact>
            <Form.Item
              name={['specifications', 'max_occupancy', 'adults']}
              noStyle
              rules={[{ required: true, message: 'Required' }]}
            >
              <InputNumber min={1} style={{ width: '50%' }} placeholder="Adults" />
            </Form.Item>
            <Form.Item
              name={['specifications', 'max_occupancy', 'children']}
              noStyle
              rules={[{ required: true, message: 'Required' }]}
            >
              <InputNumber min={0} style={{ width: '50%' }} placeholder="Children" />
            </Form.Item>
          </Input.Group>
        </Form.Item>
      </div>

      <Form.List name={['specifications', 'bed_configuration']}>
        {(fields, { add, remove }) => (
          <>
            {fields.map(({ key, name, ...restField }) => (
              <div key={key} className="flex items-center gap-4">
                <Form.Item
                  {...restField}
                  name={[name, 'bed_type']}
                  rules={[{ required: true, message: 'Required' }]}
                >
                  <Select style={{ width: 200 }}>
                    <Select.Option value="single">Single</Select.Option>
                    <Select.Option value="double">Double</Select.Option>
                    <Select.Option value="queen">Queen</Select.Option>
                    <Select.Option value="king">King</Select.Option>
                    <Select.Option value="sofa_bed">Sofa Bed</Select.Option>
                  </Select>
                </Form.Item>
                <Form.Item
                  {...restField}
                  name={[name, 'count']}
                  rules={[{ required: true, message: 'Required' }]}
                >
                  <InputNumber min={1} placeholder="Count" />
                </Form.Item>
                <MinusCircleOutlined onClick={() => remove(name)} />
              </div>
            ))}
            <Form.Item>
              <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                Add Bed Configuration
              </Button>
            </Form.Item>
          </>
        )}
      </Form.List>

      <Divider>Amenities</Divider>
      <Form.Item
        name={['amenities', 'basic']}
        label="Basic Amenities"
      >
        <Select mode="multiple" placeholder="Select basic amenities">
          <Select.Option value="wifi">WiFi</Select.Option>
          <Select.Option value="tv">TV</Select.Option>
          <Select.Option value="ac">AC</Select.Option>
          <Select.Option value="phone">Phone</Select.Option>
          <Select.Option value="heater">Heater</Select.Option>
        </Select>
      </Form.Item>

      <Form.List name={['amenities', 'additional']}>
        {(fields, { add, remove }) => (
          <>
            {fields.map(({ key, name, ...restField }) => (
              <div key={key} className="space-y-2">
                <Form.Item
                  {...restField}
                  name={[name, 'name']}
                  rules={[{ required: true, message: 'Required' }]}
                >
                  <Input placeholder="Amenity Name" />
                </Form.Item>
                <Form.Item
                  {...restField}
                  name={[name, 'description']}
                >
                  <Input placeholder="Description" />
                </Form.Item>
                <Form.Item
                  {...restField}
                  name={[name, 'icon_url']}
                >
                  <Input placeholder="Icon URL" />
                </Form.Item>
                <Button type="text" danger onClick={() => remove(name)}>
                  <MinusCircleOutlined /> Remove
                </Button>
              </div>
            ))}
            <Form.Item>
              <Button type="dashed" onClick={() => add()} block icon={<PlusOutlined />}>
                Add Additional Amenity
              </Button>
            </Form.Item>
          </>
        )}
      </Form.List>

      <Divider>Policies</Divider>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Form.Item
          name={['policies', 'allow_pets']}
          label="Allow Pets"
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>
        <Form.Item
          name={['policies', 'provide_breakfast']}
          label="Provide Breakfast"
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>
        <Form.Item
          name={['policies', 'smoking_allowed']}
          label="Smoking Allowed"
          valuePropName="checked"
        >
          <Switch />
        </Form.Item>
      </div>

      <Form.Item label="Cancellation Policy">
        <Input.Group compact>
          <Form.Item
            name={['policies', 'cancellation_policy', 'type']}
            noStyle
            rules={[{ required: true, message: 'Required' }]}
          >
            <Select style={{ width: '30%' }}>
              <Select.Option value="flexible">Flexible</Select.Option>
              <Select.Option value="moderate">Moderate</Select.Option>
              <Select.Option value="strict">Strict</Select.Option>
            </Select>
          </Form.Item>
          <Form.Item
            name={['policies', 'cancellation_policy', 'hours_before']}
            noStyle
            rules={[{ required: true, message: 'Required' }]}
          >
            <InputNumber
              min={0}
              style={{ width: '35%' }}
              placeholder="Hours before"
            />
          </Form.Item>
          <Form.Item
            name={['policies', 'cancellation_policy', 'refund_percentage']}
            noStyle
            rules={[{ required: true, message: 'Required' }]}
          >
            <InputNumber
              min={0}
              max={100}
              style={{ width: '35%' }}
              placeholder="Refund %"
            />
          </Form.Item>
        </Input.Group>
      </Form.Item>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Form.Item
          name={['policies', 'check_in_time']}
          label="Check-in Time"
          rules={[{ required: true, message: 'Required' }]}
        >
          <TimePicker format="HH:mm" style={{ width: '100%' }} />
        </Form.Item>
        <Form.Item
          name={['policies', 'check_out_time']}
          label="Check-out Time"
          rules={[{ required: true, message: 'Required' }]}
        >
          <TimePicker format="HH:mm" style={{ width: '100%' }} />
        </Form.Item>
      </div>

      <Divider>Pricing</Divider>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Form.Item
          name={['pricing', 'hourly_rate']}
          label="Hourly Rate"
        >
          <InputNumber min={0} style={{ width: '100%' }} />
        </Form.Item>
        <Form.Item
          name={['pricing', 'daily_rate']}
          label="Daily Rate"
          rules={[{ required: true, message: 'Required' }]}
        >
          <InputNumber min={0} style={{ width: '100%' }} />
        </Form.Item>
        <Form.Item
          name={['pricing', 'weekly_discount']}
          label="Weekly Discount (%)"
        >
          <InputNumber min={0} max={100} style={{ width: '100%' }} />
        </Form.Item>
        <Form.Item
          name={['pricing', 'monthly_discount']}
          label="Monthly Discount (%)"
        >
          <InputNumber min={0} max={100} style={{ width: '100%' }} />
        </Form.Item>
        <Form.Item
          name={['pricing', 'cleaning_fee']}
          label="Cleaning Fee"
        >
          <InputNumber min={0} style={{ width: '100%' }} />
        </Form.Item>
        <Form.Item
          name={['pricing', 'security_deposit']}
          label="Security Deposit"
        >
          <InputNumber min={0} style={{ width: '100%' }} />
        </Form.Item>
      </div>

      <Divider>Maintenance</Divider>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <Form.Item
          name={['maintenance', 'last_renovation']}
          label="Last Renovation Date"
        >
          <DatePicker style={{ width: '100%' }} />
        </Form.Item>
        <Form.Item
          name={['maintenance', 'next_maintenance']}
          label="Next Maintenance Date"
        >
          <DatePicker style={{ width: '100%' }} />
        </Form.Item>
      </div>
      <Form.Item
        name={['maintenance', 'maintenance_notes']}
        label="Maintenance Notes"
      >
        <TextArea rows={4} />
      </Form.Item>

      <Form.Item>
        <Button type="primary" htmlType="submit" loading={submitLoading}>
          {isAdd ? 'Create' : 'Update'} Room Type
        </Button>
      </Form.Item>
    </Form>
  );

  if (!isAdd) {
    if (loading) {
      return <Skeleton active paragraph={{ rows: 10 }} />;
    }

    if (error) {
      return (
        <Result
          status="error"
          title="Failed to load room type details"
          subTitle={String(error)}
        />
      );
    }

    if (!response?.data) {
      return (
        <Result
          status="error"
          title="No Data"
          subTitle="Room type data not found"
        />
      );
    }
  }

  return (
    <Card 
      className="max-w-4xl mx-auto"
      title={isAdd ? "Create New Room Type" : "Room Type Details"}
      extra={
        !isEditing && !isAdd && (
          <Button 
            type="primary" 
            icon={<EditOutlined />} 
            onClick={handleEdit}
          >
            Edit
          </Button>
        )
      }
    >
      {isEditing ? (
        <RoomTypeForm />
      ) : (
        <Descriptions
          bordered
          column={{ xxl: 2, xl: 2, lg: 2, md: 1, sm: 1, xs: 1 }}
        >
          {/* Basic Information */}
          <Descriptions.Item label="Name">{response?.data?.name}</Descriptions.Item>
          <Descriptions.Item label="Base Price">${response?.data?.base_price}</Descriptions.Item>
          <Descriptions.Item label="Description" span={2}>{response?.data?.description}</Descriptions.Item>
          <Descriptions.Item label="Status">
            <Tag color={response?.data?.is_active ? 'success' : 'error'}>
              {response?.data?.is_active ? 'Active' : 'Inactive'}
            </Tag>
          </Descriptions.Item>

          {/* Specifications */}
          <Descriptions.Item label="Room Size">
            {response?.data?.specifications?.room_size?.value} {response?.data?.specifications?.room_size?.unit}
          </Descriptions.Item>
          <Descriptions.Item label="Max Occupancy">
            {response?.data?.specifications?.max_occupancy?.adults} Adults, {response?.data?.specifications?.max_occupancy?.children} Children
          </Descriptions.Item>
          <Descriptions.Item label="Bed Configuration" span={2}>
            <List
              dataSource={response?.data?.specifications?.bed_configuration}
              renderItem={bed => (
                <List.Item>
                  {bed.bed_type} - {bed.count} {bed.count > 1 ? 'beds' : 'bed'}
                </List.Item>
              )}
            />
          </Descriptions.Item>

          {/* Amenities */}
          <Descriptions.Item label="Basic Amenities" span={2}>
            {response?.data?.amenities?.basic?.map(amenity => (
              <Tag key={amenity} className="m-1">{amenity}</Tag>
            ))}
          </Descriptions.Item>
          <Descriptions.Item label="Additional Amenities" span={2}>
            <List
              dataSource={response?.data?.amenities?.additional}
              renderItem={amenity => (
                <List.Item>
                  <strong>{amenity.name}</strong>: {amenity.description}
                </List.Item>
              )}
            />
          </Descriptions.Item>

          {/* Policies */}
          <Descriptions.Item label="Pet Policy">
            {response?.data?.policies?.allow_pets ? 'Allowed' : 'Not Allowed'}
          </Descriptions.Item>
          <Descriptions.Item label="Breakfast">
            {response?.data?.policies?.provide_breakfast ? 'Included' : 'Not Included'}
          </Descriptions.Item>
          <Descriptions.Item label="Smoking">
            {response?.data?.policies?.smoking_allowed ? 'Allowed' : 'Not Allowed'}
          </Descriptions.Item>
          <Descriptions.Item label="Cancellation Policy">
            {response?.data?.policies?.cancellation_policy?.type || 'N/A'}
            {response?.data?.policies?.cancellation_policy?.hours_before && 
              response?.data?.policies?.cancellation_policy?.refund_percentage && (
              <div>
                ({response?.data?.policies?.cancellation_policy?.hours_before} hours before - 
                {response?.data?.policies?.cancellation_policy?.refund_percentage}% refund)
              </div>
            )}
          </Descriptions.Item>
          <Descriptions.Item label="Check-in Time">
            {response?.data?.policies?.check_in_time}
          </Descriptions.Item>
          <Descriptions.Item label="Check-out Time">
            {response?.data?.policies?.check_out_time}
          </Descriptions.Item>

          {/* Pricing */}
          <Descriptions.Item label="Hourly Rate">
            ${response?.data?.pricing?.hourly_rate}
          </Descriptions.Item>
          <Descriptions.Item label="Daily Rate">
            ${response?.data?.pricing?.daily_rate}
          </Descriptions.Item>
          <Descriptions.Item label="Weekly Discount">
            {response?.data?.pricing?.weekly_discount}%
          </Descriptions.Item>
          <Descriptions.Item label="Monthly Discount">
            {response?.data?.pricing?.monthly_discount}%
          </Descriptions.Item>
          <Descriptions.Item label="Cleaning Fee">
            ${response?.data?.pricing?.cleaning_fee}
          </Descriptions.Item>
          <Descriptions.Item label="Security Deposit">
            ${response?.data?.pricing?.security_deposit}
          </Descriptions.Item>

          {/* Maintenance */}
          <Descriptions.Item label="Last Renovation">
            {response?.data?.maintenance?.last_renovation}
          </Descriptions.Item>
          <Descriptions.Item label="Next Maintenance">
            {response?.data?.maintenance?.next_maintenance}
          </Descriptions.Item>
          <Descriptions.Item label="Maintenance Notes" span={2}>
            {response?.data?.maintenance?.maintenance_notes}
          </Descriptions.Item>
        </Descriptions>
      )}
    </Card>
  );
}

export default RoomTypeDetails;