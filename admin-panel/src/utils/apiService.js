import axios from 'axios';
import { 
  getSessionToken, 
  getRefreshToken, 
  setSessionAccessAndRefreshToken, 
  removeSessionAndLogoutUser,
  getTokenExpiration
} from './authentication';

const ApiService = axios.create({
  baseURL: process.env.REACT_APP_API_BASE_URL
});

// Flag to prevent multiple refresh token requests
let isRefreshing = false;
// Queue of failed requests to retry after token refresh
let failedQueue = [];
// Timer for token refresh
let refreshTimer = null;

// Process the queue of failed requests
const processQueue = (error, token = null) => {
  failedQueue.forEach(prom => {
    if (error) {
      prom.reject(error);
    } else {
      prom.resolve(token);
    }
  });
  
  failedQueue = [];
};

// Setup token refresh timer
const setupRefreshTimer = () => {
  // Clear any existing timer
  if (refreshTimer) {
    clearTimeout(refreshTimer);
  }
  
  const expiration = getTokenExpiration();
  if (!expiration) return;
  
  // Get current time in seconds
  const now = Math.floor(Date.now() / 1000);
  
  // Calculate time until token expires (in ms)
  // Refresh 1 minute before expiration
  const timeUntilRefresh = Math.max(0, (expiration - now - 60) * 1000);
  
  refreshTimer = setTimeout(async () => {
    try {
      const refreshToken = getRefreshToken();
      if (!refreshToken) return;
      
      // Only refresh if not already refreshing
      if (!isRefreshing) {
        isRefreshing = true;
        
        const response = await axios.post(
          `${process.env.REACT_APP_API_BASE_URL}/api/v1/auth/refresh-token`,
          { refresh_token: refreshToken },
          { headers: { 'Content-Type': 'application/json' } }
        );
        
        if (response.data && response.data.access_token) {
          setSessionAccessAndRefreshToken(
            response.data.access_token,
            response.data.refresh_token || refreshToken
          );
          
          // Update authorization header
          ApiService.defaults.headers.common.Authorization = `Bearer ${response.data.access_token}`;
          
          // Setup the next refresh
          setupRefreshTimer();
        }
      }
    } catch (error) {
      console.error('Failed to refresh token:', error);
      // Don't logout on background refresh failure
    } finally {
      isRefreshing = false;
    }
  }, timeUntilRefresh);
};

// Setup inactivity timer
let inactivityTimer = null;
const INACTIVITY_TIMEOUT = 24 * 60 * 60 * 1000; // 24 hours (1 day)

const resetInactivityTimer = () => {
  if (inactivityTimer) {
    clearTimeout(inactivityTimer);
  }
  
  inactivityTimer = setTimeout(() => {
    console.log('User inactive for 24 hours, logging out');
    removeSessionAndLogoutUser();
  }, INACTIVITY_TIMEOUT);
};

// Add event listeners for user activity
if (typeof window !== 'undefined') {
  ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
    window.addEventListener(event, resetInactivityTimer, false);
  });
  
  // Initial setup
  resetInactivityTimer();
}

/**
 * Interceptor for all requests
 */
ApiService.interceptors.request.use(
  (config) => {
    /**
     * Add your request interceptor logic here: setting headers, authorization etc.
     */
    config.headers['Content-Type'] = 'application/json';

    if (!config?.auth) {
      const token = getSessionToken();
      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
        
        // Reset inactivity timer on each request
        resetInactivityTimer();
      }
    }

    return config;
  },
  (error) => Promise.reject(error)
);

// Initialize refresh timer when the service is imported
setupRefreshTimer();

/**
 * Interceptor for all responses
 */
ApiService.interceptors.response.use(
  /**
  * Add logic for successful response
  */
  (response) => response?.data || {},

  /**
  * Add logic for any error from backend
  */
  async (error) => {
    const originalRequest = error.config;

    // If the error is due to other reasons than 401, reject it normally
    if (!error.response || error.response.status !== 401) {
      if (error?.response?.data?.result_code === 11) {
        // if authorized to logout user and redirect login page
        removeSessionAndLogoutUser();
      }
      return Promise.reject(error);
    }

    // Prevent infinite loops
    if (originalRequest._retry) {
      removeSessionAndLogoutUser();
      return Promise.reject(error);
    }

    if (isRefreshing) {
      return new Promise((resolve, reject) => {
        failedQueue.push({ resolve, reject });
      })
        .then(token => {
          originalRequest.headers.Authorization = `Bearer ${token}`;
          return axios(originalRequest);
        })
        .catch(err => Promise.reject(err));
    }

    originalRequest._retry = true;
    isRefreshing = true;

    const refreshToken = getRefreshToken();
    
    if (!refreshToken) {
      isRefreshing = false;
      removeSessionAndLogoutUser();
      return Promise.reject(error);
    }

    try {
      // Call your refresh token endpoint
      const response = await axios.post(
        `${process.env.REACT_APP_API_BASE_URL}/api/v1/auth/refresh-token`,
        { refresh_token: refreshToken },
        { headers: { 'Content-Type': 'application/json' } }
      );

      if (response.data && response.data.access_token) {
        // Update tokens in storage
        setSessionAccessAndRefreshToken(
          response.data.access_token,
          response.data.refresh_token || refreshToken
        );
        
        // Update authorization header
        ApiService.defaults.headers.common.Authorization = `Bearer ${response.data.access_token}`;
        originalRequest.headers.Authorization = `Bearer ${response.data.access_token}`;
        
        // Process the queue
        processQueue(null, response.data.access_token);
        
        // Return the original request
        return axios(originalRequest);
      } else {
        processQueue(new Error('Failed to refresh token'));
        removeSessionAndLogoutUser();
        return Promise.reject(error);
      }
    } catch (refreshError) {
      processQueue(refreshError);
      removeSessionAndLogoutUser();
      return Promise.reject(refreshError);
    } finally {
      isRefreshing = false;
    }
  }
);

export default ApiService;
