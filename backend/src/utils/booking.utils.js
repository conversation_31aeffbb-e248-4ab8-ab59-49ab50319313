import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat.js';
import isBetween from 'dayjs/plugin/isBetween.js';

dayjs.extend(customParseFormat);
dayjs.extend(isBetween);
/**
 * Calculate the number of billable days based on check-in/out times
 * @param {Date} checkInDate - Check-in date
 * @param {Date} checkOutDate - Check-out date
 * @param {String} standardCheckInTime - Room type's standard check-in time (format: "HH:mm")
 * @param {String} standardCheckOutTime - Room type's standard check-out time (format: "HH:mm")
 * @returns {number} Number of billable days
 */
const calculateBillableDays = (checkInDate, checkOutDate, standardCheckInTime, standardCheckOutTime) => {
  const checkIn = dayjs(checkInDate);
  const checkOut = dayjs(checkOutDate);
  
  // Parse standard times
  const [checkInHour, checkInMinute] = standardCheckInTime.split(':').map(Number);
  const [checkOutHour, checkOutMinute] = standardCheckOutTime.split(':').map(Number);
  
  // Set standard check-in/out times for the actual dates
  const standardCheckIn = checkIn.hour(checkInHour).minute(checkInMinute);
  const standardCheckOut = checkOut.hour(checkOutHour).minute(checkOutMinute);
  
  // Calculate base number of days
  const baseNights = checkOut.diff(checkIn, 'day');
  
  // Add extra day if checking out after standard check-out time
  const isLateCheckOut = checkOut.isAfter(standardCheckOut);
  
  return baseNights + (isLateCheckOut ? 1 : 0);
};

/**
 * Calculate total price for the booking
 * @param {Object} roomType - Room type object with pricing details
 * @param {Date} checkInDate - Check-in date
 * @param {Date} checkOutDate - Check-out date
 * @returns {Object} Price breakdown and total
 */
const calculateBookingPrice = (roomType, checkInDate, checkOutDate) => {
  const billableDays = calculateBillableDays(
    checkInDate,
    checkOutDate,
    roomType.policies.check_in_time,
    roomType.policies.check_out_time
  );

  // Base calculation using daily rate
  let basePrice = roomType.pricing.daily_rate * billableDays;
  
  // Apply weekly discount if applicable
  if (billableDays >= 7 && roomType.pricing.weekly_discount > 0) {
    const weeklyDiscountAmount = (basePrice * roomType.pricing.weekly_discount) / 100;
    basePrice -= weeklyDiscountAmount;
  }
  
  // Apply monthly discount if applicable
  if (billableDays >= 30 && roomType.pricing.monthly_discount > 0) {
    const monthlyDiscountAmount = (basePrice * roomType.pricing.monthly_discount) / 100;
    basePrice -= monthlyDiscountAmount;
  }

  // Add cleaning fee if applicable
  const cleaningFee = roomType.pricing.cleaning_fee || 0;
  
  // Add security deposit if applicable
  const securityDeposit = roomType.pricing.security_deposit || 0;

  const totalPrice = basePrice + cleaningFee + securityDeposit;

  return {
    basePrice: Number(basePrice.toFixed(2)),
    cleaningFee: Number(cleaningFee.toFixed(2)),
    securityDeposit: Number(securityDeposit.toFixed(2)),
    totalPrice: Number(totalPrice.toFixed(2)),
    billableDays,
    appliedDiscounts: {
      weekly: billableDays >= 7 ? roomType.pricing.weekly_discount : 0,
      monthly: billableDays >= 30 ? roomType.pricing.monthly_discount : 0
    }
  };
};

export { calculateBookingPrice, calculateBillableDays };
