import React, { useState, useEffect } from 'react';
import {
  Card,
  Row,
  Col,
  Select,
  DatePicker,
  Button,
  Table,
  Statistic,
  Tabs,
  Space,
  message,
  Spin,
  Typography,
  Tag
} from 'antd';
import {
  DownloadOutlined,
  CalendarOutlined,
  FileTextOutlined,ClearOutlined,
  ToolOutlined,
  DollarOutlined
} from '@ant-design/icons';
import ApiService from '../../utils/apiService';
import notificationWithIcon from '../../utils/notification';

const { Title } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

function Reports() {
  const [loading, setLoading] = useState(false);
  const [activeTab, setActiveTab] = useState('bookings');
  const [dateRange, setDateRange] = useState(null);
  const [selectedMonth, setSelectedMonth] = useState(null);
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  
  // Data states
  const [bookingsData, setBookingsData] = useState({ bookings: [], summary: {} });
  const [cleaningsData, setCleaningsData] = useState({ cleanings: [], summary: {} });
  const [maintenanceData, setMaintenanceData] = useState({ maintenance: [], summary: {} });
  const [revenueData, setRevenueData] = useState({ revenue: [], summary: {} });

  // Fetch data based on active tab
  const fetchReportData = async (tab = activeTab) => {
    setLoading(true);
    try {
      let params = {};
      
      if (dateRange && dateRange.length === 2) {
        params.startDate = dateRange[0].format('YYYY-MM-DD');
        params.endDate = dateRange[1].format('YYYY-MM-DD');
      } else if (selectedMonth && selectedYear) {
        params.month = selectedMonth;
        params.year = selectedYear;
      }

      let endpoint = '';
      switch (tab) {
        case 'bookings':
          endpoint = '/reports/bookings/monthly';
          break;
        case 'cleanings':
          endpoint = '/reports/cleanings/monthly';
          break;
        case 'maintenance':
          endpoint = '/reports/maintenance/monthly';
          break;
        case 'revenue':
          endpoint = '/reports/revenue/monthly';
          break;
        default:
          endpoint = '/reports/bookings/monthly';
      }

      const response = await ApiService.get(endpoint, { params });
      
      if (response.result_code === 0) {
        switch (tab) {
          case 'bookings':
            setBookingsData(response.result);
            break;
          case 'cleanings':
            setCleaningsData(response.result);
            break;
          case 'maintenance':
            setMaintenanceData(response.result);
            break;
          case 'revenue':
            setRevenueData(response.result);
            break;
        }
      } else {
        notificationWithIcon('error', 'Error', response.result_message || 'Failed to fetch report data');
      }
    } catch (error) {
      console.error('Error fetching report data:', error);
      notificationWithIcon('error', 'Error', 'Failed to fetch report data');
    } finally {
      setLoading(false);
    }
  };

  // Export report as CSV
  const exportReport = async (type) => {
    try {
      let params = { type };
      
      if (dateRange && dateRange.length === 2) {
        params.startDate = dateRange[0].format('YYYY-MM-DD');
        params.endDate = dateRange[1].format('YYYY-MM-DD');
      } else if (selectedMonth && selectedYear) {
        params.month = selectedMonth;
        params.year = selectedYear;
      }

      const response = await fetch(
        `${process.env.REACT_APP_API_BASE_URL}/api/v1/reports/export?${new URLSearchParams(params)}`,
        {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('accessToken')}`
          }
        }
      );

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${type}_report_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        message.success('Report exported successfully');
      } else {
        message.error('Failed to export report');
      }
    } catch (error) {
      console.error('Error exporting report:', error);
      message.error('Failed to export report');
    }
  };

  useEffect(() => {
    fetchReportData();
  }, [activeTab, dateRange, selectedMonth, selectedYear]);

  // Table columns for different report types
  const bookingsColumns = [
    {
      title: 'Booking ID',
      dataIndex: 'booking_id',
      key: 'booking_id',
    },
    {
      title: 'Status',
      dataIndex: 'booking_status',
      key: 'booking_status',
      render: (status) => {
        const colors = {
          pending: 'orange',
          approved: 'green',
          cancelled: 'red',
          'checked-in': 'blue',
          'checked-out': 'purple'
        };
        return <Tag color={colors[status] || 'default'}>{status?.toUpperCase()}</Tag>;
      }
    },
    {
      title: 'Guest',
      key: 'guest',
      render: (record) => record.is_guest ? record.guest_name : record.user?.fullName || 'N/A'
    },
    {
      title: 'Room',
      key: 'room',
      render: (record) => `${record.room?.room_number || 'N/A'} (${record.roomType?.type_name || 'N/A'})`
    },
    {
      title: 'Amount',
      dataIndex: 'total_amount',
      key: 'total_amount',
      render: (amount) => `$${amount || 0}`
    },
    {
      title: 'Payment Status',
      dataIndex: ['payment', 'status'],
      key: 'payment_status',
      render: (status) => <Tag color={status === 'completed' ? 'green' : 'orange'}>{status?.toUpperCase()}</Tag>
    },
    {
      title: 'Created Date',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date) => new Date(date).toLocaleDateString()
    }
  ];

  const cleaningsColumns = [
    {
      title: 'Room Number',
      key: 'room_number',
      render: (record) => record.room?.room_number || 'N/A'
    },
    {
      title: 'Floor',
      key: 'floor',
      render: (record) => record.room?.floor || 'N/A'
    },
    {
      title: 'Cleaner',
      key: 'cleaner',
      render: (record) => record.cleaner?.fullName || record.cleaner?.userName || 'N/A'
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const colors = {
          pending: 'orange',
          in_progress: 'blue',
          completed: 'green',
          verified: 'purple'
        };
        return <Tag color={colors[status] || 'default'}>{status?.replace('_', ' ').toUpperCase()}</Tag>;
      }
    },
    {
      title: 'Cleaned Date',
      dataIndex: 'cleaned_at',
      key: 'cleaned_at',
      render: (date) => date ? new Date(date).toLocaleDateString() : 'N/A'
    },
    {
      title: 'Time Spent (min)',
      dataIndex: 'time_spent',
      key: 'time_spent',
      render: (time) => time || 0
    },
    {
      title: 'Issues Reported',
      dataIndex: 'issues_reported',
      key: 'issues_reported',
      render: (issues) => issues?.length || 0
    }
  ];

  const maintenanceColumns = [
    {
      title: 'Room Number',
      key: 'room_number',
      render: (record) => record.room?.room_number || 'N/A'
    },
    {
      title: 'Reason',
      dataIndex: 'reason',
      key: 'reason'
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      render: (status) => {
        const colors = {
          scheduled: 'orange',
          in_progress: 'blue',
          completed: 'green',
          cancelled: 'red'
        };
        return <Tag color={colors[status] || 'default'}>{status?.replace('_', ' ').toUpperCase()}</Tag>;
      }
    },
    {
      title: 'Start Date',
      dataIndex: 'start_date',
      key: 'start_date',
      render: (date) => new Date(date).toLocaleDateString()
    },
    {
      title: 'End Date',
      dataIndex: 'end_date',
      key: 'end_date',
      render: (date) => new Date(date).toLocaleDateString()
    },
    {
      title: 'Performed By',
      key: 'performer',
      render: (record) => record.performer?.fullName || record.performer?.userName || 'N/A'
    },
    {
      title: 'Cost',
      key: 'cost',
      render: (record) => record.cost?.amount ? `$${record.cost.amount}` : 'N/A'
    }
  ];

  const revenueColumns = [
    {
      title: 'Date',
      key: 'date',
      render: (record) => new Date(record._id.year, record._id.month - 1, record._id.day).toLocaleDateString()
    },
    {
      title: 'Room Type',
      key: 'room_type',
      render: (record) => record._id.roomType || 'Unknown'
    },
    {
      title: 'Daily Revenue',
      dataIndex: 'dailyRevenue',
      key: 'dailyRevenue',
      render: (amount) => `$${amount || 0}`
    },
    {
      title: 'Booking Count',
      dataIndex: 'bookingCount',
      key: 'bookingCount'
    }
  ];

  return (
    <div className="p-6">
      <div className="mb-6">
        <Title level={2}>Reports & Analytics</Title>
        
        {/* Filters */}
        <Card className="mb-4">
          <Row gutter={16} align="middle">
            <Col span={6}>
              <Select
                placeholder="Select Month"
                value={selectedMonth}
                onChange={setSelectedMonth}
                allowClear
                style={{ width: '100%' }}
              >
                {Array.from({ length: 12 }, (_, i) => (
                  <Option key={i + 1} value={i + 1}>
                    {new Date(0, i).toLocaleString('default', { month: 'long' })}
                  </Option>
                ))}
              </Select>
            </Col>
            <Col span={6}>
              <Select
                placeholder="Select Year"
                value={selectedYear}
                onChange={setSelectedYear}
                style={{ width: '100%' }}
              >
                {Array.from({ length: 5 }, (_, i) => {
                  const year = new Date().getFullYear() - i;
                  return (
                    <Option key={year} value={year}>
                      {year}
                    </Option>
                  );
                })}
              </Select>
            </Col>
            <Col span={8}>
              <RangePicker
                value={dateRange}
                onChange={setDateRange}
                style={{ width: '100%' }}
                placeholder={['Start Date', 'End Date']}
              />
            </Col>
            <Col span={4}>
              <Button
                type="primary"
                onClick={() => fetchReportData()}
                loading={loading}
                icon={<CalendarOutlined />}
              >
                Apply Filters
              </Button>
            </Col>
          </Row>
        </Card>
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane
          tab={
            <span>
              <FileTextOutlined />
              Bookings Report
            </span>
          }
          key="bookings"
        >
          <Spin spinning={loading}>
            <Row gutter={16} className="mb-4">
              <Col span={6}>
                <Card>
                  <Statistic
                    title="Total Bookings"
                    value={bookingsData.summary?.totalBookings || 0}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="Confirmed Bookings"
                    value={bookingsData.summary?.confirmedBookings || 0}
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="Total Revenue"
                    value={bookingsData.summary?.totalRevenue || 0}
                    prefix="$"
                    valueStyle={{ color: '#faad14' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="Average Booking Value"
                    value={bookingsData.summary?.averageBookingValue || 0}
                    prefix="$"
                    precision={2}
                    valueStyle={{ color: '#722ed1' }}
                  />
                </Card>
              </Col>
            </Row>
            
            <Card
              title="Bookings Details"
              extra={
                <Button
                  type="primary"
                  icon={<DownloadOutlined />}
                  onClick={() => exportReport('bookings')}
                >
                  Export CSV
                </Button>
              }
            >
              <Table
                columns={bookingsColumns}
                dataSource={bookingsData.bookings || []}
                rowKey="_id"
                pagination={{ pageSize: 10 }}
                scroll={{ x: 1200 }}
              />
            </Card>
          </Spin>
        </TabPane>

        <TabPane
          tab={
            <span>
              <ClearOutlined />
              Cleanings Report
            </span>
          }
          key="cleanings"
        >
          <Spin spinning={loading}>
            <Row gutter={16} className="mb-4">
              <Col span={6}>
                <Card>
                  <Statistic
                    title="Total Cleanings"
                    value={cleaningsData.summary?.totalCleanings || 0}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="Completed Cleanings"
                    value={cleaningsData.summary?.completedCleanings || 0}
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="Average Time (min)"
                    value={cleaningsData.summary?.averageTimeSpent || 0}
                    precision={1}
                    valueStyle={{ color: '#faad14' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="Issues Reported"
                    value={cleaningsData.summary?.totalIssuesReported || 0}
                    valueStyle={{ color: '#f5222d' }}
                  />
                </Card>
              </Col>
            </Row>

            <Card
              title="Cleanings Details"
              extra={
                <Button
                  type="primary"
                  icon={<DownloadOutlined />}
                  onClick={() => exportReport('cleanings')}
                >
                  Export CSV
                </Button>
              }
            >
              <Table
                columns={cleaningsColumns}
                dataSource={cleaningsData.cleanings || []}
                rowKey="_id"
                pagination={{ pageSize: 10 }}
                scroll={{ x: 1200 }}
              />
            </Card>
          </Spin>
        </TabPane>

        <TabPane
          tab={
            <span>
              <ToolOutlined />
              Maintenance Report
            </span>
          }
          key="maintenance"
        >
          <Spin spinning={loading}>
            <Row gutter={16} className="mb-4">
              <Col span={6}>
                <Card>
                  <Statistic
                    title="Total Maintenance"
                    value={maintenanceData.summary?.totalMaintenance || 0}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="Completed"
                    value={maintenanceData.summary?.completedMaintenance || 0}
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="Total Cost"
                    value={maintenanceData.summary?.totalCost || 0}
                    prefix="$"
                    valueStyle={{ color: '#faad14' }}
                  />
                </Card>
              </Col>
              <Col span={6}>
                <Card>
                  <Statistic
                    title="Average Cost"
                    value={maintenanceData.summary?.averageCost || 0}
                    prefix="$"
                    precision={2}
                    valueStyle={{ color: '#722ed1' }}
                  />
                </Card>
              </Col>
            </Row>

            <Card
              title="Maintenance Details"
              extra={
                <Button
                  type="primary"
                  icon={<DownloadOutlined />}
                  onClick={() => exportReport('maintenance')}
                >
                  Export CSV
                </Button>
              }
            >
              <Table
                columns={maintenanceColumns}
                dataSource={maintenanceData.maintenance || []}
                rowKey="_id"
                pagination={{ pageSize: 10 }}
                scroll={{ x: 1200 }}
              />
            </Card>
          </Spin>
        </TabPane>

        <TabPane
          tab={
            <span>
              <DollarOutlined />
              Revenue Report
            </span>
          }
          key="revenue"
        >
          <Spin spinning={loading}>
            <Row gutter={16} className="mb-4">
              <Col span={8}>
                <Card>
                  <Statistic
                    title="Total Revenue"
                    value={revenueData.summary?.totalRevenue || 0}
                    prefix="$"
                    valueStyle={{ color: '#52c41a' }}
                  />
                </Card>
              </Col>
              <Col span={8}>
                <Card>
                  <Statistic
                    title="Total Bookings"
                    value={revenueData.summary?.totalBookings || 0}
                    valueStyle={{ color: '#1890ff' }}
                  />
                </Card>
              </Col>
              <Col span={8}>
                <Card>
                  <Statistic
                    title="Average Revenue per Booking"
                    value={revenueData.summary?.averageRevenuePerBooking || 0}
                    prefix="$"
                    precision={2}
                    valueStyle={{ color: '#722ed1' }}
                  />
                </Card>
              </Col>
            </Row>

            <Card
              title="Revenue Details"
              extra={
                <Button
                  type="primary"
                  icon={<DownloadOutlined />}
                  onClick={() => exportReport('revenue')}
                >
                  Export CSV
                </Button>
              }
            >
              <Table
                columns={revenueColumns}
                dataSource={revenueData.revenue || []}
                rowKey={(record) => `${record._id.year}-${record._id.month}-${record._id.day}-${record._id.roomType}`}
                pagination={{ pageSize: 10 }}
                scroll={{ x: 1200 }}
              />
            </Card>
          </Spin>
        </TabPane>
      </Tabs>
    </div>
  );
}

export default Reports;
