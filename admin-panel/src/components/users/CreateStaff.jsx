import { LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import {
  Button, Col, Form, Input, Row, Select, Upload, message
} from 'antd';
import React, { useState } from 'react';
import ApiService from '../../utils/apiService';
import notificationWithIcon from '../../utils/notification';

const { Option } = Select;

function CreateStaff() {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [imageUrl, setImageUrl] = useState('');
  const [uploadLoading, setUploadLoading] = useState(false);

  // function to handle before upload
  const beforeUpload = (file) => {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('You can only upload JPG/PNG file!');
    }
    const isLt2M = file.size / 1024 / 1024 < 2;
    if (!isLt2M) {
      message.error('Image must smaller than 2MB!');
    }
    return isJpgOrPng && isLt2M;
  };

  // function to handle change image
  const handleChange = (info) => {
    if (info.file.status === 'uploading') {
      setUploadLoading(true);
      return;
    }
    if (info.file.status === 'done') {
      setUploadLoading(false);
      setImageUrl(info.file.response.secure_url);
    }
  };

  // function to handle submit form
  const onFinish = (values) => {
    setLoading(true);
    ApiService.post('/api/v1/register-user', {
      ...values,
      avatar: imageUrl,
      role: 'staff'
    })
      .then((res) => {
        setLoading(false);
        if (res?.result_code === 0) {
          notificationWithIcon('success', 'SUCCESS', res?.result?.message || 'Staff created successful');
          form.resetFields();
          setImageUrl('');
        } else {
          notificationWithIcon('error', 'ERROR', 'Sorry! Something went wrong. App server error');
        }
      })
      .catch((err) => {
        setLoading(false);
        notificationWithIcon('error', 'ERROR', err?.response?.data?.result?.error?.message || err?.response?.data?.result?.error || 'Sorry! Something went wrong. App server error');
      });
  };

  // upload button
  const uploadButton = (
    <div>
      {uploadLoading ? <LoadingOutlined /> : <PlusOutlined />}
      <div style={{ marginTop: 8 }}>Upload</div>
    </div>
  );

  return (
    <div className='m-auto max-w-screen-md'>
      <Form
        form={form}
        layout='vertical'
        onFinish={onFinish}
        scrollToFirstError
      >
        <Row gutter={16}>
          <Col xs={24} sm={24} md={24} lg={24} xl={24}>
            <div className='text-center mb-4'>
              <Upload
                name='file'
                listType='picture-card'
                className='avatar-uploader'
                showUploadList={false}
                action={`${process.env.REACT_APP_API_BASE_URL}/api/v1/upload-file`}
                beforeUpload={beforeUpload}
                onChange={handleChange}
                maxCount={1}
              >
                {imageUrl ? <img src={imageUrl} alt='avatar' style={{ width: '100%' }} /> : uploadButton}
              </Upload>
            </div>
          </Col>

          <Col xs={24} sm={24} md={12} lg={12} xl={12}>
            <Form.Item
              name='fullName'
              label='Full Name'
              rules={[
                {
                  required: true,
                  message: 'Please input staff full name!'
                }
              ]}
            >
              <Input placeholder='Enter staff full name' size='large' />
            </Form.Item>
          </Col>

          <Col xs={24} sm={24} md={12} lg={12} xl={12}>
            <Form.Item
              name='userName'
              label='Username'
              rules={[
                {
                  required: true,
                  message: 'Please input staff username!'
                }
              ]}
            >
              <Input placeholder='Enter staff username' size='large' />
            </Form.Item>
          </Col>

          <Col xs={24} sm={24} md={12} lg={12} xl={12}>
            <Form.Item
              name='email'
              label='Email'
              rules={[
                {
                  required: true,
                  message: 'Please input staff email!'
                },
                {
                  type: 'email',
                  message: 'Please enter a valid email!'
                }
              ]}
            >
              <Input placeholder='Enter staff email' size='large' />
            </Form.Item>
          </Col>

          <Col xs={24} sm={24} md={12} lg={12} xl={12}>
            <Form.Item
              name='phone'
              label='Phone'
              rules={[
                {
                  required: true,
                  message: 'Please input staff phone!'
                }
              ]}
            >
              <Input placeholder='Enter staff phone' size='large' />
            </Form.Item>
          </Col>

          <Col xs={24} sm={24} md={12} lg={12} xl={12}>
            <Form.Item
              name='password'
              label='Password'
              rules={[
                {
                  required: true,
                  message: 'Please input staff password!'
                }
              ]}
            >
              <Input.Password placeholder='Enter staff password' size='large' />
            </Form.Item>
          </Col>

          <Col xs={24} sm={24} md={12} lg={12} xl={12}>
            <Form.Item
              name='department'
              label='Department'
              rules={[
                {
                  required: true,
                  message: 'Please select staff department!'
                }
              ]}
            >
              <Select placeholder='Select staff department' size='large'>
                <Option value='housekeeping'>Housekeeping</Option>
                <Option value='frontdesk'>Front Desk</Option>
                <Option value='maintenance'>Maintenance</Option>
                <Option value='food_service'>Food Service</Option>
                <Option value='security'>Security</Option>
                <Option value='management'>Management</Option>
              </Select>
            </Form.Item>
          </Col>

          <Col xs={24} sm={24} md={12} lg={12} xl={12}>
            <Form.Item
              name='status'
              label='Status'
              initialValue='active'
              rules={[
                {
                  required: true,
                  message: 'Please select staff status!'
                }
              ]}
            >
              <Select placeholder='Select staff status' size='large'>
                <Option value='active'>Active</Option>
                <Option value='inactive'>Inactive</Option>
              </Select>
            </Form.Item>
          </Col>

          <Col xs={24} sm={24} md={12} lg={12} xl={12}>
            <Form.Item
              name='address'
              label='Address'
            >
              <Input.TextArea rows={4} placeholder='Enter staff address' />
            </Form.Item>
          </Col>
        </Row>

        <Row>
          <Col xs={24} sm={24} md={24} lg={24} xl={24}>
            <Form.Item>
              <Button
                type='primary'
                htmlType='submit'
                loading={loading}
                size='large'
                block
              >
                Create Staff
              </Button>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </div>
  );
}

export default React.memo(CreateStaff);