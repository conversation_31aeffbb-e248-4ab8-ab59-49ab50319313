
import { errorResponse, successResponse } from '../config/app.response.js';
import User from '../models/user.model.js';
import Room from '../models/room.model.js';
import Booking from '../models/booking.model.js';
import logger from '../middleware/winston.logger.js';

// TODO: Controller for get users list for admin
const getDashboardData = async (req, res) => {
  try {
    const { user } = req;

    if (!user) {
      return res.status(401).json(errorResponse(4, 'UNAUTHORIZED', 'Authentication required'));
    }

    // Calculate date range (last 6 months)
    const sixMonthsAgo = new Date();
    sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

    // Get current date for today's bookings
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const [usersData, roomsData, bookingsData, todayBookings, revenueByMonth] = await Promise.all([
      // Users Statistics
      User.aggregate([
        {
          $facet: {
            summary: [
              {
                $group: {
                  _id: null,
                  total: { $sum: 1 },
                  active: { $sum: { $cond: [{ $eq: ['$status', 'login'] }, 1, 0] } },
                  inactive: { $sum: { $cond: [{ $eq: ['$status', 'logout'] }, 1, 0] } },
                  blocked: { $sum: { $cond: [{ $eq: ['$status', 'blocked'] }, 1, 0] } }
                }
              }
            ],
            trends: [
              { $match: { createdAt: { $gte: sixMonthsAgo } } },
              {
                $group: {
                  _id: {
                    month: { $month: '$createdAt' },
                    year: { $year: '$createdAt' }
                  },
                  count: { $sum: 1 }
                }
              },
              { $sort: { '_id.year': 1, '_id.month': 1 } }
            ],
            byRole: [
              {
                $group: {
                  _id: '$role',
                  count: { $sum: 1 }
                }
              }
            ]
          }
        }
      ]),

      // Rooms Statistics
      Room.aggregate([
        {
          $facet: {
            summary: [
              {
                $group: {
                  _id: null,
                  total: { $sum: 1 },
                  operational: { $sum: { $cond: [{ $eq: ['$current_status', 'operational'] }, 1, 0] } },
                  maintenance: { $sum: { $cond: [{ $eq: ['$current_status', 'maintenance'] }, 1, 0] } },
                  outOfService: { $sum: { $cond: [{ $eq: ['$current_status', 'out_of_service'] }, 1, 0] } },
                  reserved: { $sum: { $cond: [{ $eq: ['$current_status', 'reserved'] }, 1, 0] } },
                  occupied: { $sum: { $cond: [{ $eq: ['$current_status', 'occupied'] }, 1, 0] } }
                }
              }
            ],
            byFloor: [
              {
                $group: {
                  _id: '$floor',
                  count: { $sum: 1 }
                }
              },
              { $sort: { '_id': 1 } }
            ],
            byType: [
              {
                $lookup: {
                  from: 'roomtypes',
                  localField: 'room_type',
                  foreignField: '_id',
                  as: 'roomTypeInfo'
                }
              },
              {
                $unwind: {
                  path: '$roomTypeInfo',
                  preserveNullAndEmptyArrays: true
                }
              },
              {
                $group: {
                  _id: '$roomTypeInfo.name',
                  count: { $sum: 1 }
                }
              }
            ]
          }
        }
      ]),

      // Bookings Statistics
      Booking.aggregate([
        {
          $facet: {
            summary: [
              {
                $group: {
                  _id: null,
                  total: { $sum: 1 },
                  pending: { $sum: { $cond: [{ $eq: ['$booking_status', 'pending'] }, 1, 0] } },
                  approved: { $sum: { $cond: [{ $eq: ['$booking_status', 'approved'] }, 1, 0] } },
                  checkedIn: { $sum: { $cond: [{ $eq: ['$booking_status', 'checked-in'] }, 1, 0] } },
                  completed: { $sum: { $cond: [{ $eq: ['$booking_status', 'completed'] }, 1, 0] } },
                  cancelled: { $sum: { $cond: [{ $eq: ['$booking_status', 'cancelled'] }, 1, 0] } },
                  totalRevenue: { $sum: '$total_amount' }
                }
              }
            ],
            trends: [
              { $match: { createdAt: { $gte: sixMonthsAgo } } },
              {
                $group: {
                  _id: {
                    month: { $month: '$createdAt' },
                    year: { $year: '$createdAt' }
                  },
                  count: { $sum: 1 },
                  revenue: { $sum: '$total_amount' }
                }
              },
              { $sort: { '_id.year': 1, '_id.month': 1 } }
            ]
          }
        }
      ]),
      
      // Today's bookings
      Booking.find({
        booking_dates: {
          $elemMatch: {
            $gte: today,
            $lt: tomorrow
          }
        }
      }).countDocuments(),
      
      // Revenue by month (last 6 months)
      Booking.aggregate([
        {
          $match: {
            createdAt: { $gte: sixMonthsAgo },
            'payment.status': 'completed'
          }
        },
        {
          $group: {
            _id: {
              month: { $month: '$createdAt' },
              year: { $year: '$createdAt' }
            },
            revenue: { $sum: '$payment.amount' }
          }
        },
        { $sort: { '_id.year': 1, '_id.month': 1 } }
      ])
    ]);

    // Extract summary data
    const users = usersData[0]?.summary[0] || { total: 0, active: 0, inactive: 0, blocked: 0 };
    const rooms = roomsData[0]?.summary[0] || { 
      total: 0, 
      operational: 0, 
      maintenance: 0, 
      outOfService: 0,
      reserved: 0,
      occupied: 0
    };
    const bookings = bookingsData[0]?.summary[0] || {
      total: 0,
      pending: 0,
      approved: 0,
      checkedIn: 0,
      completed: 0,
      cancelled: 0,
      totalRevenue: 0
    };

    // Calculate key metrics
    const occupancyRate = rooms.total > 0
      ? Math.round(((rooms.reserved + rooms.occupied) / rooms.total) * 100)
      : 0;

    const activeBookings = bookings.approved + bookings.checkedIn;
    const bookingGrowth = bookings.total > 0
      ? ((activeBookings / bookings.total) * 100).toFixed(2)
      : 0;
      
    // Format month names for trends
    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    
    const formattedUserTrends = usersData[0]?.trends.map(item => ({
      month: `${monthNames[item._id.month - 1]} ${item._id.year}`,
      count: item.count
    })) || [];
    
    const formattedBookingTrends = bookingsData[0]?.trends.map(item => ({
      month: `${monthNames[item._id.month - 1]} ${item._id.year}`,
      count: item.count,
      revenue: item.revenue || 0
    })) || [];
    
    const formattedRevenueTrends = revenueByMonth.map(item => ({
      month: `${monthNames[item._id.month - 1]} ${item._id.year}`,
      revenue: item.revenue || 0
    })) || [];

    return res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Dashboard information retrieved successfully',
      {
        total_revenue: bookings.totalRevenue,
        active_bookings: activeBookings,
        booking_growth: parseFloat(bookingGrowth),
        occupancy_rate: occupancyRate,
        today_bookings: todayBookings,
        users: {
          total: users.total,
          active: users.active,
          inactive: users.inactive,
          blocked: users.blocked,
          trends: formattedUserTrends,
          by_role: usersData[0]?.byRole || []
        },
        rooms: {
          total: rooms.total,
          operational: rooms.operational,
          maintenance: rooms.maintenance,
          out_of_service: rooms.outOfService,
          reserved: rooms.reserved,
          occupied: rooms.occupied,
          by_floor: roomsData[0]?.byFloor || [],
          by_type: roomsData[0]?.byType || []
        },
        bookings: {
          total: bookings.total,
          pending: bookings.pending,
          approved: bookings.approved,
          checked_in: bookings.checkedIn,
          completed: bookings.completed,
          cancelled: bookings.cancelled,
          trends: formattedBookingTrends
        },
        revenue: {
          trends: formattedRevenueTrends
        }
      }
    ));

  } catch (error) {
    logger.error('Dashboard data error:', error);
    return res.status(500).json(errorResponse(2, 'SERVER SIDE ERROR', error.message || 'Error retrieving dashboard data'));
  }
};

export { getDashboardData };
