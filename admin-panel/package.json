{"name": "tts-nexus", "version": "0.0.1", "private": true, "homepage": "./", "main": "main.js", "dependencies": {"@ant-design/icons": "^5.0.1", "@fullcalendar/daygrid": "^6.1.15", "@fullcalendar/interaction": "^6.1.15", "@fullcalendar/list": "^6.1.17", "@fullcalendar/react": "^6.1.15", "@fullcalendar/timegrid": "^6.1.15", "@react-pdf/renderer": "^4.3.0", "@reduxjs/toolkit": "^1.9.1", "@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^13.5.0", "antd": "^5.1.6", "antd-img-crop": "^4.7.0", "axios": "^0.27.2", "cra-template": "1.2.0", "date-fns": "^2.30.0", "dayjs": "^1.11.13", "framer-motion": "^12.5.0", "jwt-decode": "^3.1.2", "lucide-react": "^0.477.0", "react": "^18.2.0", "react-countup": "^6.4.1", "react-dom": "^18.2.0", "react-feather": "^2.0.10", "react-redux": "^8.1.3", "react-router-dom": "^6.3.0", "react-scripts": "5.0.1", "recharts": "^2.15.1", "redux": "^4.2.1", "redux-persist": "^6.0.0", "redux-thunk": "^2.4.2", "sweetalert2": "^11.21.2", "uuid": "^9.0.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "set PORT=3033 && react-scripts start", "build": "GENERATE_SOURCEMAP=false react-scripts build", "build:win": "set \"GENERATE_SOURCEMAP=false\" && react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "electron": "electron .", "electron:dev": "concurrently \"npm start\" \"wait-on http://localhost:3033 && electron .\"", "electron:dev:debug": "set DEBUG=electron-builder && concurrently \"npm start\" \"wait-on http://localhost:3033 && electron . --trace-warnings\"", "electron:build": "npm run build && electron-builder", "electron:package": "electron-builder --dir", "electron:test": "electron electron-test.js", "electron:simple": "npm start && electron .", "verify-build": "node verify-build.js", "package:win": "set \"NODE_ENV=production\" && set \"PUBLIC_URL=./\" && npm run build:win && node verify-build.js && electron-packager . TTSNexus --overwrite --asar=false --platform=win32 --arch=x64 --icon=assets/icons/icon.ico --prune=true --out=dist --app-version=0.0.1 --build-version=0.0.1 --version-string.CompanyName=TTSNexus --version-string.FileDescription=TTSNexus --version-string.ProductName=\"TTS Nexus\"", "package:prod": "cross-env NODE_ENV=production GENERATE_SOURCEMAP=false npm run build && electron-packager . TTSNexus --overwrite --asar=true --platform=win32 --arch=x64 --icon=assets/icons/icon.ico --prune=true --out=dist --app-version=0.0.1 --build-version=0.0.1 --version-string.CompanyName=TTSNexus --version-string.FileDescription=TTSNexus --version-string.ProductName=\"TTS Nexus\""}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.7", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "electron": "^36.2.1", "electron-packager": "^17.1.2", "postcss": "^8.4.14", "prettier": "^2.7.1", "tailwindcss": "^3.1.4", "wait-on": "^8.0.3"}, "build": {"appId": "com.ttsnexus.hoteladmin", "productName": "TTS Nexus", "files": ["build/**/*", "node_modules/**/*", "main.js", "preload.js", "package.json", "assets/**/*"], "directories": {"buildResources": "assets"}, "win": {"target": "nsis", "icon": "assets/icons/icon.ico"}}}