import mongoose from 'mongoose';

const counterSchema = new mongoose.Schema({
  name: {
    type: String,
    required: true,
    unique: true
  },
  value: {
    type: Number,
    default: 0
  }
});

// Static method to get the next sequence value
counterSchema.statics.getNextSequence = async function(name) {
  try {
    const counter = await this.findOneAndUpdate(
      { name },
      { $inc: { value: 1 } },
      { new: true, upsert: true }
    );
    
    console.log(`Counter updated for ${name}: ${counter.value}`);
    return counter.value;
  } catch (error) {
    console.error(`Error updating counter for ${name}:`, error);
    throw error;
  }
};

// Static method to set a specific counter value
counterSchema.statics.setCounterValue = async function(name, value) {
  try {
    const counter = await this.findOneAndUpdate(
      { name },
      { value },
      { new: true, upsert: true }
    );
    
    console.log(`Counter ${name} set to ${counter.value}`);
    return counter.value;
  } catch (error) {
    console.error(`Error setting counter ${name}:`, error);
    throw error;
  }
};

// Initialize counter if it doesn't exist
const initializeCounter = async () => {
  try {
    const Counter = mongoose.model('Counter');
    const exists = await Counter.exists({ name: 'booking_id' });
    
    if (!exists) {
      await Counter.create({ name: 'booking_id', value: 0 });
      console.log('Booking ID counter initialized');
    }
  } catch (error) {
    console.error('Error initializing counter:', error);
  }
};

const Counter = mongoose.model('Counter', counterSchema);

// Run initialization when the model is first loaded
mongoose.connection.once('open', () => {
  initializeCounter();
});

export default Counter;
