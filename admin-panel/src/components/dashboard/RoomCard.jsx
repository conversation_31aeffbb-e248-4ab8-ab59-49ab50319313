import React, { useState } from 'react';
import { Card, Statistic, Select } from 'antd';
import { Home, Tool, Check, X } from 'react-feather';
import { 
  <PERSON><PERSON><PERSON>, Bar, 
  RadialBar<PERSON>hart, RadialBar,
  ComposedChart, Line,
  XAxis, YAxis, CartesianGrid, 
  Tooltip, ResponsiveContainer,
  Legend,
  Cell
} from 'recharts';
import { motion, AnimatePresence } from 'framer-motion';

function RoomCard({ loading, data }) {
  const [selectedChart, setSelectedChart] = useState('composed');
  const [hoveredStat, setHoveredStat] = useState(null);

  const formatter = (value) => <span>{value}</span>;

  const stats = [
    {
      title: 'Total Rooms',
      value: data?.rooms?.total || 0,
      icon: <Home size={20} />,
      color: 'purple',
      key: 'total'
    },
    {
      title: 'Operational',
      value: data?.rooms?.operational || 0,
      icon: <Check size={20} />,
      color: 'green',
      key: 'operational'
    },
    {
      title: 'Maintenance',
      value: data?.rooms?.maintenance || 0,
      icon: <Tool size={20} />,
      color: 'amber',
      key: 'maintenance'
    },
    {
      title: 'Out of Service',
      value: data?.rooms?.out_of_service || 0,
      icon: <X size={20} />,
      color: 'red',
      key: 'outOfService'
    }
  ];

  const renderChart = () => {
    switch (selectedChart) {
      case 'composed':
        return (
          <ComposedChart
            data={data?.occupancy_data || []}
            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
            <XAxis 
              dataKey="name" 
              label={{ 
                value: 'Time Period', 
                position: 'bottom', 
                offset: 0 
              }}
            />
            <YAxis
              label={{ 
                value: 'Number of Rooms', 
                angle: -90, 
                position: 'insideLeft',
                offset: -5
              }}
            />
            <Tooltip
              contentStyle={{
                background: 'rgba(255, 255, 255, 0.95)',
                borderRadius: '8px',
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
              }}
              formatter={(value, name) => [`${value} rooms`, name]}
              labelFormatter={(label) => `Period: ${label}`}
            />
            <Legend 
              verticalAlign="top" 
              height={36}
              formatter={(value) => value.charAt(0).toUpperCase() + value.slice(1)}
            />
            <Bar 
              name="Room Occupancy"
              dataKey="occupancy" 
              fill="#7c3aed" 
              radius={[4, 4, 0, 0]}
              barSize={20}
            />
            <Line
              name="Occupancy Trend"
              type="monotone"
              dataKey="trend"
              stroke="#ea580c"
              strokeWidth={2}
              dot={{ r: 4 }}
            />
          </ComposedChart>
        );

      case 'radial':
        return (
          <RadialBarChart
            width={500}
            height={300}
            cx="50%"
            cy="50%"
            innerRadius="10%"
            outerRadius="80%"
            data={stats}
            startAngle={180}
            endAngle={0}
          >
            <RadialBar
              minAngle={15}
              label={{ 
                fill: '#666', 
                position: 'insideStart',
                formatter: (value, entry) => entry?.payload?.title ? `${entry.payload.title}: ${value}` : value
              }}
              background
              clockWise={true}
              dataKey="value"
              name="Room Status"
            >
              {stats.map((entry, index) => (
                <Cell 
                  key={`cell-${index}`} 
                  fill={`var(--${entry.color}-600)`}
                />
              ))}
            </RadialBar>
            <Legend
              iconSize={10}
              width={120}
              height={140}
              layout="vertical"
              verticalAlign="middle"
              align="right"
              formatter={(value, entry) => {
                // Add null check before accessing payload
                return entry?.payload?.title || value;
              }}
            />
            <Tooltip
              contentStyle={{
                background: 'rgba(255, 255, 255, 0.95)',
                borderRadius: '8px',
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
              }}
              formatter={(value, name, props) => [
                `${value} rooms`, 
                props?.payload?.title || name
              ]}
            />
          </RadialBarChart>
        );

      case 'bar':
        return (
          <BarChart
            data={data?.occupancy_data || []}
            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
          >
            <CartesianGrid strokeDasharray="3 3" opacity={0.1} />
            <XAxis 
              dataKey="name"
              label={{ 
                value: 'Time Period', 
                position: 'bottom', 
                offset: 0 
              }}
            />
            <YAxis
              label={{ 
                value: 'Room Count', 
                angle: -90, 
                position: 'insideLeft',
                offset: -5
              }}
            />
            <Tooltip
              contentStyle={{
                background: 'rgba(255, 255, 255, 0.95)',
                borderRadius: '8px',
                boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
              }}
              formatter={(value) => [`${value} rooms`, 'Occupancy']}
              labelFormatter={(label) => `Period: ${label}`}
            />
            <Legend
              verticalAlign="top"
              height={36}
              formatter={() => 'Room Occupancy'}
            />
            <Bar
              name="Room Occupancy"
              dataKey="value"
              fill="#7c3aed"
              radius={[4, 4, 0, 0]}
              animationBegin={0}
              animationDuration={1500}
              animationEasing="ease-out"
            >
              {(data?.occupancy_data || []).map((entry, index) => (
                <Cell
                  key={`cell-${index}`}
                  fill={`hsl(${270 + index * 20}, 70%, 50%)`}
                />
              ))}
            </Bar>
          </BarChart>
        );

      default:
        return null;
    }
  };

  return (
    <Card
      title={
        <div className='flex items-center justify-between px-6 py-4'>
          <div className='flex items-center space-x-3'>
            <Home className='text-purple-600' />
            <span className='text-lg font-semibold'>Rooms Overview</span>
          </div>
          <Select
            defaultValue="composed"
            onChange={setSelectedChart}
            className='w-36'
            options={[
              { value: 'composed', label: 'Composed Chart' },
              { value: 'radial', label: 'Radial Chart' },
              { value: 'bar', label: 'Bar Chart' },
            ]}
          />
        </div>
      }
      loading={loading}
      className='shadow-lg hover:shadow-xl transition-all duration-300'
      bodyStyle={{ padding: 0 }}
    >
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-6 p-6'>
        {/* Stats Section */}
        <div className='space-y-4'>
          <div className='grid grid-cols-1 sm:grid-cols-2 gap-4'>
            {stats.map((stat) => (
              <motion.div
                key={stat.key}
                whileHover={{ scale: 1.02 }}
                whileTap={{ scale: 0.98 }}
                onHoverStart={() => setHoveredStat(stat.key)}
                onHoverEnd={() => setHoveredStat(null)}
              >
                <Card 
                  className={`bg-gradient-to-r from-${stat.color}-50 to-${stat.color}-100 
                    shadow-md hover:shadow-lg transition-all duration-300 border-none
                    ${hoveredStat === stat.key ? 'ring-2 ring-' + stat.color + '-400' : ''}`}
                >
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.3 }}
                    className='px-4 py-3'
                  >
                    <Statistic
                      title={
                        <div className='flex items-center space-x-2 text-gray-600 font-medium mb-2'>
                          <span className={`text-${stat.color}-600`}>{stat.icon}</span>
                          <span>{stat.title}</span>
                        </div>
                      }
                      value={stat.value}
                      formatter={formatter}
                      valueStyle={{ 
                        color: `var(--${stat.color}-600)`, 
                        fontSize: '24px', 
                        fontWeight: '600' 
                      }}
                    />
                  </motion.div>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>

        {/* Chart Section */}
        <motion.div
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5 }}
          className='bg-white rounded-lg shadow-md p-4'
        >
          <h3 className='text-lg font-semibold mb-4 text-gray-700'>Occupancy Rate</h3>
          <ResponsiveContainer width="100%" height={300}>
            {renderChart()}
          </ResponsiveContainer>
        </motion.div>
      </div>
    </Card>
  );
}

export default RoomCard;
