{"name": "backend", "version": "1.0.0", "type": "module", "main": "server.js", "scripts": {"start": "node server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@sendgrid/mail": "^8.1.4", "app-root-path": "^3.1.0", "bcryptjs": "^3.0.2", "body-parser": "^1.20.3", "cookie-parser": "^1.4.7", "cors": "^2.8.5", "dayjs": "^1.11.13", "dotenv": "^16.4.7", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "file-stream-rotator": "^1.0.0", "helmet": "^8.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.11.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^4.0.5", "nodemon": "^3.1.9", "serve-favicon": "^2.5.0", "validator": "^13.12.0", "winston": "^3.17.0"}}