class MyQueryHelper {
  constructor(query, queryStr) {
    this.query = query;
    this.queryStr = queryStr;
  }

  search(searchField) {
    const keyword = this.queryStr.keyword;
    if (keyword) {
      const searchObject = {
        [searchField]: {
          $regex: keyword,
          $options: 'i'
        }
      };
      this.query = this.query.find(searchObject);
    }
    return this;
  }

  filter() {
    const queryCopy = { ...this.queryStr };
    
    // Remove fields that should not be used for filtering
    const removeFields = ['keyword', 'page', 'limit', 'sort'];
    removeFields.forEach(field => delete queryCopy[field]);

    // Apply specific filters
    if (queryCopy.featured_room) {
      this.query = this.query.find({ 
        featured_room: queryCopy.featured_room === 'true' 
      });
    }

    if (queryCopy.current_status) {
      this.query = this.query.find({ 
        current_status: queryCopy.current_status 
      });
    }

    if (queryCopy.floor) {
      this.query = this.query.find({ 
        floor: parseInt(queryCopy.floor) 
      });
    }

    if (queryCopy.room_type) {
      this.query = this.query.find({ 
        room_type: queryCopy.room_type 
      });
    }

    return this;
  }

  sort() {
    if (this.queryStr.sort) {
      const sortBy = this.queryStr.sort.toLowerCase();
      if (sortBy === 'asc' || sortBy === 'desc') {
        const sortOrder = sortBy === 'desc' ? -1 : 1;
        this.query = this.query.sort({ createdAt: sortOrder });
      } else if (sortBy === 'price_asc') {
        this.query = this.query.sort({ 'room_type.base_price': 1 });
      } else if (sortBy === 'price_desc') {
        this.query = this.query.sort({ 'room_type.base_price': -1 });
      } else if (sortBy === 'room_number_asc') {
        this.query = this.query.sort({ room_number: 1 });
      } else if (sortBy === 'room_number_desc') {
        this.query = this.query.sort({ room_number: -1 });
      } else {
        // Default sort by newest
        this.query = this.query.sort({ createdAt: -1 });
      }
    } else {
      this.query = this.query.sort({ createdAt: -1 }); // Default sort by newest
    }
    return this;
  }

  paginate() {
    const page = parseInt(this.queryStr.page, 10) || 1;
    const limit = parseInt(this.queryStr.limit, 10) || 10;
    const skip = (page - 1) * limit;

    this.query = this.query.skip(skip).limit(limit);
    return this;
  }
}

export default MyQueryHelper;
