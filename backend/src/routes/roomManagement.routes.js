import express from 'express';
import { isAuthenticatedUser, isAdmin } from '../middleware/app.authentication.js';
import {
  checkRoomAvailability,
  getRoomAvailabilityCalendar,
  scheduleRoomMaintenance,
  updateMaintenanceStatus,
  markRoomAsCleaned,
  findAvailableRooms,
  getRoomBookedDates,
  updateRoomStatus,
  runStatusUpdateForCompletedBookings,
  verifyRoomCleaning,
  getRoomMaintenanceHistory,
  getRoomCleaningHistory
} from '../controllers/roomManagement.controllers.js';

const router = express.Router();

// Public routes
router.post('/rooms/check-availability', checkRoomAvailability);
router.get('/rooms/:roomId/availability/:year/:month', getRoomAvailabilityCalendar);
router.get('/rooms/:roomId/booked-dates', getRoomBookedDates);
router.post('/rooms/find-available', findAvailableRooms);

// Admin-only routes
router.post('/rooms/:roomId/maintenance', isAuthenticatedUser, isAdmin, scheduleRoomMaintenance);
router.put('/rooms/:roomId/maintenance/:maintenanceId', isAuthenticatedUser, isAdmin, updateMaintenanceStatus);
router.post('/rooms/:roomId/mark-cleaned', isAuthenticatedUser, isAdmin, markRoomAsCleaned);
router.put('/rooms/:roomId/status', isAuthenticatedUser, isAdmin, updateRoomStatus);
router.post('/rooms/update-completed-bookings', isAuthenticatedUser, isAdmin, runStatusUpdateForCompletedBookings);
router.put('/rooms/:roomId/cleaning/:cleaningId/verify', isAuthenticatedUser, isAdmin, verifyRoomCleaning);
router.get('/rooms/:roomId/maintenance-history', isAuthenticatedUser, isAdmin, getRoomMaintenanceHistory);
router.get('/rooms/:roomId/cleaning-history', isAuthenticatedUser, isAdmin, getRoomCleaningHistory);

export default router;
