import { DeleteOutlined, EditOutlined, ExclamationCircleFilled } from '@ant-design/icons';
import { Button, Modal, Table, Tag } from 'antd';
import React, { useState } from 'react';
import useFetchData from '../../hooks/useFetchData';
import ApiService from '../../utils/apiService';
import notificationWithIcon from '../../utils/notification';
import QueryOptions from '../shared/QueryOptions';

const { confirm } = Modal;

function RoomTypesList({ onEdit }) {
  const [query, setQuery] = useState({
    search: '',
    sort: 'asc',
    page: '1',
    rows: '10'
  });

  const [fetchLoading, fetchError, response] = useFetchData(
    `/api/v1/room-types?keyword=${query.search}&limit=${query.rows}&page=${query.page}&sort=${query.sort}`
  );

  const handleDelete = (id) => {
    confirm({
      title: 'Are you sure you want to delete this room type?',
      icon: <ExclamationCircleFilled />,
      content: 'This action cannot be undone.',
      okText: 'Yes',
      okType: 'danger',
      cancelText: 'No',
      onOk: async () => {
        try {
          await ApiService.delete(`/api/v1/room-types/${id}`);
          notificationWithIcon('success', 'Success', 'Room type deleted successfully');
          // Refresh the list
          window.location.reload();
        } catch (error) {
          notificationWithIcon('error', 'Error', error.message);
        }
      }
    });
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'name',
      key: 'name',
      sorter: true
    },
    {
      title: 'Base Price',
      dataIndex: 'base_price',
      key: 'base_price',
      render: (price) => `₹${price.toFixed(2)}`,
      sorter: true
    },
    {
      title: 'Room Size',
      dataIndex: ['specifications', 'room_size'],
      key: 'room_size',
      render: (size) => size ? `${size.value} ${size.unit}` : 'N/A'
    },
    {
      title: 'Capacity',
      dataIndex: 'total_capacity',
      key: 'total_capacity',
      render: (_, record) => (
        `${record.specifications?.max_occupancy?.adults || 0} Adults, ${record.specifications?.max_occupancy?.children || 0} Children`
      )
    },
    {
      title: 'Amenities',
      dataIndex: ['amenities', 'basic'],
      key: 'amenities',
      render: (amenities) => (
        <div className="flex flex-wrap gap-1">
          {amenities?.map((amenity, index) => (
            <Tag key={`${amenity}-${index}`} color="blue">
              {amenity}
            </Tag>
          ))}
        </div>
      )
    },
    {
      title: 'Status',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (active) => (
        <Tag color={active ? 'green' : 'red'}>
          {active ? 'Active' : 'Inactive'}
        </Tag>
      )
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <div className="flex gap-2">
          <Button
            type="primary"
            icon={<EditOutlined />}
            onClick={() => onEdit(record._id)}
          >
            View/Edit
          </Button>
          <Button
            type="primary"
            danger
            icon={<DeleteOutlined />}
            onClick={() => handleDelete(record._id)}
          >
            Delete
          </Button>
        </div>
      )
    }
  ];

  return (
    <div className="space-y-4">
      <QueryOptions
        query={query}
        setQuery={setQuery}
        searchPlaceholder="Search room types..."
      />
      <Table
        columns={columns}
        dataSource={response?.data || []}
        loading={fetchLoading}
        rowKey="_id"
        pagination={{
          current: parseInt(query.page),
          pageSize: parseInt(query.rows),
          total: response?.total || 0,
          onChange: (page, pageSize) => {
            setQuery({ ...query, page: page.toString(), rows: pageSize.toString() });
          }
        }}
      />
    </div>
  );
}

export default RoomTypesList;