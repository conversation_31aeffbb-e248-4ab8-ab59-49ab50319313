import mongoose from 'mongoose';

const maintenanceSchema = new mongoose.Schema({
  room_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Room',
    required: true
  },
  start_date: {
    type: Date,
    required: true
  },
  end_date: {
    type: Date,
    required: true
  },
  reason: {
    type: String,
    required: true
  },
  status: {
    type: String,
    enum: ['scheduled', 'in_progress', 'completed', 'cancelled'],
    default: 'scheduled'
  },
  performed_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  assigned_to: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }],
  notes: String,
  cost: {
    amount: Number,
    currency: {
      type: String,
      default: 'USD'
    }
  },
  parts_replaced: [{
    name: String,
    quantity: Number,
    cost: Number
  }],
  images: [{
    url: String,
    caption: String,
    uploaded_at: {
      type: Date,
      default: Date.now
    }
  }],
  completion_report: {
    summary: String,
    completed_at: Date,
    verified_by: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    }
  }
}, {
  timestamps: true
});

// Validate dates
maintenanceSchema.pre('save', function(next) {
  if (this.end_date < this.start_date) {
    return next(new Error('End date cannot be before start date'));
  }
  
  next();
});

// Method to start maintenance
maintenanceSchema.methods.startMaintenance = async function(userId) {
  if (this.status !== 'scheduled') {
    throw new Error('Only scheduled maintenance can be started');
  }
  
  this.status = 'in_progress';
  
  // Update room status
  const Room = mongoose.model('Room');
  const room = await Room.findById(this.room_id);
  if (room) {
    room.current_status = 'maintenance';
    await room.save();
  }
  
  return this.save();
};

// Method to complete maintenance
maintenanceSchema.methods.completeMaintenance = async function(summary, userId) {
  if (this.status !== 'in_progress') {
    throw new Error('Only in-progress maintenance can be completed');
  }
  
  this.status = 'completed';
  this.completion_report = {
    summary: summary || 'Maintenance completed',
    completed_at: new Date(),
    verified_by: userId
  };
  
  // Update room status
  const Room = mongoose.model('Room');
  const room = await Room.findById(this.room_id);
  if (room) {
    room.current_status = 'available';
    await room.save();
  }
  
  return this.save();
};

// Method to cancel maintenance
maintenanceSchema.methods.cancelMaintenance = async function(reason) {
  if (this.status === 'completed') {
    throw new Error('Completed maintenance cannot be cancelled');
  }
  
  this.status = 'cancelled';
  this.notes = this.notes ? `${this.notes}\nCancelled: ${reason}` : `Cancelled: ${reason}`;
  
  // Update room status if needed
  if (this.status === 'in_progress') {
    const Room = mongoose.model('Room');
    const room = await Room.findById(this.room_id);
    if (room && room.current_status === 'maintenance') {
      room.current_status = 'available';
      await room.save();
    }
  }
  
  return this.save();
};

// Add indexes for faster queries
maintenanceSchema.index({ room_id: 1 });
maintenanceSchema.index({ status: 1 });
maintenanceSchema.index({ start_date: 1 });
maintenanceSchema.index({ end_date: 1 });
maintenanceSchema.index({ performed_by: 1 });
maintenanceSchema.index({ 'assigned_to': 1 });

const Maintenance = mongoose.model('Maintenance', maintenanceSchema);

export default Maintenance;