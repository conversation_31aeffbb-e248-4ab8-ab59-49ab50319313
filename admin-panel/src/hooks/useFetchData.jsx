import { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import ApiService from '../utils/apiService';

const useFetchData = (url, fetchAgain) => {
  const reFetch = useSelector((state) => state.app.reFetch);
  const [response, setResponse] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Reset states when URL changes
    setResponse(null);
    setError(null);

    // Don't make API call if URL is null
    if (!url) {
      setLoading(false);
      return;
    }

    setLoading(true);
    ApiService.get(url)
      .then((res) => {
        if (res?.result_code === 0) {
          setResponse(res?.result);
        } else {
          setError(res?.result?.error || 'Sorry! Something went wrong. App server error');
        }
      })
      .catch((err) => {
        setError(err?.response?.data?.result?.error || 'Sorry! Something went wrong. App server error');
      })
      .finally(() => {
        setLoading(false);
      });
  }, [url, fetchAgain, reFetch]);

  return [loading, error, response];
};

export default useFetchData;
