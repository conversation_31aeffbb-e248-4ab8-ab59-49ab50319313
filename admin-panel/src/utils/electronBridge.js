// Utility to communicate with Electron main process
const electronBridge = {
  // Send message to main process
  sendToMain: (data) => {
    if (window.electron) {
      window.electron.send('toMain', data);
    } else {
      console.warn('Electron API not available');
    }
  },
  
  // Register listener for messages from main process
  onMessageFromMain: (callback) => {
    if (window.electron) {
      window.electron.receive('fromMain', callback);
    } else {
      console.warn('Electron API not available');
    }
  }
};

export default electronBridge;