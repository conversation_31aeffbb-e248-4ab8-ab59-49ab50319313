import React from 'react';
import { useNavigate } from 'react-router-dom';
import Avatar from '../../assets/images/avatar.png';
import { getSessionUser } from '../../utils/authentication';

function UserBox() {
  const user = getSessionUser();
  const navigate = useNavigate();

  return (
    <div
      className='flex items-center space-x-3 cursor-pointer text-gray-100 hover:text-blue-500 hover:bg-gray-100 p-2 rounded-lg transition-all duration-200'
      onClick={() => navigate('/main/profile')}
      role='button'
      tabIndex={0}
      onKeyDown={(e) => e.key === 'Enter' && navigate('/main/profile')}
    >
      <img
        className='w-10 h-10 rounded-full object-cover border-2 border-gray-200'
        src={user?.avatar || Avatar}
        crossOrigin='anonymous'
        alt='avatar'
      />
      <div className='flex flex-col '>
        <span className='font-semibold text-sm truncate '>
          {user?.fullName}
        </span>
        <span className='text-xs'>
          {user?.role || 'User'}
        </span>
      </div>
    </div>
  );
}

export default UserBox;
