// imports modules & dependencies
import express from 'express';
import favicon from 'serve-favicon';
import crossOrigin from 'cors';
import cookieParser from 'cookie-parser';
import appRoot from 'app-root-path';
import bodyParser from 'body-parser';
import helmet from 'helmet';
import env from 'dotenv';

// imports application middleware and routes
import morganLogger from '../middleware/morgan.logger.js';
import defaultController from '../controllers/default.controllers.js';
import {notFoundRoute,errorHandler} from '../middleware/error.handler.js';
import { limiter} from '../middleware/access.limiter.js';
import corsOptions from '../config/cors.config.js';
import authRoute from '../routes/auth.routes.js';
import userRoute from '../routes/user.routes.js';
import appsRoute from '../routes/apps.routes.js';
import roomRoute from '../routes/room.routes.js';
import roomTypeRoute from '../routes/roomType.routes.js';
import bookingRoute from '../routes/booking.routes.js';
import reviewRoute from '../routes/review.routes.js';
import roomManagementRoute from '../routes/roomManagement.routes.js';
import reportsRoute from '../routes/reports.routes.js';

// load environment variables from .env file
env.config();

// initialize express app
const app = express();

// Enable trust proxy - this is required when the app is behind a reverse proxy
// This allows Express to trust the X-Forwarded-For header for client IP addresses
app.set('trust proxy', false);

// limiting middleware to all requests
app.use(limiter);

// application database connection establishment
import connectDatabase from '../database/connect.mongodb.js';

connectDatabase();

// HTTP request logger middleware
if (process.env.APP_NODE_ENV !== 'production') {
  app.use(morganLogger());
}

// secure HTTP headers setting middleware
app.use(helmet.crossOriginResourcePolicy({ policy: 'cross-origin' }));

// allow cross-origin resource sharing
app.use(crossOrigin(corsOptions));

// parse cookies from request
app.use(cookieParser());

// parse body of request
app.use(bodyParser.json());
app.use(bodyParser.urlencoded({ extended: true }));

// sets favicon in API routes
if (process.env.APP_NODE_ENV !== 'production') {
  app.use(favicon(`${appRoot}/public/favicon.ico`));
}

// sets static folder
app.use(express.static('public'));

// parse requests of content-type ~ application/json
app.use(express.json());

// parse requests of content-type ~ application/x-www-form-urlencoded
app.use(express.urlencoded({ extended: true }));

// response default (welcome) route
app.get('/', defaultController);

// sets application API's routes
app.use('/api/v1', authRoute); // auth routes
app.use('/api/v1', userRoute); // user routes
app.use('/api/v1', appsRoute); // apps routes
app.use('/api/v1', roomRoute); // room routes
app.use('/api/v1', roomTypeRoute); // room type routes
app.use('/api/v1', bookingRoute); // booking routes
app.use('/api/v1', reviewRoute); // review routes
app.use('/api/v1', roomManagementRoute); // room management routes
app.use('/api/v1', reportsRoute); // reports routes

// 404 ~ not found error handler
app.use(notFoundRoute);

// 500 ~ internal server error handler
app.use(errorHandler);

// default export ~ app
export default app;
