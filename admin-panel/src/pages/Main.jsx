import {
  DashboardOutlined, FileProtectOutlined, FullscreenExitOutlined, FullscreenOutlined,
  HomeOutlined, LogoutOutlined, TeamOutlined, UserOutlined, CalendarOutlined,
  AppstoreOutlined, KeyOutlined, BarChartOutlined
} from '@ant-design/icons';
import {
  Button,
  Layout,
  Menu,
  Tooltip
} from 'antd';
import React, { useEffect, useState } from 'react';
import { Link, useNavigate, useParams } from 'react-router-dom';
import Logo from '../assets/images/tts2.png';
import UserBox from '../components/shared/UserBox';
import Dashboard from '../components/tabs/Dashboard';
import MyProfile from '../components/tabs/MyProfile';
import Orders from '../components/tabs/Orders';
import Rooms from '../components/tabs/Rooms';
import Users from '../components/tabs/Users';
import BookRoom from '../components/tabs/BookRoom';
import BookingCalendar from '../components/tabs/BookingCalendar';
import RoomTypes from '../components/rooms/RoomTypes';
import useFullScreen from '../hooks/useFullScreen';
import RoomManagement from '../components/tabs/RoomManagement';
import ApiService from '../utils/apiService';
import { removeSessionAndLogoutUser } from '../utils/authentication';
import notificationWithIcon from '../utils/notification';
import AvailableRooms from '../components/tabs/AvailableRooms';
import CreateRoom from '../components/rooms/CreateRoom';
import BookingManagement from '../components/tabs/BookingManagement';
import Reports from '../components/tabs/Reports';

const {
  Header, Content, Footer, Sider
} = Layout;

function Main() {
  window.document.title = 'TTS Nexus — Main';
  const { isFullscreen, toggleFullScreen } = useFullScreen();
  const [selectedKeys, setSelectedKeys] = useState('1');
  const navigate = useNavigate();
  const { tab } = useParams();

  // function to handle user logout
  const userLogout = async () => {
    try {
      const response = await ApiService.post('/api/v1/auth/logout');
      if (response?.result_code === 0) {
        removeSessionAndLogoutUser();
      } else {
        notificationWithIcon('error', 'ERROR', 'Sorry! Something went wrong. App server error');
        removeSessionAndLogoutUser();
      }
    } catch (error) {
      notificationWithIcon('error', 'ERROR', error?.response?.data?.result?.error || 'Sorry! Something went wrong. App server error');
      removeSessionAndLogoutUser();
    }
  };

  const handleTabChange = (key) => {
    switch (key) {
      case '1': {
        navigate('/main/dashboard');
        break;
      }
      case '2': {
        navigate('/main/users');
        break;
      }
      case '3': {
        navigate('/main/hotel-rooms');
        break;
      }
      case '4': {
        navigate('/main/booking-orders');
        break;
      }
      case '5': {
        navigate('/main/profile');
        break;
      }
      case '6': {
        navigate('/main/book-room');
        break;
      }
      case '7': {
        navigate('/main/booking-calendar');
        break;
      }
      case '8': {
        userLogout();
        break;
      }
      case '9': {
        navigate('/main/room-types');
        break;
      }
      case '10': {
        navigate('/main/room-management');
        break;
      }
      case '11': {
        navigate('/main/available-rooms');
        break;
      }
      case '12': {
        navigate('/main/booking-management');
        break;
      }
      case '13': {
        navigate('/main/reports');
        break;
      }
      case 'create-room': {
        navigate('/main/create-room');
        break;
      }
      default: {
        navigate('/main/dashboard');
      }
    }
  };

  useEffect(() => {
    if (tab) {
      switch (tab) {
        case 'dashboard': {
          setSelectedKeys('1');
          break;
        }
        case 'users': {
          setSelectedKeys('2');
          break;
        }
        case 'hotel-rooms': {
          setSelectedKeys('3');
          break;
        }
        case 'booking-orders': {
          setSelectedKeys('4');
          break;
        }
        case 'profile': {
          setSelectedKeys('5');
          break;
        }
        case 'book-room': {
          setSelectedKeys('6');
          break;
        }
        case 'booking-calendar': {
          setSelectedKeys('7');
          break;
        }
        case 'logout': {
          setSelectedKeys('8');
          break;
        }
        case 'room-types': {
          setSelectedKeys('9');
          break;
        }
        case 'room-management': {
          setSelectedKeys('10');
          break;
        }
        case 'available-rooms': {
          setSelectedKeys('11');
          break;
        }
        case 'booking-management': {
          setSelectedKeys('12');
          break;
        }
        case 'reports': {
          setSelectedKeys('13');
          break;
        }
        case 'create-room': {
          window.document.title = 'TTS Nexus — Create Room';
          break;
        }
        default: {
          navigate('/not-found');
        }
      }
    }
  }, [tab, navigate]);

  useEffect(() => {
    switch (selectedKeys) {
      case '1': {
        window.document.title = 'TTS Nexus — Dashboard';
        break;
      }
      case '2': {
        window.document.title = 'TTS Nexus — Users';
        break;
      }
      case '3': {
        window.document.title = 'TTS Nexus — Hotel Rooms';
        break;
      }
      case '4': {
        window.document.title = 'TTS Nexus — Booking Orders';
        break;
      }
      case '5': {
        window.document.title = 'TTS Nexus — Profile';
        break;
      }
      case '6': {
        window.document.title = 'TTS Nexus — Book Room';
        break;
      }
      case '7': {
        window.document.title = 'TTS Nexus — Booking Calendar';
        break;
      }
      case '8': {
        window.document.title = 'TTS Nexus — Logout';
        break;
      }
      case '9': {
        window.document.title = 'TTS Nexus — Room Types';
        break;
      }
      case '10': {
        window.document.title = 'TTS Nexus — Room Management';
        break;
      }
      case '11': {
        window.document.title = 'TTS Nexus — Available Rooms';
        break;
      }
      case '12': {
        window.document.title = 'TTS Nexus — Booking Management';
        break;
      }
      case '13': {
        window.document.title = 'TTS Nexus — Reports';
        break;
      }
      default: {
        window.document.title = 'TTS Nexus — Dashboard';
      }
    }
  }, [selectedKeys]);

  return (
    <Layout className='w-full h-screen'>
      <Sider width={250} breakpoint='lg' collapsedWidth='0'>
        <UserBox />

        <div className="flex flex-col h-[calc(100vh-64px)]">
          <Menu
            theme='dark'
            mode='inline'
            selectedKeys={[selectedKeys]}
            onClick={(e) => {
              handleTabChange(e.key);
            }}
            items={[
              // Dashboard - Overview
              {
                key: '1',
                icon: <DashboardOutlined />,
                label: 'Dashboard'
              },

              // Room Management Group
              {
                key: '3',
                icon: <HomeOutlined />,
                label: 'Hotel Rooms'
              },
              {
                key: '9',
                icon: <AppstoreOutlined />,
                label: 'Room Types'
              },
              {
                key: '11',
                icon: <HomeOutlined />,
                label: 'Available Rooms'
              },
              {
                key: '10',
                icon: <KeyOutlined />,
                label: 'Room Management'
              },

              // Booking Management Group
              {
                key: '6',
                icon: <CalendarOutlined />,
                label: 'Book Room'
              },
              {
                key: '4',
                icon: <FileProtectOutlined />,
                label: 'Booking Orders'
              },
              {
                key: '12',
                icon: <FileProtectOutlined />,
                label: 'Booking Management'
              },
              {
                key: '7',
                icon: <CalendarOutlined />,
                label: 'Booking Calendar'
              },

              // User Management Group
              {
                key: '2',
                icon: <TeamOutlined />,
                label: 'Users'
              },
              {
                key: '5',
                icon: <UserOutlined />,
                label: 'My Profile'
              },

              // Reports Group
              {
                key: '13',
                icon: <BarChartOutlined />,
                label: 'Reports & Analytics'
              }
            ]}
          />
        </div>
      </Sider>

      <Layout>
        <Header className='bg-white p-0 flex justify-between items-center'>
          <div className='flex items-center'>
            <img src={Logo} alt='Logo' className='h-16 ml-4' />
          </div>
          <div className='flex items-center mr-4'>
            <Tooltip title={isFullscreen ? 'Exit Fullscreen' : 'Enter Fullscreen'}>
              <Button
                type='text'
                icon={isFullscreen ? <FullscreenExitOutlined /> : <FullscreenOutlined />}
                onClick={toggleFullScreen}
              />
            </Tooltip>
            <Tooltip title='Logout'>
              <Button
                type='text'
                icon={<LogoutOutlined />}
                onClick={() => userLogout()}
              />
            </Tooltip>
          </div>
        </Header>

        <Content className='bg-bg-white overflow-y-scroll m-2 p-2'>
          {selectedKeys === '1' && (<Dashboard />)}
          {selectedKeys === '2' && (<Users />)}
          {selectedKeys === '3' && (<Rooms />)}
          {selectedKeys === '4' && (<Orders />)}
          {selectedKeys === '5' && (<MyProfile />)}
          {selectedKeys === '6' && (<BookRoom />)}
          {selectedKeys === '7' && (<BookingCalendar />)}
          {selectedKeys === '9' && (<RoomTypes />)}
          {selectedKeys === '10' && (<RoomManagement />)}
          {selectedKeys === '11' && (<AvailableRooms />)}
          {selectedKeys === '12' && (<BookingManagement />)}
          {selectedKeys === '13' && (<Reports />)}
          {tab === 'create-room' && <CreateRoom />}
        </Content>

        <Footer className='text-center'>
          TTS Nexus ©{new Date().getFullYear()} Created by TTS
        </Footer>
      </Layout>
    </Layout>
  );
}

export default React.memo(Main);
