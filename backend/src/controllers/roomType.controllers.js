import RoomType from '../models/roomType.model.js';
import { errorResponse, successResponse } from '../config/app.response.js';
import mongoose from 'mongoose';

// Create room type controller
const createRoomType = async (req, res) => {
  try {
    const {
      // Basic Information
      name,
      description,
      base_price,
      is_active,

      // Specifications
      specifications,

      // Amenities
      amenities,

      // Policies
      policies,

      // Pricing
      pricing,

      // Maintenance
      maintenance
    } = req.body;

    // Validation for required fields
    if (!name || !description || !base_price) {
      return res.status(400).json(errorResponse(
        1,
        'FAILED',
        'Name, description, and base price are required'
      ));
    }

    // Validate specifications
    if (!specifications?.room_size?.value || 
        !specifications?.max_occupancy?.adults || 
        !specifications?.max_occupancy?.children ||
        !specifications?.bed_configuration?.length) {
      return res.status(400).json(errorResponse(
        1,
        'FAILED',
        'Room specifications are incomplete'
      ));
    }

    // Create room type data object
    const roomTypeData = {
      name,
      description,
      base_price,
      is_active: is_active ?? true,
      created_by: req.user._id,

      // Specifications
      specifications: {
        room_size: {
          value: specifications.room_size.value,
          unit: specifications.room_size.unit || 'sqft'
        },
        max_occupancy: {
          adults: specifications.max_occupancy.adults,
          children: specifications.max_occupancy.children
        },
        bed_configuration: specifications.bed_configuration.map(bed => ({
          bed_type: bed.bed_type,
          count: bed.count
        }))
      },

      // Amenities
      amenities: {
        basic: amenities?.basic || [],
        additional: amenities?.additional?.map(item => ({
          name: item.name,
          description: item.description,
          icon_url: item.icon_url
        })) || []
      },

      // Policies
      policies: {
        allow_pets: policies?.allow_pets ?? false,
        provide_breakfast: policies?.provide_breakfast ?? false,
        smoking_allowed: policies?.smoking_allowed ?? false,
        cancellation_policy: {
          type: policies?.cancellation_policy?.type || 'moderate',
          hours_before: policies?.cancellation_policy?.hours_before || 24,
          refund_percentage: policies?.cancellation_policy?.refund_percentage || 80
        },
        check_in_time: policies?.check_in_time || "14:00",
        check_out_time: policies?.check_out_time || "12:00"
      },

      // Pricing
      pricing: {
        hourly_rate: pricing?.hourly_rate || 0,
        daily_rate: pricing?.daily_rate || base_price,
        weekly_discount: pricing?.weekly_discount || 0,
        monthly_discount: pricing?.monthly_discount || 0,
        cleaning_fee: pricing?.cleaning_fee || 0,
        security_deposit: pricing?.security_deposit || 0
      },

      // Maintenance
      maintenance: {
        last_renovation: maintenance?.last_renovation || null,
        next_maintenance: maintenance?.next_maintenance || null,
        maintenance_notes: maintenance?.maintenance_notes || ''
      }
    };

    // Create room type
    const roomType = await RoomType.create(roomTypeData);

    // Populate created_by field
    const populatedRoomType = await RoomType.findById(roomType._id)
      .populate('created_by', 'userName fullName email role');

    res.status(201).json(successResponse(
      0,
      'SUCCESS',
      'Room type created successfully',
      populatedRoomType
    ));

  } catch (error) {
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

// Update room type controller
const updateRoomType = async (req, res) => {
  try {
    const roomTypeId = req.params.id;
    const {
      name,
      description,
      base_price,
      is_active,
      specifications,
      amenities,
      policies,
      pricing,
      maintenance
    } = req.body;

    // Check if room type exists
    const existingRoomType = await RoomType.findById(roomTypeId);
    if (!existingRoomType) {
      return res.status(404).json(errorResponse(
        4,
        'UNKNOWN ACCESS',
        'Room type not found'
      ));
    }

    // Prepare update data with proper schema validation
    const updateData = {
      // Basic fields
      ...(name && { name }),
      ...(description && { description }),
      ...(typeof base_price !== 'undefined' && { base_price }),
      ...(typeof is_active !== 'undefined' && { is_active }),

      // Specifications
      ...(specifications && {
        specifications: {
          ...(specifications.room_size && {
            room_size: {
              ...existingRoomType.specifications.room_size,
              ...(specifications.room_size.value && { value: specifications.room_size.value }),
              ...(specifications.room_size.unit && { unit: specifications.room_size.unit })
            }
          }),
          ...(specifications.max_occupancy && {
            max_occupancy: {
              ...existingRoomType.specifications.max_occupancy,
              ...(typeof specifications.max_occupancy.adults !== 'undefined' && { adults: specifications.max_occupancy.adults }),
              ...(typeof specifications.max_occupancy.children !== 'undefined' && { children: specifications.max_occupancy.children })
            }
          }),
          ...(specifications.bed_configuration && {
            bed_configuration: specifications.bed_configuration.map(bed => ({
              bed_type: bed.bed_type,
              count: bed.count
            }))
          })
        }
      }),

      // Amenities
      ...(amenities && {
        amenities: {
          ...(Array.isArray(amenities.basic) && { basic: amenities.basic }),
          ...(Array.isArray(amenities.additional) && {
            additional: amenities.additional.map(item => ({
              name: item.name,
              description: item.description,
              icon_url: item.icon_url
            }))
          })
        }
      }),

      // Policies
      ...(policies && {
        policies: {
          ...(typeof policies.allow_pets !== 'undefined' && { allow_pets: policies.allow_pets }),
          ...(typeof policies.provide_breakfast !== 'undefined' && { provide_breakfast: policies.provide_breakfast }),
          ...(typeof policies.smoking_allowed !== 'undefined' && { smoking_allowed: policies.smoking_allowed }),
          ...(policies.cancellation_policy && {
            cancellation_policy: {
              ...existingRoomType.policies.cancellation_policy,
              ...(policies.cancellation_policy.type && { type: policies.cancellation_policy.type }),
              ...(typeof policies.cancellation_policy.hours_before !== 'undefined' && { hours_before: policies.cancellation_policy.hours_before }),
              ...(typeof policies.cancellation_policy.refund_percentage !== 'undefined' && { refund_percentage: policies.cancellation_policy.refund_percentage })
            }
          }),
          ...(policies.check_in_time && { check_in_time: policies.check_in_time }),
          ...(policies.check_out_time && { check_out_time: policies.check_out_time })
        }
      }),

      // Pricing
      ...(pricing && {
        pricing: {
          ...existingRoomType.pricing,
          ...(typeof pricing.hourly_rate !== 'undefined' && { hourly_rate: pricing.hourly_rate }),
          ...(typeof pricing.daily_rate !== 'undefined' && { daily_rate: pricing.daily_rate }),
          ...(typeof pricing.weekly_discount !== 'undefined' && { weekly_discount: pricing.weekly_discount }),
          ...(typeof pricing.monthly_discount !== 'undefined' && { monthly_discount: pricing.monthly_discount }),
          ...(typeof pricing.cleaning_fee !== 'undefined' && { cleaning_fee: pricing.cleaning_fee }),
          ...(typeof pricing.security_deposit !== 'undefined' && { security_deposit: pricing.security_deposit })
        }
      }),

      // Maintenance
      ...(maintenance && {
        maintenance: {
          ...(maintenance.last_renovation && { last_renovation: new Date(maintenance.last_renovation) }),
          ...(maintenance.next_maintenance && { next_maintenance: new Date(maintenance.next_maintenance) }),
          ...(maintenance.maintenance_notes && { maintenance_notes: maintenance.maintenance_notes })
        }
      })
    };

    // Update room type with validation
    const updatedRoomType = await RoomType.findByIdAndUpdate(
      roomTypeId,
      updateData,
      { 
        new: true, 
        runValidators: true,
        context: 'query'
      }
    ).populate('created_by', 'userName fullName email role');

    if (!updatedRoomType) {
      return res.status(404).json(errorResponse(
        4,
        'UNKNOWN ACCESS',
        'Room type not found'
      ));
    }

    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Room type updated successfully',
      updatedRoomType
    ));

  } catch (error) {
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

// Get room type list controller
const getRoomTypeList = async (req, res) => {
  try {
    const roomTypes = await RoomType.find()
      .populate('created_by', 'userName fullName email role')
      .sort('-createdAt');

    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Room types retrieved successfully',
      roomTypes
    ));

  } catch (error) {
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

// Get single room type controller
const getRoomType = async (req, res) => {
  try {
    const roomType = await RoomType.findById(req.params.id)
      .populate('created_by', 'userName fullName email role');

    if (!roomType) {
      return res.status(404).json(errorResponse(
        4,
        'UNKNOWN ACCESS',
        'Room type not found'
      ));
    }

    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Room type retrieved successfully',
      roomType
    ));

  } catch (error) {
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};
// Delete room type
const deleteRoomType = async (req, res) => {
  try {
    const roomType = await RoomType.findById(req.params.id);

    if (!roomType) {
      return res.status(404).json(errorResponse(
        4,
        'UNKNOWN ACCESS',
        'Room type not found'
      ));
    }

    // Check if any rooms are using this room type
    const Room = mongoose.model('Room');
    const roomsUsingType = await Room.countDocuments({ room_type: roomType._id });
    
    if (roomsUsingType > 0) {
      return res.status(400).json(errorResponse(
        1,
        'FAILED',
        'Cannot delete room type that is in use by existing rooms'
      ));
    }

    await RoomType.findByIdAndDelete(roomType._id);

    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Room type deleted successfully'
    ));
  } catch (error) {
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

export {
  createRoomType,
  updateRoomType,
  getRoomTypeList,
  getRoomType,
  deleteRoomType
};
