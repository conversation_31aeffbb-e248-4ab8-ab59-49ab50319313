import Room from '../models/room.model.js';
import Booking from '../models/booking.model.js';
import logger from '../middleware/winston.logger.js';

/**
 * Updates room statuses for completed bookings
 * @returns {Promise<Object>} Results of the update operation
 */
export const updateRoomStatusForCompletedBookings = async () => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    // Find checked-in bookings where the last booking date has passed
    const expiredBookings = await Booking.find({
      booking_status: 'checked-in',
      booking_dates: {
        $lt: today // All booking dates are before today
      }
    }).populate('room_id');
    
    let updatedCount = 0;
    
    // Update each room status to available
    for (const booking of expiredBookings) {
      await Room.findByIdAndUpdate(booking.room_id._id, {
        current_status: 'available'
      });
      
      // Update booking status to completed
      await Booking.findByIdAndUpdate(booking._id, {
        booking_status: 'completed'
      });
      
      updatedCount++;
    }
    
    logger.info(`Updated ${updatedCount} rooms to available status after booking completion`);
    return { success: true, updatedCount };
  } catch (error) {
    logger.error('Error updating room statuses for completed bookings:', error);
    return { success: false, error: error.message };
  }
};