const { app, BrowserWindow, protocol } = require('electron');
const path = require('path');
const fs = require('fs');
const url = require('url');
const { URL } = require('url');

// Add this function to check file existence
function fileExists(filePath) {
  try {
    return fs.statSync(filePath).isFile();
  } catch (err) {
    return false;
  }
}

// Add this function to check directory existence
function dirExists(dirPath) {
  try {
    return fs.statSync(dirPath).isDirectory();
  } catch (err) {
    return false;
  }
}

// Keep a global reference of the window object
let mainWindow;

// Enable logging
console.log('Starting Electron application...');
console.log('App path:', app.getAppPath());
console.log('Current working directory:', process.cwd());

function createWindow() {
  console.log('Creating main window...');
  
  // Register protocol handler for serving local files
  protocol.registerFileProtocol('app', (request, callback) => {
    const url = request.url.substring(6);
    callback({ path: path.normalize(`${__dirname}/${url}`) });
  });
  
  // Determine the correct icon path
  const iconPath = path.join(__dirname, 'assets', 'icons', 'icon.ico');
  const iconExists = fileExists(iconPath);
  console.log(`Icon path: ${iconPath}, exists: ${iconExists}`);
  
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: false // Disable web security to allow loading local files
    },
    icon: iconExists ? iconPath : undefined
  });

  // Production - load from build folder
  const buildPath = path.join(__dirname, 'build');
  const indexPath = path.join(buildPath, 'index.html');
  
  console.log(`Build directory: ${buildPath}, exists: ${dirExists(buildPath)}`);
  console.log(`Loading production build from: ${indexPath}, exists: ${fileExists(indexPath)}`);
  
  // List files in the directory to help debug
  try {
    const files = fs.readdirSync(__dirname);
    console.log('Files in app directory:', files);
    
    if (dirExists(buildPath)) {
      const buildFiles = fs.readdirSync(buildPath);
      console.log('Files in build directory:', buildFiles);
    }
  } catch (err) {
    console.error('Error listing directory contents:', err);
  }
  
  if (!fileExists(indexPath)) {
    console.error(`Error: Build index file not found at ${indexPath}`);
    app.quit();
    return;
  }
  
  // Load the index.html using file URL format
  const fileUrl = url.format({
    pathname: indexPath,
    protocol: 'file:',
    slashes: true
  });
  
  console.log(`Loading URL: ${fileUrl}`);
  mainWindow.loadURL(fileUrl)
    .catch(err => {
      console.error('Failed to load production build:', err);
    });

  // Handle navigation events to prevent 404 errors with SPA routing
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
    console.log(`Failed to load: ${validatedURL} - ${errorDescription}`);
    // If the page fails to load, try loading the index.html again
    if (errorCode !== -3) { // Ignore ERR_ABORTED errors
      mainWindow.loadURL(fileUrl);
    }
  });

  // Prevent navigation away from the app
  mainWindow.webContents.on('will-navigate', (event, navigationUrl) => {
    const parsedUrl = new URL(navigationUrl);
    const parsedFileUrl = new URL(fileUrl);
    
    // Only allow navigation within the app
    if (parsedUrl.protocol !== 'file:' || !parsedUrl.pathname.startsWith(parsedFileUrl.pathname.replace(/\/index\.html$/, ''))) {
      console.log(`Preventing navigation to: ${navigationUrl}`);
      event.preventDefault();
      // Redirect back to the app
      mainWindow.loadURL(fileUrl);
    }
  });

  mainWindow.on('closed', () => {
    console.log('Main window closed');
    mainWindow = null;
  });
}

app.whenReady().then(() => {
  console.log('Electron app ready');
  createWindow();
});

app.on('window-all-closed', () => {
  console.log('All windows closed');
  if (process.platform !== 'darwin') {
    console.log('Quitting app');
    app.quit();
  }
});

app.on('activate', () => {
  console.log('App activated');
  if (mainWindow === null) {
    createWindow();
  }
});

// Handle any uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught exception:', error);
});
