import Booking from '../models/booking.model.js';
import Cleaning from '../models/cleaning.model.js';
import Maintenance from '../models/maintenance.model.js';
import Room from '../models/room.model.js';
import { successResponse, errorResponse } from '../config/app.response.js';
import logger from '../middleware/winston.logger.js';

/**
 * Get monthly bookings report
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getMonthlyBookingsReport = async (req, res) => {
  try {
    const { year, month, startDate, endDate } = req.query;
    
    let matchCondition = {};
    
    if (year && month) {
      // Specific month and year
      const startOfMonth = new Date(year, month - 1, 1);
      const endOfMonth = new Date(year, month, 0, 23, 59, 59, 999);
      matchCondition.createdAt = {
        $gte: startOfMonth,
        $lte: endOfMonth
      };
    } else if (startDate && endDate) {
      // Custom date range
      matchCondition.createdAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    } else {
      // Default to current month
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
      matchCondition.createdAt = {
        $gte: startOfMonth,
        $lte: endOfMonth
      };
    }

    // Get bookings with aggregation
    const bookingsReport = await Booking.aggregate([
      { $match: matchCondition },
      {
        $lookup: {
          from: 'rooms',
          localField: 'room_id',
          foreignField: '_id',
          as: 'room'
        }
      },
      {
        $lookup: {
          from: 'roomtypes',
          localField: 'room_type',
          foreignField: '_id',
          as: 'roomType'
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'user'
        }
      },
      {
        $addFields: {
          room: { $arrayElemAt: ['$room', 0] },
          roomType: { $arrayElemAt: ['$roomType', 0] },
          user: { $arrayElemAt: ['$user', 0] }
        }
      },
      {
        $project: {
          booking_id: 1,
          booking_status: 1,
          booking_dates: 1,
          total_amount: 1,
          'payment.status': 1,
          'payment.amount': 1,
          'payment.method': 1,
          guest_name: 1,
          guest_email: 1,
          guest_phone: 1,
          is_guest: 1,
          'user.userName': 1,
          'user.fullName': 1,
          'user.email': 1,
          'room.room_number': 1,
          'room.floor': 1,
          'roomType.type_name': 1,
          'roomType.price': 1,
          createdAt: 1,
          updatedAt: 1
        }
      },
      { $sort: { createdAt: -1 } }
    ]);

    // Calculate summary statistics
    const summary = {
      totalBookings: bookingsReport.length,
      confirmedBookings: bookingsReport.filter(b => b.booking_status === 'approved').length,
      pendingBookings: bookingsReport.filter(b => b.booking_status === 'pending').length,
      cancelledBookings: bookingsReport.filter(b => b.booking_status === 'cancelled').length,
      checkedInBookings: bookingsReport.filter(b => b.booking_status === 'checked-in').length,
      checkedOutBookings: bookingsReport.filter(b => b.booking_status === 'checked-out').length,
      totalRevenue: bookingsReport.reduce((sum, booking) => {
        return sum + (booking.payment?.status === 'completed' ? (booking.payment?.amount || 0) : 0);
      }, 0),
      averageBookingValue: bookingsReport.length > 0 ? 
        bookingsReport.reduce((sum, booking) => sum + (booking.total_amount || 0), 0) / bookingsReport.length : 0
    };

    // Group by booking status for chart data
    const statusDistribution = bookingsReport.reduce((acc, booking) => {
      const status = booking.booking_status || 'unknown';
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, {});

    // Group by room type for analysis
    const roomTypeDistribution = bookingsReport.reduce((acc, booking) => {
      const roomType = booking.roomType?.type_name || 'Unknown';
      acc[roomType] = (acc[roomType] || 0) + 1;
      return acc;
    }, {});

    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Monthly bookings report retrieved successfully',
      {
        bookings: bookingsReport,
        summary,
        statusDistribution,
        roomTypeDistribution,
        period: {
          startDate: matchCondition.createdAt?.$gte,
          endDate: matchCondition.createdAt?.$lte
        }
      }
    ));
  } catch (error) {
    logger.error('Error getting monthly bookings report:', error);
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

/**
 * Get monthly cleanings report
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getMonthlyCleaningsReport = async (req, res) => {
  try {
    const { year, month, startDate, endDate } = req.query;
    
    let matchCondition = {};
    
    if (year && month) {
      const startOfMonth = new Date(year, month - 1, 1);
      const endOfMonth = new Date(year, month, 0, 23, 59, 59, 999);
      matchCondition.cleaned_at = {
        $gte: startOfMonth,
        $lte: endOfMonth
      };
    } else if (startDate && endDate) {
      matchCondition.cleaned_at = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    } else {
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
      matchCondition.cleaned_at = {
        $gte: startOfMonth,
        $lte: endOfMonth
      };
    }

    const cleaningsReport = await Cleaning.aggregate([
      { $match: matchCondition },
      {
        $lookup: {
          from: 'rooms',
          localField: 'room_id',
          foreignField: '_id',
          as: 'room'
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'cleaned_by',
          foreignField: '_id',
          as: 'cleaner'
        }
      },
      {
        $addFields: {
          room: { $arrayElemAt: ['$room', 0] },
          cleaner: { $arrayElemAt: ['$cleaner', 0] }
        }
      },
      {
        $project: {
          status: 1,
          cleaned_at: 1,
          time_spent: 1,
          notes: 1,
          'room.room_number': 1,
          'room.floor': 1,
          'cleaner.userName': 1,
          'cleaner.fullName': 1,
          'inspection.passed': 1,
          'inspection.inspected_by': 1,
          'inspection.inspected_at': 1,
          issues_reported: 1,
          createdAt: 1
        }
      },
      { $sort: { cleaned_at: -1 } }
    ]);

    const summary = {
      totalCleanings: cleaningsReport.length,
      completedCleanings: cleaningsReport.filter(c => c.status === 'completed').length,
      verifiedCleanings: cleaningsReport.filter(c => c.status === 'verified').length,
      inProgressCleanings: cleaningsReport.filter(c => c.status === 'in_progress').length,
      pendingCleanings: cleaningsReport.filter(c => c.status === 'pending').length,
      averageTimeSpent: cleaningsReport.length > 0 ? 
        cleaningsReport.reduce((sum, cleaning) => sum + (cleaning.time_spent || 0), 0) / cleaningsReport.length : 0,
      totalIssuesReported: cleaningsReport.reduce((sum, cleaning) => 
        sum + (cleaning.issues_reported?.length || 0), 0)
    };

    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Monthly cleanings report retrieved successfully',
      {
        cleanings: cleaningsReport,
        summary,
        period: {
          startDate: matchCondition.cleaned_at?.$gte,
          endDate: matchCondition.cleaned_at?.$lte
        }
      }
    ));
  } catch (error) {
    logger.error('Error getting monthly cleanings report:', error);
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

/**
 * Get monthly maintenance report
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getMonthlyMaintenanceReport = async (req, res) => {
  try {
    const { year, month, startDate, endDate } = req.query;

    let matchCondition = {};

    if (year && month) {
      const startOfMonth = new Date(year, month - 1, 1);
      const endOfMonth = new Date(year, month, 0, 23, 59, 59, 999);
      matchCondition.start_date = {
        $gte: startOfMonth,
        $lte: endOfMonth
      };
    } else if (startDate && endDate) {
      matchCondition.start_date = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    } else {
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
      matchCondition.start_date = {
        $gte: startOfMonth,
        $lte: endOfMonth
      };
    }

    const maintenanceReport = await Maintenance.aggregate([
      { $match: matchCondition },
      {
        $lookup: {
          from: 'rooms',
          localField: 'room_id',
          foreignField: '_id',
          as: 'room'
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'performed_by',
          foreignField: '_id',
          as: 'performer'
        }
      },
      {
        $addFields: {
          room: { $arrayElemAt: ['$room', 0] },
          performer: { $arrayElemAt: ['$performer', 0] }
        }
      },
      {
        $project: {
          start_date: 1,
          end_date: 1,
          reason: 1,
          status: 1,
          notes: 1,
          'cost.amount': 1,
          'cost.currency': 1,
          parts_replaced: 1,
          'room.room_number': 1,
          'room.floor': 1,
          'performer.userName': 1,
          'performer.fullName': 1,
          'completion_report.summary': 1,
          'completion_report.completed_at': 1,
          createdAt: 1
        }
      },
      { $sort: { start_date: -1 } }
    ]);

    const summary = {
      totalMaintenance: maintenanceReport.length,
      scheduledMaintenance: maintenanceReport.filter(m => m.status === 'scheduled').length,
      inProgressMaintenance: maintenanceReport.filter(m => m.status === 'in_progress').length,
      completedMaintenance: maintenanceReport.filter(m => m.status === 'completed').length,
      cancelledMaintenance: maintenanceReport.filter(m => m.status === 'cancelled').length,
      totalCost: maintenanceReport.reduce((sum, maintenance) =>
        sum + (maintenance.cost?.amount || 0), 0),
      averageCost: maintenanceReport.length > 0 ?
        maintenanceReport.reduce((sum, maintenance) => sum + (maintenance.cost?.amount || 0), 0) / maintenanceReport.length : 0
    };

    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Monthly maintenance report retrieved successfully',
      {
        maintenance: maintenanceReport,
        summary,
        period: {
          startDate: matchCondition.start_date?.$gte,
          endDate: matchCondition.start_date?.$lte
        }
      }
    ));
  } catch (error) {
    logger.error('Error getting monthly maintenance report:', error);
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

/**
 * Get monthly revenue report
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const getMonthlyRevenueReport = async (req, res) => {
  try {
    const { year, month, startDate, endDate } = req.query;

    let matchCondition = {
      'payment.status': 'completed'
    };

    if (year && month) {
      const startOfMonth = new Date(year, month - 1, 1);
      const endOfMonth = new Date(year, month, 0, 23, 59, 59, 999);
      matchCondition.createdAt = {
        $gte: startOfMonth,
        $lte: endOfMonth
      };
    } else if (startDate && endDate) {
      matchCondition.createdAt = {
        $gte: new Date(startDate),
        $lte: new Date(endDate)
      };
    } else {
      const now = new Date();
      const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
      const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
      matchCondition.createdAt = {
        $gte: startOfMonth,
        $lte: endOfMonth
      };
    }

    const revenueReport = await Booking.aggregate([
      { $match: matchCondition },
      {
        $lookup: {
          from: 'roomtypes',
          localField: 'room_type',
          foreignField: '_id',
          as: 'roomType'
        }
      },
      {
        $addFields: {
          roomType: { $arrayElemAt: ['$roomType', 0] }
        }
      },
      {
        $group: {
          _id: {
            day: { $dayOfMonth: '$createdAt' },
            month: { $month: '$createdAt' },
            year: { $year: '$createdAt' },
            roomType: '$roomType.type_name'
          },
          dailyRevenue: { $sum: '$payment.amount' },
          bookingCount: { $sum: 1 }
        }
      },
      { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
    ]);

    // Calculate total revenue and other metrics
    const totalRevenue = revenueReport.reduce((sum, item) => sum + item.dailyRevenue, 0);
    const totalBookings = revenueReport.reduce((sum, item) => sum + item.bookingCount, 0);

    // Group by room type
    const revenueByRoomType = revenueReport.reduce((acc, item) => {
      const roomType = item._id.roomType || 'Unknown';
      if (!acc[roomType]) {
        acc[roomType] = { revenue: 0, bookings: 0 };
      }
      acc[roomType].revenue += item.dailyRevenue;
      acc[roomType].bookings += item.bookingCount;
      return acc;
    }, {});

    // Daily revenue trend
    const dailyRevenue = revenueReport.reduce((acc, item) => {
      const date = `${item._id.year}-${String(item._id.month).padStart(2, '0')}-${String(item._id.day).padStart(2, '0')}`;
      if (!acc[date]) {
        acc[date] = 0;
      }
      acc[date] += item.dailyRevenue;
      return acc;
    }, {});

    const summary = {
      totalRevenue,
      totalBookings,
      averageRevenuePerBooking: totalBookings > 0 ? totalRevenue / totalBookings : 0,
      revenueByRoomType,
      dailyRevenue
    };

    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Monthly revenue report retrieved successfully',
      {
        revenue: revenueReport,
        summary,
        period: {
          startDate: matchCondition.createdAt?.$gte,
          endDate: matchCondition.createdAt?.$lte
        }
      }
    ));
  } catch (error) {
    logger.error('Error getting monthly revenue report:', error);
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

/**
 * Export reports as CSV
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
export const exportReportAsCSV = async (req, res) => {
  try {
    const { type, year, month, startDate, endDate } = req.query;

    let data = [];
    let filename = '';
    let headers = [];

    switch (type) {
      case 'bookings':
        // Get bookings data
        const bookingsData = await getBookingsDataForExport(year, month, startDate, endDate);
        data = bookingsData;
        filename = `bookings_report_${new Date().toISOString().split('T')[0]}.csv`;
        headers = [
          'Booking ID', 'Status', 'Guest Name', 'Email', 'Phone', 'Room Number',
          'Room Type', 'Check-in Date', 'Check-out Date', 'Total Amount',
          'Payment Status', 'Payment Method', 'Created Date'
        ];
        break;

      case 'cleanings':
        const cleaningsData = await getCleaningsDataForExport(year, month, startDate, endDate);
        data = cleaningsData;
        filename = `cleanings_report_${new Date().toISOString().split('T')[0]}.csv`;
        headers = [
          'Room Number', 'Floor', 'Cleaner Name', 'Status', 'Cleaned Date',
          'Time Spent (minutes)', 'Inspection Passed', 'Issues Reported', 'Notes'
        ];
        break;

      case 'maintenance':
        const maintenanceData = await getMaintenanceDataForExport(year, month, startDate, endDate);
        data = maintenanceData;
        filename = `maintenance_report_${new Date().toISOString().split('T')[0]}.csv`;
        headers = [
          'Room Number', 'Floor', 'Reason', 'Status', 'Start Date', 'End Date',
          'Performed By', 'Cost', 'Parts Replaced', 'Completion Summary'
        ];
        break;

      case 'revenue':
        const revenueData = await getRevenueDataForExport(year, month, startDate, endDate);
        data = revenueData;
        filename = `revenue_report_${new Date().toISOString().split('T')[0]}.csv`;
        headers = [
          'Date', 'Booking ID', 'Room Type', 'Guest Name', 'Amount',
          'Payment Method', 'Payment Status'
        ];
        break;

      default:
        return res.status(400).json(errorResponse(
          1,
          'INVALID TYPE',
          'Invalid report type. Supported types: bookings, cleanings, maintenance, revenue'
        ));
    }

    // Convert data to CSV format
    const csvContent = convertToCSV(data, headers);

    // Set response headers for CSV download
    res.setHeader('Content-Type', 'text/csv');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.status(200).send(csvContent);

  } catch (error) {
    logger.error('Error exporting report as CSV:', error);
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error.message
    ));
  }
};

// Helper function to convert data to CSV format
const convertToCSV = (data, headers) => {
  const csvRows = [];

  // Add headers
  csvRows.push(headers.join(','));

  // Add data rows
  data.forEach(row => {
    const values = headers.map(header => {
      const value = row[header] || '';
      // Escape commas and quotes in CSV
      return `"${String(value).replace(/"/g, '""')}"`;
    });
    csvRows.push(values.join(','));
  });

  return csvRows.join('\n');
};

// Helper functions to get data for export
const getBookingsDataForExport = async (year, month, startDate, endDate) => {
  let matchCondition = {};

  if (year && month) {
    const startOfMonth = new Date(year, month - 1, 1);
    const endOfMonth = new Date(year, month, 0, 23, 59, 59, 999);
    matchCondition.createdAt = { $gte: startOfMonth, $lte: endOfMonth };
  } else if (startDate && endDate) {
    matchCondition.createdAt = { $gte: new Date(startDate), $lte: new Date(endDate) };
  } else {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
    matchCondition.createdAt = { $gte: startOfMonth, $lte: endOfMonth };
  }

  const bookings = await Booking.aggregate([
    { $match: matchCondition },
    {
      $lookup: {
        from: 'rooms',
        localField: 'room_id',
        foreignField: '_id',
        as: 'room'
      }
    },
    {
      $lookup: {
        from: 'roomtypes',
        localField: 'room_type',
        foreignField: '_id',
        as: 'roomType'
      }
    },
    {
      $addFields: {
        room: { $arrayElemAt: ['$room', 0] },
        roomType: { $arrayElemAt: ['$roomType', 0] }
      }
    }
  ]);

  return bookings.map(booking => ({
    'Booking ID': booking.booking_id,
    'Status': booking.booking_status,
    'Guest Name': booking.is_guest ? booking.guest_name : booking.user?.fullName || 'N/A',
    'Email': booking.is_guest ? booking.guest_email : booking.user?.email || 'N/A',
    'Phone': booking.is_guest ? booking.guest_phone : booking.user?.phone || 'N/A',
    'Room Number': booking.room?.room_number || 'N/A',
    'Room Type': booking.roomType?.type_name || 'N/A',
    'Check-in Date': booking.booking_dates?.[0] ? new Date(booking.booking_dates[0]).toLocaleDateString() : 'N/A',
    'Check-out Date': booking.booking_dates?.length > 1 ? new Date(booking.booking_dates[booking.booking_dates.length - 1]).toLocaleDateString() : 'N/A',
    'Total Amount': booking.total_amount || 0,
    'Payment Status': booking.payment?.status || 'N/A',
    'Payment Method': booking.payment?.method || 'N/A',
    'Created Date': new Date(booking.createdAt).toLocaleDateString()
  }));
};

const getCleaningsDataForExport = async (year, month, startDate, endDate) => {
  let matchCondition = {};

  if (year && month) {
    const startOfMonth = new Date(year, month - 1, 1);
    const endOfMonth = new Date(year, month, 0, 23, 59, 59, 999);
    matchCondition.cleaned_at = { $gte: startOfMonth, $lte: endOfMonth };
  } else if (startDate && endDate) {
    matchCondition.cleaned_at = { $gte: new Date(startDate), $lte: new Date(endDate) };
  } else {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
    matchCondition.cleaned_at = { $gte: startOfMonth, $lte: endOfMonth };
  }

  const cleanings = await Cleaning.aggregate([
    { $match: matchCondition },
    {
      $lookup: {
        from: 'rooms',
        localField: 'room_id',
        foreignField: '_id',
        as: 'room'
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: 'cleaned_by',
        foreignField: '_id',
        as: 'cleaner'
      }
    },
    {
      $addFields: {
        room: { $arrayElemAt: ['$room', 0] },
        cleaner: { $arrayElemAt: ['$cleaner', 0] }
      }
    }
  ]);

  return cleanings.map(cleaning => ({
    'Room Number': cleaning.room?.room_number || 'N/A',
    'Floor': cleaning.room?.floor || 'N/A',
    'Cleaner Name': cleaning.cleaner?.fullName || cleaning.cleaner?.userName || 'N/A',
    'Status': cleaning.status,
    'Cleaned Date': cleaning.cleaned_at ? new Date(cleaning.cleaned_at).toLocaleDateString() : 'N/A',
    'Time Spent (minutes)': cleaning.time_spent || 0,
    'Inspection Passed': cleaning.inspection?.passed ? 'Yes' : 'No',
    'Issues Reported': cleaning.issues_reported?.length || 0,
    'Notes': cleaning.notes || ''
  }));
};

const getMaintenanceDataForExport = async (year, month, startDate, endDate) => {
  let matchCondition = {};

  if (year && month) {
    const startOfMonth = new Date(year, month - 1, 1);
    const endOfMonth = new Date(year, month, 0, 23, 59, 59, 999);
    matchCondition.start_date = { $gte: startOfMonth, $lte: endOfMonth };
  } else if (startDate && endDate) {
    matchCondition.start_date = { $gte: new Date(startDate), $lte: new Date(endDate) };
  } else {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
    matchCondition.start_date = { $gte: startOfMonth, $lte: endOfMonth };
  }

  const maintenance = await Maintenance.aggregate([
    { $match: matchCondition },
    {
      $lookup: {
        from: 'rooms',
        localField: 'room_id',
        foreignField: '_id',
        as: 'room'
      }
    },
    {
      $lookup: {
        from: 'users',
        localField: 'performed_by',
        foreignField: '_id',
        as: 'performer'
      }
    },
    {
      $addFields: {
        room: { $arrayElemAt: ['$room', 0] },
        performer: { $arrayElemAt: ['$performer', 0] }
      }
    }
  ]);

  return maintenance.map(item => ({
    'Room Number': item.room?.room_number || 'N/A',
    'Floor': item.room?.floor || 'N/A',
    'Reason': item.reason,
    'Status': item.status,
    'Start Date': new Date(item.start_date).toLocaleDateString(),
    'End Date': new Date(item.end_date).toLocaleDateString(),
    'Performed By': item.performer?.fullName || item.performer?.userName || 'N/A',
    'Cost': item.cost?.amount ? `${item.cost.amount} ${item.cost.currency || 'USD'}` : 'N/A',
    'Parts Replaced': item.parts_replaced?.map(part => `${part.name} (${part.quantity})`).join('; ') || 'None',
    'Completion Summary': item.completion_report?.summary || ''
  }));
};

const getRevenueDataForExport = async (year, month, startDate, endDate) => {
  let matchCondition = {
    'payment.status': 'completed'
  };

  if (year && month) {
    const startOfMonth = new Date(year, month - 1, 1);
    const endOfMonth = new Date(year, month, 0, 23, 59, 59, 999);
    matchCondition.createdAt = { $gte: startOfMonth, $lte: endOfMonth };
  } else if (startDate && endDate) {
    matchCondition.createdAt = { $gte: new Date(startDate), $lte: new Date(endDate) };
  } else {
    const now = new Date();
    const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
    const endOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0, 23, 59, 59, 999);
    matchCondition.createdAt = { $gte: startOfMonth, $lte: endOfMonth };
  }

  const revenue = await Booking.aggregate([
    { $match: matchCondition },
    {
      $lookup: {
        from: 'roomtypes',
        localField: 'room_type',
        foreignField: '_id',
        as: 'roomType'
      }
    },
    {
      $addFields: {
        roomType: { $arrayElemAt: ['$roomType', 0] }
      }
    }
  ]);

  return revenue.map(booking => ({
    'Date': new Date(booking.createdAt).toLocaleDateString(),
    'Booking ID': booking.booking_id,
    'Room Type': booking.roomType?.type_name || 'N/A',
    'Guest Name': booking.is_guest ? booking.guest_name : booking.user?.fullName || 'N/A',
    'Amount': booking.payment?.amount || 0,
    'Payment Method': booking.payment?.method || 'N/A',
    'Payment Status': booking.payment?.status || 'N/A'
  }));
};
