import mongoose from 'mongoose';
import Counter from '../models/counter.model.js';
import config from '../config/db.config.js';

// Connect to MongoDB
mongoose.connect(config.url, config.options)
  .then(async () => {
    console.log('Connected to MongoDB');
    
    try {
      // Check if booking_id counter exists
      const exists = await Counter.exists({ name: 'booking_id' });
      
      if (!exists) {
        // Create the counter
        await Counter.create({ name: 'booking_id', value: 0 });
        console.log('Booking ID counter initialized');
      } else {
        console.log('Booking ID counter already exists');
      }
      
      console.log('Counter initialization complete');
    } catch (error) {
      console.error('Error initializing counters:', error);
    } finally {
      // Close the connection
      await mongoose.connection.close();
      console.log('MongoDB connection closed');
    }
  })
  .catch(error => {
    console.error('MongoDB connection error:', error);
  });