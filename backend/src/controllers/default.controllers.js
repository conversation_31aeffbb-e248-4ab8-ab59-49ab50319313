import { successResponse, errorResponse } from '../config/app.response.js';

function defaultController(_req, res) {
  try {
    res.status(200).json(successResponse(
      0,
      'SUCCESS',
      'Welcome to TTS Nexus Hotel Room Booking API'
    ));
  } catch (error) {
    res.status(500).json(errorResponse(
      2,
      'SERVER SIDE ERROR',
      error
    ));
  }
}

export default defaultController;
