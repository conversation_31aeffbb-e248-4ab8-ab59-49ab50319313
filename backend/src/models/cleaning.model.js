import mongoose from 'mongoose';

const cleaningSchema = new mongoose.Schema({
  room_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Room',
    required: true
  },
  cleaned_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  cleaned_at: {
    type: Date,
    default: Date.now
  },
  status: {
    type: String,
    enum: ['pending', 'in_progress', 'completed', 'verified'],
    default: 'pending'
  },
  notes: String,
  inspection: {
    inspected_by: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    inspected_at: Date,
    passed: Boolean,
    comments: String
  },
  checklist: {
    bathroom_cleaned: {
      type: Boolean,
      default: false
    },
    bed_made: {
      type: Boolean,
      default: false
    },
    dusting_done: {
      type: Boolean,
      default: false
    },
    vacuum_done: {
      type: Boolean,
      default: false
    },
    linens_changed: {
      type: Boolean,
      default: false
    },
    towels_replaced: {
      type: Boolean,
      default: false
    },
    amenities_restocked: {
      type: Boolean,
      default: false
    },
    trash_emptied: {
      type: Boolean,
      default: false
    }
  },
  issues_reported: [{
    issue: String,
    severity: {
      type: String,
      enum: ['low', 'medium', 'high'],
      default: 'low'
    },
    requires_maintenance: {
      type: Boolean,
      default: false
    }
  }],
  time_spent: {
    type: Number, // in minutes
    default: 0
  }
}, {
  timestamps: true
});

// Method to start cleaning
cleaningSchema.methods.startCleaning = async function() {
  if (this.status !== 'pending') {
    throw new Error('Only pending cleaning can be started');
  }
  
  this.status = 'in_progress';
  
  // Update room status
  const Room = mongoose.model('Room');
  const room = await Room.findById(this.room_id);
  if (room) {
    room.current_status = 'cleaning';
    await room.save();
  }
  
  return this.save();
};

// Method to complete cleaning
cleaningSchema.methods.completeCleaning = async function(checklistData, timeSpent, notes) {
  if (this.status !== 'in_progress') {
    throw new Error('Only in-progress cleaning can be completed');
  }
  
  this.status = 'completed';
  this.cleaned_at = new Date();
  
  if (checklistData) {
    this.checklist = { ...this.checklist, ...checklistData };
  }
  
  if (timeSpent) {
    this.time_spent = timeSpent;
  }
  
  if (notes) {
    this.notes = notes;
  }
  
  // Update room status and last_cleaned
  const Room = mongoose.model('Room');
  const room = await Room.findById(this.room_id);
  if (room) {
    room.current_status = 'available';
    room.last_cleaned = {
      date: new Date(),
      by: this.cleaned_by,
      notes: this.notes
    };
    await room.save();
  }
  
  return this.save();
};

// Method to verify cleaning
cleaningSchema.methods.verifyCleaning = async function(inspectorId, passed, comments) {
  if (this.status !== 'completed') {
    throw new Error('Only completed cleaning can be verified');
  }
  
  this.status = 'verified';
  this.inspection = {
    inspected_by: inspectorId,
    inspected_at: new Date(),
    passed: passed,
    comments: comments
  };
  
  // If cleaning failed inspection, update room status back to cleaning
  if (!passed) {
    const Room = mongoose.model('Room');
    const room = await Room.findById(this.room_id);
    if (room) {
      room.current_status = 'cleaning';
      await room.save();
    }
  }
  
  return this.save();
};

// Method to report issues
cleaningSchema.methods.reportIssue = async function(issue, severity, requiresMaintenance) {
  this.issues_reported.push({
    issue,
    severity,
    requires_maintenance
  });
  
  // If issue requires maintenance, create a maintenance record
  if (requiresMaintenance) {
    const Maintenance = mongoose.model('Maintenance');
    await new Maintenance({
      room_id: this.room_id,
      start_date: new Date(),
      end_date: new Date(new Date().setDate(new Date().getDate() + 1)), // Default to 1 day
      reason: `Issue reported during cleaning: ${issue}`,
      performed_by: this.cleaned_by,
      status: 'scheduled'
    }).save();
  }
  
  return this.save();
};

// Add indexes for faster queries
cleaningSchema.index({ room_id: 1 });
cleaningSchema.index({ cleaned_by: 1 });
cleaningSchema.index({ status: 1 });
cleaningSchema.index({ cleaned_at: -1 });
cleaningSchema.index({ 'inspection.passed': 1 });

const Cleaning = mongoose.model('Cleaning', cleaningSchema);

export default Cleaning;