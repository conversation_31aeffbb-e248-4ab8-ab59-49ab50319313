import { PlusOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Tabs } from 'antd';
import React, { useRef, useState } from 'react';
import RoomTypesList from './RoomTypesList';
import CreateRoomType from './CreateRoomType';
import RoomTypeDetails from './RoomTypeDetails';

function RoomTypes() {
  const [activeKey, setActiveKey] = useState('list');
  const [items, setItems] = useState([
    {
      key: 'list',
      label: 'Room Types',
      children: <RoomTypesList onEdit={(id) => handleEdit(id)} />,
      closable: false
    }
  ]);
  const newTabIndex = useRef(0);

  const handleEdit = (id) => {
    const newActiveKey = `details-${id}`;
    const existingTab = items.find(item => item.key === newActiveKey);
    
    if (!existingTab) {
      setItems([...items, {
        key: newActiveKey,
        label: 'Room Type Details',
        children: <RoomTypeDetails id={id} />,
        closable: true
      }]);
    }
    setActiveKey(newActiveKey);
  };

  const addCreateTab = () => {
    const newActiveKey = `create-${newTabIndex.current++}`;
    setItems([...items, {
      key: newActiveKey,
      label: 'Create Room Type',
      children: <CreateRoomType onSuccess={() => {
        removeTab(newActiveKey);
        setActiveKey('list');
      }} />,
      closable: true
    }]);
    setActiveKey(newActiveKey);
  };

  const removeTab = (targetKey) => {
    const newItems = items.filter(item => item.key !== targetKey);
    setItems(newItems);
    
    if (targetKey === activeKey) {
      setActiveKey(newItems[newItems.length - 1].key);
    }
  };

  return (
    <Tabs
      activeKey={activeKey}
      onChange={setActiveKey}
      type="editable-card"
      onEdit={(targetKey, action) => {
        if (action === 'remove') {
          removeTab(targetKey);
        }
      }}
      tabBarExtraContent={
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={addCreateTab}
          size="large"
        >
          Create Room Type
        </Button>
      }
      items={items}
      hideAdd
    />
  );
}

export default RoomTypes;