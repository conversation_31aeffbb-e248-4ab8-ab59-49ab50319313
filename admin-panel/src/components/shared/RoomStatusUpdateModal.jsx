import {
  Button, Modal, Select, Input
} from 'antd';
import React, { useEffect, useState } from 'react';
import ApiService from '../../utils/apiService';
import notificationWithIcon from '../../utils/notification';

function RoomStatusUpdateModal({ statusUpdateModal, setStatusUpdateModal, setFetchAgain }) {
  const [roomStatus, setRoomStatus] = useState([
    { value: 'approved', label: 'Approved', disabled: false },
    { value: 'rejected', label: 'Rejected', disabled: false },
    { value: 'cancelled', label: 'Cancelled', disabled: false },
    { value: 'in-reviews', label: 'In Reviews', disabled: false },
    { value: 'checked_in', label: 'Check In', disabled: false },
    { value: 'checked_out', label: 'Check Out', disabled: false }
  ]);
  const [loading, setLoading] = useState(false);
  const [status, setStatus] = useState(null);
  const [reason, setReason] = useState('');

  useEffect(() => {
    if (statusUpdateModal?.status === 'approved') {
      setRoomStatus([
        { value: 'approved', label: 'Approved', disabled: true },
        { value: 'rejected', label: 'Rejected', disabled: true },
        { value: 'cancelled', label: 'Cancelled', disabled: false },
        { value: 'checked_in', label: 'Check In', disabled: false },
        { value: 'checked_out', label: 'Check Out', disabled: true }
      ]);
    } else if (statusUpdateModal?.status === 'checked_in') {
      setRoomStatus([
        { value: 'checked_out', label: 'Check Out', disabled: false },
        { value: 'cancelled', label: 'Cancelled', disabled: false }
      ]);
    }
  }, [statusUpdateModal]);

  const getEndpointAndPayload = () => {
    const bookingId = statusUpdateModal?.roomId;
    
    switch (status) {
      case 'checked_in':
        return {
          endpoint: `/api/v1/admin/booking/${bookingId}/check-in`,
          method: 'put',
          payload: {}
        };
      
      case 'checked_out':
        return {
          endpoint: `/api/v1/admin/booking/${bookingId}/check-out`,
          method: 'put',
          payload: {}
        };
      
      case 'cancelled':
        return {
          endpoint: `/api/v1/admin/booking/${bookingId}/cancel`,
          method: 'put',
          payload: {
            cancellation_reason: reason,
            cancelled_at: new Date().toISOString()
          }
        };
      
      case 'rejected':
        return {
          endpoint: `/api/v1/admin/booking/${bookingId}/reject`,
          method: 'post',
          payload: {
            rejection_reason: reason
          }
        };
      
      default:
        return {
          endpoint: `/api/v1/updated-booking-order/${bookingId}`,
          method: 'put',
          payload: {
            booking_status: status
          }
        };
    }
  };

  const handleUpdateStatus = async () => {
    if (!status) {
      notificationWithIcon('error', 'ERROR', 'Please select a status first to update room status');
      return;
    }

    if ((status === 'cancelled' || status === 'rejected') && !reason.trim()) {
      notificationWithIcon('error', 'ERROR', `Please provide a ${status === 'cancelled' ? 'cancellation' : 'rejection'} reason`);
      return;
    }

    try {
      setLoading(true);
      const { endpoint, method, payload } = getEndpointAndPayload();
      
      const apiCall = method === 'post' 
        ? ApiService.post(endpoint, payload)
        : ApiService.put(endpoint, payload);

      const response = await apiCall;

      if (response?.result_code === 0) {
        notificationWithIcon('success', 'SUCCESS', 'Booking status updated successfully');
        setStatusUpdateModal((prevState) => ({ ...prevState, open: false }));
        setFetchAgain((prevState) => !prevState);
      } else {
        throw new Error(response?.result?.error || 'Failed to update status');
      }
    } catch (err) {
      notificationWithIcon('error', 'ERROR', err?.response?.data?.result?.error || 'Something went wrong');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      title='Update Booking Status'
      open={statusUpdateModal?.open}
      onCancel={() => {
        setStatusUpdateModal((prevState) => ({ ...prevState, open: false }));
        setStatus(null);
        setReason('');
      }}
      footer={[
        <Button
          key='cancel'
          onClick={() => {
            setStatusUpdateModal((prevState) => ({ ...prevState, open: false }));
            setStatus(null);
            setReason('');
          }}
        >
          Cancel
        </Button>,
        <Button
          key='submit'
          type='primary'
          loading={loading}
          onClick={handleUpdateStatus}
        >
          Update
        </Button>
      ]}
    >
      <div className='flex flex-col gap-4'>
        <Select
          className='w-full'
          placeholder='Select status'
          onChange={(value) => {
            setStatus(value);
            setReason('');
          }}
          options={roomStatus}
        />
        {(status === 'cancelled' || status === 'rejected') && (
          <Input.TextArea
            placeholder={`Enter ${status === 'cancelled' ? 'cancellation' : 'rejection'} reason`}
            value={reason}
            onChange={(e) => setReason(e.target.value)}
            rows={4}
          />
        )}
      </div>
    </Modal>
  );
}

export default RoomStatusUpdateModal;
