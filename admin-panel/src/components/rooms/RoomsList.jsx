import React, { useState, useEffect, useCallback } from 'react';
import { 
  Table, Tag, Button, Space, Tooltip, Drawer, 
  Calendar, Badge, Skeleton, Empty, Image, Result, Modal
} from 'antd';
import { 
  EditOutlined, DeleteOutlined, EyeOutlined, 
  CalendarOutlined, ToolOutlined, CheckOutlined
} from '@ant-design/icons';
import ApiService from '../../utils/apiService';
import notificationWithIcon from '../../utils/notification';
import { format } from 'date-fns';
import useFetchData from '../../hooks/useFetchData';
import QueryOptions from '../shared/QueryOptions';
import { ExclamationCircleFilled } from '@ant-design/icons';
import dayjs from 'dayjs';

const { confirm } = Modal;

function RoomsList({ onEdit }) {
  const [loading, setLoading] = useState(false);
  const [query, setQuery] = useState({
    search: '',
    sort: 'asc',
    page: '1',
    rows: '10',
    sortField: 'room_number',
    sortOrder: 'asc'
  });
  const [calendarVisible, setCalendarVisible] = useState(false);
  const [selectedRoom, setSelectedRoom] = useState(null);
  const [bookingDates, setBookingDates] = useState([]);
  const [maintenanceDates, setMaintenanceDates] = useState([]);
  const [unavailableDates, setUnavailableDates] = useState([]); // Add state for other unavailable dates
  const [calendarLoading, setCalendarLoading] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(dayjs());

  // Fetch rooms data with updated query parameters
  const [fetchLoading, fetchError, response] = useFetchData(
    `/api/v1/all-rooms-list?keyword=${query.search}&limit=${query.rows}&page=${query.page}&sort=${query.sortOrder}&sortBy=${query.sortField}`
  );

  // Handle table change for sorting, pagination, etc.
  const handleTableChange = (pagination, filters, sorter) => {
    const { field, order } = sorter;
    
    // Update query with new sort parameters
    setQuery(prev => ({
      ...prev,
      page: pagination.current.toString(),
      rows: pagination.pageSize.toString(),
      sortField: field || 'room_number', // Default to room_number if no field
      sortOrder: order === 'ascend' ? 'asc' : order === 'descend' ? 'desc' : 'asc' // Convert antd sort order to API format
    }));
  };

  // Handle edit action
  const handleEdit = useCallback((roomId) => {
    if (!roomId) {
      notificationWithIcon('error', 'ERROR', 'Room ID is required for editing');
      return;
    }
    onEdit(roomId);
  }, [onEdit]);

  // Handle delete action
  const handleDelete = useCallback((id) => {
    if (!id) {
      notificationWithIcon('error', 'ERROR', 'Room ID is required for deletion');
      return;
    }

    confirm({
      title: 'Are you sure you want to delete this room?',
      icon: <ExclamationCircleFilled />,
      content: 'This action cannot be undone',
      onOk() {
        return new Promise((resolve, reject) => {
          setLoading(true);
          ApiService.delete(`/api/v1/delete-room/${id}`)
            .then(res => {
              if (res?.result_code === 0) {
                notificationWithIcon('success', 'SUCCESS', res?.result?.message || 'Room deleted successfully');
                setQuery(prev => ({ ...prev, timestamp: Date.now() }));
                resolve();
              } else {
                throw new Error(res?.result?.error || 'Failed to delete room');
              }
            })
            .catch(err => {
              notificationWithIcon('error', 'ERROR', err?.response?.data?.result?.error || 'Failed to delete room');
              reject(err);
            })
            .finally(() => {
              setLoading(false);
            });
        });
      }
    });
  }, []);

  // Handle view calendar action
  const handleViewCalendar = useCallback((room) => {
    setSelectedRoom(room);
    setCalendarVisible(true);
    fetchRoomAvailability(room._id, dayjs().year(), dayjs().month() + 1);
  }, []);

  // Fetch room availability data
  const fetchRoomAvailability = async (roomId, year, month) => {
    try {
      setCalendarLoading(true);
      const response = await ApiService.get(`/api/v1/room-booking-calendar/${roomId}?year=${year}&month=${month}`);
      
      if (response?.result_code === 0) {
        const calendar = response.result.data.calendar || [];
        
        // Extract dates based on status
        const booked = calendar
          .filter(day => day.status === 'booked')
          .map(day => day.date);
          
        const maintenance = calendar
          .filter(day => day.status === 'maintenance')
          .map(day => day.date);
          
        // Other unavailable dates (not booked or maintenance but unavailable)
        const unavailable = calendar
          .filter(day => !day.available && 
                 day.status !== 'booked' && 
                 day.status !== 'maintenance')
          .map(day => day.date);
          
        setBookingDates(booked);
        setMaintenanceDates(maintenance);
        setUnavailableDates(unavailable);
      }
    } catch (error) {
      notificationWithIcon('error', 'Error', 'Failed to fetch room availability');
    } finally {
      setCalendarLoading(false);
    }
  };

  // Handle calendar panel change
  const handlePanelChange = (date) => {
    setCurrentMonth(date);
    if (selectedRoom) {
      fetchRoomAvailability(selectedRoom._id, date.year(), date.month() + 1);
    }
  };

  // Calendar cell renderer
  const dateCellRender = (date) => {
    const dateStr = date.format('YYYY-MM-DD');
    const isBooked = bookingDates.includes(dateStr);
    const isMaintenance = maintenanceDates.includes(dateStr);
    const isUnavailable = unavailableDates.includes(dateStr);
    
    // Only render for current month
    if (date.month() !== currentMonth.month()) {
      return null;
    }
    
    return (
      <div className="date-cell">
        {isBooked && (
          <Badge status="error" text={<span className="text-xs">Booked</span>} />
        )}
        {isMaintenance && (
          <Badge status="warning" text={<span className="text-xs">Maintenance</span>} />
        )}
        {isUnavailable && (
          <Badge status="default" text={<span className="text-xs">Unavailable</span>} />
        )}
        {!isBooked && !isMaintenance && !isUnavailable && (
          <Badge status="success" text={<span className="text-xs">Available</span>} />
        )}
      </div>
    );
  };

  const columns = [
    {
      title: 'Image',
      key: 'image',
      width: 120,
      render: (_, record) => (
        <div className="w-[100px] h-[40px]">
          {record?.room_images?.[0]?.url ? (
            <Image
              src={record.room_images[0].url}
              alt={record.name || 'Room image'}
              className="w-full h-full object-cover rounded"
              preview={{
                mask: 'View',
                maskClassName: 'rounded'
              }}
            />
          ) : (
            <div className="w-full h-full bg-gray-200 rounded flex items-center justify-center text-gray-400 text-sm">
              No Image
            </div>
          )}
        </div>
      )
    },
    {
      title: 'Room Number',
      dataIndex: 'room_number',
      key: 'room_number',
      sorter: true
    },
    {
      title: 'Room Type',
      dataIndex: ['room_type', 'name'],
      key: 'room_type',
      render: (text, record) => record?.room_type?.name || 'N/A',
      sorter: true
    },
    {
      title: 'Floor',
      dataIndex: 'floor',
      key: 'floor',
      sorter: true
    },
    {
      title: 'Status',
      dataIndex: 'current_status',
      key: 'current_status',
      render: status => (
        <Tag color={
          status === 'available' ? 'green' :
          status === 'occupied' ? 'blue' :
          status === 'reserved' ? 'purple' :
          status === 'cleaning' ? 'cyan' :
          status === 'maintenance' ? 'orange' :
          status === 'out_of_order' ? 'red' :
          status === 'blocked' ? 'magenta' :
          status === 'inspection' ? 'gold' : 'default'
        }>
          {(status || 'N/A').toUpperCase()}
        </Tag>
      ),
      sorter: true
    },
    {
      title: 'Active',
      dataIndex: 'is_active',
      key: 'is_active',
      render: active => (
        <Tag color={active ? 'green' : 'red'}>
          {active ? 'YES' : 'NO'}
        </Tag>
      ),
      sorter: true
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <div className="space-x-2">
          <Button
            type="primary"
            size="small"
            onClick={() => handleEdit(record._id)}
          >
            Edit
          </Button>
          <Button
            type="primary"
            danger
            size="small"
            onClick={() => handleDelete(record._id)}
          >
            Delete
          </Button>
          <Tooltip title="View Booking Calendar">
            <Button
              type="default"
              size="small"
              icon={<CalendarOutlined />}
              onClick={() => handleViewCalendar(record)}
            />
          </Tooltip>
        </div>
      )
    }
  ];

  // Handle critical error states (not including "No rooms found" which is a normal state)
  if (fetchError && !fetchError.includes("No rooms found")) {
    return (
      <Result
        status="error"
        title="Error"
        subTitle={`Failed to load rooms list: ${fetchError}`}
      />
    );
  }

  // Get data - handle both normal response and "No rooms found" response
  const roomsData = response?.data?.rows || [];
  const totalRows = response?.data?.total_rows || 0;

  // Always render the table, even when empty
  return (
    <div>
      <QueryOptions 
        query={query} 
        setQuery={setQuery}
        searchPlaceholder="Search rooms..."
      />
      <Table
        columns={columns}
        dataSource={roomsData}
        loading={fetchLoading}
        rowKey="_id"
        onChange={handleTableChange} // Add onChange handler for sorting
        pagination={{
          total: totalRows,
          pageSize: Number(query.rows),
          current: Number(query.page),
          showSizeChanger: true,
          showTotal: (total) => `Total ${total} rooms`,
          onChange: (page, pageSize) => {
            setQuery(prev => ({
              ...prev,
              page: String(page),
              rows: String(pageSize)
            }));
          }
        }}
        locale={{
          emptyText: (
            <Empty 
              description={fetchError?.includes("No rooms found") ? "No rooms found" : "No data available"}
              image={Empty.PRESENTED_IMAGE_SIMPLE}
            >
              <Button type="primary" onClick={() => setQuery(prev => ({ ...prev, timestamp: Date.now() }))}>
                Refresh
              </Button>
            </Empty>
          )
        }}
      />

      {/* Room Availability Calendar Drawer */}
      <Drawer
        title={`Room ${selectedRoom?.room_number} - Booking Calendar`}
        open={calendarVisible}
        onClose={() => setCalendarVisible(false)}
        width={800}
        placement="right"
        extra={[
          <Button key="close" onClick={() => setCalendarVisible(false)}>
            Close
          </Button>
        ]}
      >
        <div className="calendar-legend mb-4 flex gap-4">
          <div className="flex items-center">
            <Badge status="success" />
            <span className="ml-2">Available</span>
          </div>
          <div className="flex items-center">
            <Badge status="error" />
            <span className="ml-2">Booked</span>
          </div>
          <div className="flex items-center">
            <Badge status="warning" />
            <span className="ml-2">Maintenance</span>
          </div>
          <div className="flex items-center">
            <Badge status="default" />
            <span className="ml-2">Unavailable</span>
          </div>
        </div>
        
        {calendarLoading ? (
          <div className="flex justify-center items-center h-96">
            <Skeleton active paragraph={{ rows: 10 }} />
          </div>
        ) : (
          <Calendar 
            dateCellRender={dateCellRender}
            onPanelChange={handlePanelChange}
            value={currentMonth}
          />
        )}
      </Drawer>
    </div>
  );
}

export default RoomsList;
