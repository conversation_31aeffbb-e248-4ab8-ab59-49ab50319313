import React, { useState, useEffect } from 'react';
import dayjs from 'dayjs';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import interactionPlugin from '@fullcalendar/interaction';
import timeGridPlugin from '@fullcalendar/timegrid';
import { Card, Drawer, Descriptions, Tag, Spin, Empty, Alert } from 'antd';
import useFetchData from '../../hooks/useFetchData';
import { bookingStatusAsResponse } from '../../utils/responseAsStatus';
import ApiService from '../../utils/apiService';

// Add this CSS at the top of your file
const calendarStyles = {
  '.fc': {
    '--fc-border-color': '#e5e7eb',
    '--fc-button-bg-color': '#2563eb',
    '--fc-button-border-color': '#2563eb',
    '--fc-button-hover-bg-color': '#1d4ed8',
    '--fc-button-hover-border-color': '#1d4ed8',
    '--fc-button-active-bg-color': '#1e40af',
    '--fc-today-bg-color': '#eff6ff',
    '--fc-event-bg-color': '#2563eb',
    '--fc-event-border-color': '#2563eb',
    '--fc-page-bg-color': '#ffffff',
    '--fc-neutral-bg-color': '#ffffff',
  },
  '.fc .fc-toolbar': {
    'padding': '1rem',
    'margin-bottom': '0',
  },
  '.fc .fc-toolbar-title': {
    'fontSize': '1.25rem',
    'fontWeight': '600',
  },
  '.fc .fc-button': {
    'padding': '0.5rem 1rem',
    'fontWeight': '500',
    'borderRadius': '0.375rem',
    'textTransform': 'capitalize',
    'boxShadow': '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  },
  '.fc .fc-button-primary:not(:disabled):active, .fc .fc-button-primary:not(:disabled).fc-button-active': {
    'backgroundColor': 'var(--fc-button-active-bg-color)',
    'borderColor': 'var(--fc-button-active-bg-color)',
  },
  '.fc .fc-daygrid-day-frame': {
    'padding': '0.5rem',
  },
  '.fc .fc-daygrid-day-number': {
    'fontSize': '0.875rem',
    'fontWeight': '500',
    'color': '#4b5563',
  },
  '.fc .fc-col-header-cell': {
    'padding': '0.75rem 0',
    'backgroundColor': '#f9fafb',
  },
  '.fc .fc-col-header-cell-cushion': {
    'fontWeight': '600',
    'color': '#374151',
  },
  '.fc-theme-standard td, .fc-theme-standard th': {
    'borderColor': 'var(--fc-border-color)',
  },
  '.fc-event': {
    'borderRadius': '0.375rem',
    'padding': '0.25rem',
    'marginBottom': '0.25rem',
    'boxShadow': '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    'transition': 'transform 0.2s ease-in-out',
    '&:hover': {
      'transform': 'translateY(-1px)',
      'boxShadow': '0 4px 6px -1px rgb(0 0 0 / 0.1)',
    },
  },
};

function BookingCalendar() {
  const [selectedBooking, setSelectedBooking] = useState(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [bookingEvents, setBookingEvents] = useState([]);

  // Define headerToolbar and buttonText at the top of the component
  const buttonText = {
    today: 'Today',
    month: 'Month',
    week: 'Week',
    day: 'Day',
  };

  const headerToolbar = {
    left: 'prev,next today',
    center: 'title',
    right: 'dayGridMonth,timeGridWeek,timeGridDay'
  };

  // Fetch booking data
  const [loading, error, response] = useFetchData('/api/v1/bookings');
  
  useEffect(() => {
    console.log('Booking response:', response); // Debug log
    
    // Check if response exists and has the expected structure
    if (response && response.result && response.result.data) {
      // Handle the case where data is in result.data (common API pattern)
      const bookingsData = response.result.data.bookings || response.result.data;
      
      if (Array.isArray(bookingsData)) {
        console.log('Processing bookings array:', bookingsData.length); // Debug log
        const events = bookingsData.map((booking) => {
          // Get customer name based on booking type
          const customerName = booking.is_guest 
            ? `Guest: ${booking.guest_name || 'Guest'}`
            : booking.user_id?.name || booking.user?.name || 'N/A';

          // Ensure booking_dates exists and is an array
          if (!booking.booking_dates || !Array.isArray(booking.booking_dates) || booking.booking_dates.length === 0) {
            console.log('Booking without valid dates:', booking._id);
            return null; // Skip this booking
          }

          const startDate = dayjs(booking.booking_dates[0]).format('YYYY-MM-DD');
          // Add 1 day to end date since FullCalendar uses exclusive end dates
          const endDate = dayjs(booking.booking_dates[booking.booking_dates.length - 1])
            .add(1, 'day')
            .format('YYYY-MM-DD');

          return {
            id: booking._id,
            title: `${booking.room_id?.room_number || booking.room?.room_number || 'Room N/A'} - ${customerName}`,
            start: startDate,
            end: endDate,
            backgroundColor: getStatusColor(booking.booking_status),
            borderColor: getStatusColor(booking.booking_status),
            textColor: '#ffffff',
            extendedProps: {
              ...booking
            }
          };
        }).filter(event => event !== null); // Filter out null events
        
        console.log('Generated events:', events.length); // Debug log
        setBookingEvents(events);
      } else {
        console.error('Bookings data is not an array:', bookingsData);
      }
    } else if (response && Array.isArray(response.data)) {
      // Handle the case where data is directly in response.data
      console.log('Processing response.data array:', response.data.length); // Debug log
      const events = response.data.map((booking) => {
        // Get customer name based on booking type
        const customerName = booking.is_guest 
          ? `Guest: ${booking.guest_name || booking.guest_names?.[0] || 'Guest'}`
          : booking.user_id?.name || booking.user?.name || 'N/A';

        // Ensure booking_dates exists and is an array
        if (!booking.booking_dates || !Array.isArray(booking.booking_dates) || booking.booking_dates.length === 0) {
          console.log('Booking without valid dates:', booking._id);
          return null; // Skip this booking
        }

        const startDate = dayjs(booking.booking_dates[0]).format('YYYY-MM-DD');
        // Add 1 day to end date since FullCalendar uses exclusive end dates
        const endDate = dayjs(booking.booking_dates[booking.booking_dates.length - 1])
          .add(1, 'day')
          .format('YYYY-MM-DD');

        return {
          id: booking._id,
          title: `${booking.room_id?.room_number || booking.room?.room_number || 'Room N/A'} - ${customerName}`,
          start: startDate,
          end: endDate,
          backgroundColor: getStatusColor(booking.booking_status),
          borderColor: getStatusColor(booking.booking_status),
          textColor: '#ffffff',
          extendedProps: {
            ...booking
          }
        };
      }).filter(event => event !== null); // Filter out null events
      
      console.log('Generated events:', events.length); // Debug log
      setBookingEvents(events);
    } else {
      console.error('Unexpected response structure:', response);
      
      // Attempt to fetch bookings directly if the response structure is unexpected
      fetchBookingsDirectly();
    }
  }, [response]);

  // Fallback function to fetch bookings directly
  const fetchBookingsDirectly = async () => {
    try {
      console.log('Attempting to fetch bookings directly');
      const directResponse = await ApiService.get('/api/v1/bookings');
      
      if (directResponse && directResponse.result && directResponse.result.data) {
        const bookingsData = directResponse.result.data.bookings || directResponse.result.data;
        
        if (Array.isArray(bookingsData)) {
          console.log('Direct fetch successful, processing bookings:', bookingsData.length);
          processBookingsIntoEvents(bookingsData);
        }
      }
    } catch (error) {
      console.error('Error fetching bookings directly:', error);
    }
  };

  // Helper function to process bookings into calendar events
  const processBookingsIntoEvents = (bookingsData) => {
    if (!Array.isArray(bookingsData)) {
      console.error('bookingsData is not an array');
      return;
    }
    
    const events = bookingsData.map((booking) => {
      if (!booking) return null;
      
      // Get customer name based on booking type
      const customerName = booking.is_guest 
        ? `Guest: ${booking.guest_name || booking.guest_names?.[0] || 'Guest'}`
        : booking.user_id?.fullName	 || booking.user?.fullName || 'N/A';

      // Ensure booking_dates exists and is an array
      if (!booking.booking_dates || !Array.isArray(booking.booking_dates) || booking.booking_dates.length === 0) {
        return null; // Skip this booking
      }

      const startDate = dayjs(booking.booking_dates[0]).format('YYYY-MM-DD');
      // Add 1 day to end date since FullCalendar uses exclusive end dates
      const endDate = dayjs(booking.booking_dates[booking.booking_dates.length - 1])
        .add(1, 'day')
        .format('YYYY-MM-DD');

      return {
        id: booking._id,
        title: `${booking.room_id?.room_number || booking.room?.room_number || 'Room N/A'} - ${customerName}`,
        start: startDate,
        end: endDate,
        backgroundColor: getStatusColor(booking.booking_status),
        borderColor: getStatusColor(booking.booking_status),
        textColor: '#ffffff',
        extendedProps: {
          ...booking
        }
      };
    }).filter(event => event !== null); // Filter out null events
    
    setBookingEvents(events);
  };

  const getStatusColor = (status) => {
    const statusColors = {
      pending: '#FFBB28',
      approved: '#00C49F',
      completed: '#82ca9d',
      cancelled: '#8884d8',
      rejected: '#FF8042',
      'checked-in': '#0088FE',
      'checked-out': '#82ca9d',
      'no-show': '#FF8042'
    };
    return statusColors[status] || '#999999';
  };

  const handleEventClick = (clickInfo) => {
    setSelectedBooking(clickInfo.event.extendedProps);
    setIsModalVisible(true);
  };

  const renderEventContent = (eventInfo) => {
    const booking = eventInfo.event.extendedProps;
    const customerName = booking.is_guest 
      ? `Guest: ${booking.guest_name || booking.guest_names?.[0] || 'Guest'}`
      : booking.user_id?.name || booking.user?.fullName || 'N/A';

    return (
      <div className='p-2 space-y-1.5'>
        <div className='flex items-center space-x-2'>
          <div className='w-2 h-2 rounded-full bg-white/80' />
          <div className='font-semibold truncate text-white'>
            {booking.room_id?.room_number || booking.room?.room_number || 'Room N/A'}
          </div>
        </div>
        <div className='text-sm truncate text-white/90 pl-4'>
          {customerName}
        </div>
        <div className='text-xs truncate pl-4'>
          {booking.check_in_time || '14:00'} - {booking.check_out_time || '12:00'}
        </div>
        <div className='text-xs truncate pl-4 bg-white/20 rounded-full px-2 py-0.5 inline-block'>
          {booking.booking_status}
        </div>
      </div>
    );
  };

  if (error) {
    return (
      <div className='p-4'>
        <Card 
          className='shadow-xl rounded-2xl overflow-hidden border-0'
          bodyStyle={{ padding: 0 }}
        >
          <div className='p-6'>
            <Alert
              message="Error loading bookings"
              description={error}
              type="error"
              showIcon
            />
          </div>
          <div className='calendar-container'>
            <FullCalendar
              plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
              initialView='dayGridMonth'
              events={[]}
              height='800px'
              headerToolbar={headerToolbar}
              buttonText={buttonText}
              slotMinTime='08:00:00'
              slotMaxTime='20:00:00'
              allDaySlot={false}
              nowIndicator
              editable={false}
              selectable
              selectMirror
              dayMaxEventRows={3}
              eventDisplay='block'
              weekends
            />
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className='p-4'>
      <Card 
        className='shadow-xl rounded-2xl overflow-hidden border-0'
        bodyStyle={{ padding: 0 }}
      >
        {loading ? (
          <div className='calendar-loading flex flex-col items-center justify-center p-10'>
            <Spin size='large' />
            <div className='text-gray-500 mt-4'>Loading calendar...</div>
          </div>
        ) : (
          <div className='calendar-container'>
            {bookingEvents.length === 0 && (
              <div className='p-4 bg-gray-50 border-b'>
                <Alert
                  message="No bookings found"
                  description="The calendar is currently empty. Bookings will appear here when they are created."
                  type="info"
                  showIcon
                />
              </div>
            )}
            <FullCalendar
              plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
              initialView='dayGridMonth'
              events={bookingEvents}
              eventClick={handleEventClick}
              eventContent={renderEventContent}
              height='800px'
              headerToolbar={headerToolbar}
              buttonText={buttonText}
              slotMinTime='08:00:00'
              slotMaxTime='20:00:00'
              allDaySlot={false}
              nowIndicator
              editable={false}
              selectable
              selectMirror
              dayMaxEventRows={3}
              eventDisplay='block'
              weekends
              eventTimeFormat={{
                hour: '2-digit',
                minute: '2-digit',
                meridiem: true
              }}
              eventDidMount={(info) => {
                info.el.style.minHeight = '70px';
              }}
              views={{
                dayGrid: {
                  dayMaxEventRows: 3
                }
              }}
            />
          </div>
        )}
      </Card>

      <Drawer
        title={
          <div className='text-lg font-semibold text-gray-800 py-2'>
            Booking Details
          </div>
        }
        open={isModalVisible}
        onClose={() => setIsModalVisible(false)}
        width={600}
        placement="right"
      >
        {selectedBooking && (
          <Descriptions 
            bordered 
            column={1}
            className='bg-white rounded-lg overflow-hidden'
            labelStyle={{ 
              backgroundColor: '#f9fafb',
              fontWeight: '500'
            }}
            contentStyle={{
              backgroundColor: '#ffffff'
            }}
          >
            <Descriptions.Item label='Room'>
              {selectedBooking.room_id?.room_number || selectedBooking.room?.room_number || 'N/A'}
            </Descriptions.Item>
            <Descriptions.Item label='Customer Type'>
              {selectedBooking.is_guest ? 'Guest' : 'Registered User'}
            </Descriptions.Item>
            <Descriptions.Item label='Customer Name'>
              {selectedBooking.is_guest 
                ? (selectedBooking.guest_name || selectedBooking.guest_names?.[0] || 'Guest')
                : (selectedBooking.user_id?.fullName|| selectedBooking.user?.fullName || 'N/A')}
            </Descriptions.Item>
            {selectedBooking.is_guest && (
              <>
                <Descriptions.Item label='Guest Email'>
                  {selectedBooking.guest_email || 'N/A'}
                </Descriptions.Item>
                <Descriptions.Item label='Guest Phone'>
                  {selectedBooking.guest_phone || 'N/A'}
                </Descriptions.Item>
              </>
            )}
            <Descriptions.Item label='Dates'>
              {Array.isArray(selectedBooking.booking_dates) && selectedBooking.booking_dates.length > 0
                ? selectedBooking.booking_dates.map((date) => (
                    dayjs(date).format('MMMM D, YYYY')
                  )).join(' - ')
                : 'No dates available'}
            </Descriptions.Item>
            <Descriptions.Item label='Number of Guests'>
              {typeof selectedBooking.guests === 'object' 
                ? `${selectedBooking.guests.adults || 0} Adults, ${selectedBooking.guests.children || 0} Children`
                : (typeof selectedBooking.guests === 'number' 
                    ? selectedBooking.guests 
                    : '0')}
            </Descriptions.Item>
            <Descriptions.Item label='Status'>
              <Tag color={bookingStatusAsResponse(selectedBooking.booking_status).color}>
                {bookingStatusAsResponse(selectedBooking.booking_status).level}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label='Payment Status'>
              <Tag color={selectedBooking.payment?.status === 'completed' ? 'green' : 'orange'}>
                {selectedBooking.payment?.status || 'pending'}
              </Tag>
            </Descriptions.Item>
            <Descriptions.Item label='Payment Amount'>
              {selectedBooking.payment?.currency || 'INR'} {selectedBooking.payment?.amount || 0}
            </Descriptions.Item>
            {selectedBooking.special_requests && selectedBooking.special_requests !== '' && (
              <Descriptions.Item label='Special Requests'>
                {selectedBooking.special_requests || 'Nil'}
              </Descriptions.Item>
            )}
            <Descriptions.Item label='Created At'>
              {dayjs(selectedBooking.createdAt).format('MMMM D, YYYY h:mm A')}
            </Descriptions.Item>
            {selectedBooking.cancellation?.reason && (
              <Descriptions.Item label='Cancellation Reason'>
                {selectedBooking.cancellation.reason}
              </Descriptions.Item>
            )}
            <Descriptions.Item label='Check-in Time'>
              {selectedBooking.check_in_time || '14:00'}
              {selectedBooking.check_in && selectedBooking.check_in.time && (
                <div>Actual: {selectedBooking.check_in.time}</div>
              )}
            </Descriptions.Item>
            <Descriptions.Item label='Check-out Time'>
              {selectedBooking.check_out_time || '12:00'}
              {selectedBooking.check_out && selectedBooking.check_out.time && (
                <div>Actual: {selectedBooking.check_out.time}</div>
              )}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Drawer>
    </div>
  );
}

export default BookingCalendar;
