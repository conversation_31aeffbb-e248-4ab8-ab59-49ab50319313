import { UserAddOutlined, TeamOutlined, UserSwitchOutlined } from '@ant-design/icons';
import { Button, Dropdown, Tabs } from 'antd';
import React, { useRef, useState } from 'react';
import CreateUser from '../users/CreateUser';
import CreateStaff from '../users/CreateStaff';
import UserDetails from '../users/UserDetails';
import UsersList from '../users/UsersList';
import StaffList from '../users/StaffList';
import notificationWithIcon from '../../utils/notification';

function Users() {
  // function to create new tab pane for user details
  const add = (id) => {
    const newActiveKey = `NewTab1${newTabIndex.current++}`;
    setItems([
      ...items,
      {
        key: newActiveKey,
        label: 'User Details',
        children: <UserDetails id={id} />
      }
    ]);
    setActiveKey(newActiveKey);
  };

  // function to create new tab pane create new user
  const addUser = () => {
    const newActiveKey = `NewTab2${newTabIndex.current++}`;
    setItems([
      ...items,
      {
        key: newActiveKey,
        label: 'Create User',
        children: <CreateUser />
      }
    ]);
    setActiveKey(newActiveKey);
  };

  // function to create new tab pane create new staff
  const addStaff = () => {
    const newActiveKey = `NewTab3${newTabIndex.current++}`;
    setItems([
      ...items,
      {
        key: newActiveKey,
        label: 'Create Staff',
        children: <CreateStaff />
      }
    ]);
    setActiveKey(newActiveKey);
  };

  // default tab pane and component
  const defaultPanes = [
    {
      key: '1',
      label: 'Users List',
      children: <UsersList add={add} />,
      closable: false
    },
    {
      key: '2',
      label: 'Staff List',
      children: <StaffList add={add} />,
      closable: false
    }
  ];

  const [activeKey, setActiveKey] = useState(defaultPanes[0].key);
  const [items, setItems] = useState(defaultPanes);
  const newTabIndex = useRef(0);

  // function to removed a tab pane
  const remove = (targetKey) => {
    const targetIndex = items.findIndex((pane) => pane.key === targetKey);
    const newPanes = items.filter((pane) => pane.key !== targetKey);
    if (newPanes.length && targetKey === activeKey) {
      const { key } = newPanes[targetIndex === newPanes.length ? targetIndex - 1 : targetIndex];
      setActiveKey(key);
    }
    setItems(newPanes);
  };

  // function to edit tab components
  const onEdit = (targetKey, action) => {
    if (action === 'add') {
      addUser();
    } else {
      remove(targetKey);
    }
  };

  // Create dropdown menu items
  const items2 = [
    {
      key: '1',
      label: 'Create User',
      icon: <UserAddOutlined />,
      onClick: addUser
    },
    {
      key: '2',
      label: 'Create Staff',
      icon: <TeamOutlined />,
      onClick: addStaff
    }
  ];

  return (
    <Tabs
      onChange={(key) => setActiveKey(key)}
      tabBarExtraContent={(
        <Dropdown menu={{ items: items2 }} placement="bottomRight">
          <Button
            className='inline-flex items-center'
            icon={<UserSwitchOutlined />}
            type='primary'
            size='large'
          >
            Create New
          </Button>
        </Dropdown>
      )}
      activeKey={activeKey}
      type='editable-card'
      onEdit={onEdit}
      items={items}
      size='large'
      hideAdd
    />
  );
}

export default React.memo(Users);
