import React, { useRef, useState } from 'react';
import { <PERSON><PERSON>, Card, Tabs } from 'antd';
import { PlusOutlined } from '@ant-design/icons';
import RoomsList from '../rooms/RoomsList';
import RoomEdit from '../rooms/RoomEdit';
import CreateRoom from '../rooms/CreateRoom';

function Rooms() {
  const newTabIndex = useRef(0);
  const [activeKey, setActiveKey] = useState('list');
  const [roomEditModal, setRoomEditModal] = useState({
    open: false,
    roomId: null
  });

  const handleEdit = (roomId) => {
    setRoomEditModal({
      open: true,
      roomId: roomId
    });
  };

  const [items, setItems] = useState([
    {
      key: 'list',
      label: 'Rooms List',
      children: <RoomsList onEdit={handleEdit} />,
      closable: false
    }
  ]);

  const addCreateTab = () => {
    const newActiveKey = `create-${newTabIndex.current++}`;
    setItems([...items, {
      key: newActiveKey,
      label: 'Create New Room',
      children: <CreateRoom onSuccess={() => {
        removeTab(newActiveKey);
        setActiveKey('list');
      }} />,
      closable: true
    }]);
    setActiveKey(newActiveKey);
  };

  const removeTab = (targetKey) => {
    const newItems = items.filter(item => item.key !== targetKey);
    setItems(newItems);
    
    if (targetKey === activeKey) {
      setActiveKey(newItems[newItems.length - 1].key);
    }
  };

  return (
    <div className="space-y-4">
      <Tabs
        activeKey={activeKey}
        onChange={setActiveKey}
        type="editable-card"
        onEdit={(targetKey, action) => {
          if (action === 'remove') {
            removeTab(targetKey);
          }
        }}
        tabBarExtraContent={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={addCreateTab}
            size="large"
          >
            Create New Room
          </Button>
        }
        items={items}
        hideAdd
      />
      <RoomEdit 
        roomEditModal={roomEditModal}
        setRoomEditModal={setRoomEditModal}
      />
    </div>
  );
}

export default Rooms;
